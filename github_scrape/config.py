#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub爬虫配置文件
包含GitHub API Token和其他配置参数
"""

import os

# GitHub API Token
# 建议设置环境变量 GITHUB_API_TOKEN
# 或者在此处直接设置 (不推荐)
GITHUB_TOKEN = os.environ.get("GITHUB_TOKEN", "")

# API请求错误后的重试间隔（秒）
RATE_LIMIT_WAIT = 60

# 每页结果数
RESULTS_PER_PAGE = 100

# 日志配置
LOG_DIR = "logs"
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = "github_scrape.log"  # 只是文件名，不包含路径

# 系统路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__)) 