import requests
import time
import logging
import json
import os
import concurrent.futures
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

# 添加项目根目录到sys.path
import sys
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入配置
from github_scrape.config import RATE_LIMIT_WAIT, GITHUB_TOKEN
# 导入处理器
from github_scrape.core.processor import DataProcessor

logger = logging.getLogger(__name__)

class GitHubScraper:
    """GitHub API交互类，用于抓取AI项目信息"""
    
    BASE_URL = "https://api.github.com"
    
    def __init__(self, token: str = GITHUB_TOKEN, output_dir: str = "data", max_workers: int = 5):
        # 使用配置中的token
        self.token = token
        logger.info(f"使用GitHub Token: {self.token[:4]}...{self.token[-4:]}")
        
        self.headers = {
            "Authorization": f"token {self.token}",
            "Accept": "application/vnd.github.v3+json"
        }
        self.output_dir = output_dir
        self.max_workers = max_workers
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, "repos"), exist_ok=True)
        
        # 记录已处理的仓库ID
        self.processed_repo_ids_file = os.path.join(output_dir, "processed_repo_ids.json")
        self.processed_repo_ids = self._load_processed_repo_ids()
        
        # 创建数据处理器
        self.processor = DataProcessor()
        
        # 记录上次更新时间
        self.last_update_file = os.path.join(output_dir, "last_update.json")
        self.last_update_time = self._load_last_update_time()
    
    def _load_processed_repo_ids(self) -> set:
        """加载已处理的仓库ID"""
        if os.path.exists(self.processed_repo_ids_file):
            try:
                with open(self.processed_repo_ids_file, 'r') as f:
                    return set(json.load(f))
            except Exception as e:
                logger.error(f"加载已处理仓库ID时出错: {str(e)}")
                return set()
        return set()
    
    def _save_processed_repo_ids(self) -> None:
        """保存已处理的仓库ID"""
        try:
            with open(self.processed_repo_ids_file, 'w') as f:
                json.dump(list(self.processed_repo_ids), f)
        except Exception as e:
            logger.error(f"保存已处理仓库ID时出错: {str(e)}")
    
    def _load_last_update_time(self) -> str:
        """加载上次更新时间"""
        if os.path.exists(self.last_update_file):
            try:
                with open(self.last_update_file, 'r') as f:
                    data = json.load(f)
                    return data.get("last_update", "")
            except Exception as e:
                logger.error(f"加载上次更新时间时出错: {str(e)}")
                return ""
        return ""
    
    def _save_last_update_time(self) -> None:
        """保存当前更新时间"""
        try:
            with open(self.last_update_file, 'w') as f:
                json.dump({"last_update": datetime.utcnow().isoformat()}, f)
        except Exception as e:
            logger.error(f"保存更新时间时出错: {str(e)}")
    
    def _save_repo_data(self, repo_data: Dict) -> None:
        """保存单个仓库数据到文件，使用仓库名作为文件名并按processor格式处理"""
        try:
            # 记录原始数据基本信息以便调试
            repo_id = repo_data.get("id")
            repo_name = repo_data.get("name", "unknown")
            logger.info(f"开始处理仓库数据: ID={repo_id}, 名称={repo_name}")
            
            # 尝试使用processor处理数据，添加更多错误捕获
            try:
                processed_data = self.processor.process_repository(repo_data)
                logger.info(f"处理器成功处理数据: {repo_name}")
            except Exception as process_error:
                logger.error(f"处理器处理数据失败: {str(process_error)}")
                # 如果处理器失败，我们使用原始数据
                processed_data = repo_data
                logger.info("将使用原始数据保存文件")
            
            # 确保文件名合法（移除特殊字符）
            safe_repo_name = "".join(c for c in repo_name if c.isalnum() or c in ['-', '_'])
            logger.info(f"使用安全文件名: {safe_repo_name}")
            
            # 构建文件路径并检查目录
            repos_dir = os.path.join(self.output_dir, "repos")
            if not os.path.exists(repos_dir):
                logger.info(f"创建目录: {repos_dir}")
                os.makedirs(repos_dir, exist_ok=True)
                
            repo_file = os.path.join(repos_dir, f"{safe_repo_name}.json")
            logger.info(f"准备写入文件: {repo_file}")
            
            # 如果存在同名文件，附加ID以避免冲突
            if os.path.exists(repo_file):
                logger.info(f"文件已存在，将附加ID: {repo_file}")
                repo_file = os.path.join(repos_dir, f"{safe_repo_name}_{repo_id}.json")
                logger.info(f"新文件名: {repo_file}")
            
            # 写入文件
            with open(repo_file, 'w', encoding='utf-8') as f:
                logger.info(f"写入数据到文件: {repo_file}")
                json.dump(processed_data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"成功保存仓库数据: {repo_file}")
            
            # 检查文件是否实际写入
            if os.path.exists(repo_file):
                file_size = os.path.getsize(repo_file)
                logger.info(f"确认文件已创建: {repo_file}, 大小: {file_size}字节")
            else:
                logger.error(f"文件似乎未创建: {repo_file}")
                
        except Exception as e:
            logger.error(f"保存仓库数据时出错 (ID: {repo_data.get('id')}, 名称: {repo_data.get('name')}): {str(e)}")
            # 打印详细错误信息和堆栈跟踪
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def _make_request(self, url: str, params: Optional[Dict] = None) -> Dict:
        """发送请求到GitHub API并处理速率限制"""
        if params is None:
            params = {}
            
        max_retries = 5
        retry_count = 0
        
        # 添加更多日志信息以便于调试
        logger.info(f"准备请求: {url}")
        logger.info(f"使用token: {self.token[:4]}...{self.token[-4:]}")
        logger.info(f"请求头: {self.headers}")
        
        while retry_count < max_retries:
            try:
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
                
                # 记录响应详情
                logger.info(f"响应状态码: {response.status_code}")
                if 'x-ratelimit-remaining' in response.headers:
                    logger.info(f"API剩余请求数: {response.headers['x-ratelimit-remaining']}")
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 422:
                    # 验证失败，通常是查询语法问题
                    error_message = response.json().get("message", "未知验证错误")
                    logger.warning(f"API验证失败 (422): {error_message}")
                    # 返回响应对象，让调用方处理错误
                    response.json_data = response.json()
                    return response
                elif response.status_code == 403 and 'x-ratelimit-remaining' in response.headers and int(response.headers['x-ratelimit-remaining']) == 0:
                    reset_time = int(response.headers['x-ratelimit-reset'])
                    sleep_time = reset_time - time.time() + 1
                    logger.warning(f"Rate limit exceeded. Waiting for {sleep_time} seconds")
                    time.sleep(max(sleep_time, RATE_LIMIT_WAIT))
                elif response.status_code == 404:
                    logger.warning(f"资源不存在: {url}")
                    return {}
                else:
                    logger.error(f"请求错误: {response.status_code} - {response.text}")
                    retry_count += 1
                    sleep_time = 2 ** retry_count  # 指数退避策略
                    logger.info(f"等待 {sleep_time} 秒后重试 ({retry_count}/{max_retries})")
                    time.sleep(sleep_time)
            except requests.exceptions.RequestException as e:
                logger.error(f"请求异常: {str(e)}")
                retry_count += 1
                sleep_time = 2 ** retry_count
                logger.info(f"等待 {sleep_time} 秒后重试 ({retry_count}/{max_retries})")
                time.sleep(sleep_time)
        
        logger.error(f"请求失败，已达到最大重试次数: {url}")
        return {}
    
    def search_ai_repositories(self, min_stars: int = 500, page: int = 1, per_page: int = 100) -> Dict:
        """搜索AI相关的GitHub仓库，将长查询拆分为多个简单独立的查询"""
        # 结果合并
        all_results = {"items": [], "total_count": 0}
        
        # 修改搜索顺序，先使用关键词搜索
        # 1. 通过单独关键词搜索
        self._search_by_single_keywords(all_results, min_stars, page, per_page)
        
        # 2. 通过热门AI主题搜索（每次只使用1-2个主题）
        self._search_by_popular_topics(all_results, min_stars, page, per_page)
        
        # 3. 搜索顶尖AI相关机构的项目
        self._search_by_organizations(all_results, min_stars, page, per_page)
        
        return all_results
    
    def _search_by_popular_topics(self, results: Dict, min_stars: int, page: int, per_page: int) -> None:
        """搜索主要AI框架和库"""
        frameworks = [
            "tensorflow",
            "pytorch", 
            "huggingface",
            "openai",
            "langchain",
            "scikit-learn",
            "keras",
            "fastai",
            "jax",
            "llamaindex",
            "autogen",
            "diffusers",
            "transformers",
            "haystack",
            "llama-cpp",
            "whisper",
            "anthropic",
            "milvus",
            "chroma",
            "faiss"
        ]
        
        for framework in frameworks:
            logger.info(f"执行框架搜索: {framework}")
            
            # 框架名称搜索 - 简单查询无布尔操作符
            query = f"stars:>={min_stars} {framework} in:name,description,readme"
            
            try:
                framework_results = self._execute_search(query, page, per_page)
                
                # 检查是否达到了星标下限，如果达到了就跳过当前框架，继续下一个
                if framework_results.get("reached_star_limit", False):
                    logger.info(f"框架 '{framework}' 搜索结果中存在低于最低星标({min_stars})的仓库，跳过此框架")
                    continue
                
                self._merge_results(results, framework_results, min_stars)
                logger.info(f"框架 '{framework}' 搜索结果: {len(framework_results.get('items', []))} 个仓库")
                
                # 立即处理搜索结果
                repos = framework_results.get("items", [])
                if repos:
                    logger.info(f"立即处理框架 '{framework}' 搜索到的 {len(repos)} 个仓库")
                    self._process_repos_batch(repos)
                
                time.sleep(1)
            except Exception as e:
                logger.error(f"框架搜索失败 - {framework}: {str(e)}")
    
    def _search_by_single_keywords(self, results: Dict, min_stars: int, page: int, per_page: int) -> None:
        """使用单独关键词搜索，无复杂布尔逻辑"""
        keywords = [
            # 核心AI关键词
            "artificial intelligence",
            "machine learning",
            "deep learning",
            "neural network",
            "language model",
            
            # 流行模型与框架
            "gpt",
            "llama",
            "mistral",
            "anthropic",
            "claude",
            "gemini",
            "chatgpt",
            "copilot",
            "falcon",
            "yi",
            "mixtral",
            "phi",
            "palm",
            "chinchilla",
            "cerebras",
            "vicuna",
            "alpaca",
            "koala",
            "dolly",
            "stable-diffusion",
            "midjourney",
            "dall-e",
            "imagen",
            "sora",
            "mcp",
            "llava", 
            "o1",
            "grok",
            "kimi",
            "qwen",
            "deepseek",
            "internlm",
            "segment anything",
            "sam",
            "foundation models",
            "moondream",
            "llm",
            "glm",
            "bert",
            "bart",
            "t5",
            "florence",
            "video generation",
            "image generation",
            "audio generation",
            
            # 模型能力
            "diffusion",
            "transformer",
            "fine-tuning",
            "rag",
            "chatbot",
            "vector database",
            "embeddings",
            "multimodal",
            "mamba",
            "state space model",
            "mixture of experts",
            "moe",
            "quantization",
            "rlhf",
            "reinforcement learning from human feedback",
            "constitutional ai",
            "synthetic data",
            "multi-modal",
            "multimodality",
            "multi modality",
            "model compression",
            "low rank adaptation",
            "lora",
            "qlora",
            "quantization",
            "4-bit",
            "8-bit",
            "pruning",
            "distillation",
            "knowledge distillation",
            "parameter efficient fine tuning",
            "peft",
            "dpo",
            "direct preference optimization",
            "model merging",
            "sft",
            "instruction tuning",
            "flash attention",
            
            # 具体技术与应用
            "text-to-image",
            "speech recognition",
            "voice assistant",
            "nlp",
            "computer vision",
            "reinforcement learning",
            "stable diffusion",
            "generative ai",
            "agent",
            "autonomous agent",
            "agentic workflow",
            "ai memory",
            "human-ai interaction",
            "text-to-speech",
            "speech-to-text",
            "text-to-video",
            "image-to-text",
            "image generation",
            "text generation",
            "code generation",
            "music generation",
            "audio generation",
            "3d generation",
            "ai agent framework",
            "multi-agent",
            "multi agent",
            "multi-agent system",
            "MAS",
            "agent swarm",
            "agent collaboration",
            "rag framework",
            "retrieval augmented generation",
            "hybrid search",
            "semantic search",
            "context window",
            "context length",
            "prompt chaining",
            "agentic ai",
            "function calling agent",
            "tool using agent",
            "agent orchestration",
            "automated reasoning",
            "chain of thought",
            "cot",
            "reflexion",
            "react framework",
            "text to 3d",
            "video to 3d",
            "dreamfusion",
            "shap-e",
            "3d gaussian splatting",
            "gaussian splatting",
            "nerf",
            "neural radiance fields",
            "talking head generation",
            "talking face generation",
            "visual reasoning",
            "visual question answering",
            "vqa",
            "text-to-sql",
            "text-to-api",
            "text-to-function",
            "function calling",
            "tool use",
            "tool learning",
            
            # AI工具和平台
            "ai assistant",
            "ai tool",
            "llm framework",
            "ai platform",
            "ai studio",
            "prompt engineering",
            "ai workflow",
            "ai automation",
            "ai api",
            "ai orchestration",
            "document ai",
            "ai pipeline",
            "ai ui",
            "chatgpt plugin",
            "ai integration",
            "ai monitoring",
            "ai explainer",
            "ai evaluation",
            "ai visualization",
            "knowledge graph",
            "prompt marketplace",
            "ai marketplace",
            "model marketplace",
            "model deployment",
            "serving infrastructure",
            "inference server",
            "model compression",
            "mcp for ai",
            "microsoft copilot platform",
            "microsoft copilot stack",
            "openai assistants",
            "discord bots",
            "telegram bots",
            "builder bot",
            "llamahub",
            "huggingface spaces",
            "huggingface inference api",
            "gradio",
            "streamlit",
            "vercel ai sdk",
            "langchain templates",
            "flowise",
            "langflow",
            "autogen studio",
            "local inference",
            "local llm",
            "vllm",
            "ollama",
            "lmstudio",
            "gputhreads",
            
            # 开发工具
            "ai coding assistant",
            "code generator",
            "developer tools",
            "code explainer",
            "ai debugging",
            "semantic search",
            "ai testing",
            "model deployment",
            "code interpreter",
            "code translation",
            "code generation",
            "ai pair programming",
            "codellama",
            "starcoder",
            "code completion",
            "deepsparse",
            "tensorrt",
            "onnx",
            "onnx runtime",
            "mlx",
            "pytorch",
            "tensorflow",
            "jax",
            "flax",
            "tinygrad",
            "mojo",
            "nemo",
            "deepspeed",
            "triton",
            
            # 新兴趋势与特定领域
            "ai agent",
            "ai reasoning",
            "in-context learning",
            "few-shot learning",
            "zero-shot learning",
            "meta learning",
            "transfer learning",
            "contrastive learning",
            "self-supervised learning",
            "federated learning",
            "edge ai",
            "tiny ml",
            "neuromorphic computing",
            "ai on chip",
            "ai ethics",
            "explainable ai",
            "responsible ai",
            "trustworthy ai",
            "ai safety",
            "ai alignment",
            "ai governance",
            "sovereign ai",
            "privacy preserving ai",
            "ai regulations",
            "decentralized ai",
            "opensource ai",
            "open models",
            "ai democratization",
            "embodied ai",
            "robotics ai",
            "autonomous systems",
            "self-driving",
            "drone ai",
            "robot learning",
            "synthetic data generation",
            "medical imaging ai",
            "healthcare ai",
            "legal ai",
            "finance ai",
            "ai for science",
            "ai for drug discovery",
            "ai for climate",
            "ai for sustainability",
            
            # 从processor.py提取的更多细分关键词
            # 模型开发与研究
            "model repository", "model zoo", "model hub", "pretrained models", "foundation models",
            "parameter efficient fine-tuning", "peft", "lora", "qlora", "adapter", "distributed training",
            "gradient accumulation", "mixed precision", "deepspeed", "accelerate", 
            "inference optimization", "quantization", "pruning", "distillation", "compression",
            "int8", "int4", "fp16", "bfloat16", "KV cache", "inference acceleration", "model compression",
            "hardware acceleration",
            "dataset engineering", "data augmentation", "data preprocessing", "data annotation", 
            "synthetic data", "data generation", "data cleaning", "data labeling",
            "vision-language", "vision transformer", "clip", "imagen", "3d generation", "audio processing",
            "speech synthesis", "multilingual", "cross-lingual", "language understanding", 
            "language detection", "instruction tuning", "alignment", 
            "code model", "code completion", "code understanding", "codegen", "code llm", 
            "programming assistant", "code interpreter", "code synthesis", "code translation",
            
            # AI工程化工具
            "langchain", "autogen", "crewai", "babyagi", "agentverse", "llamaindex", 
            "tool use", "function calling", "planning", "reasoning", "agent memory", "tool augmented",
            "ai interface", "chatbot interface", "ai interaction", "chat ui", "api wrapper",
            "chain of thought", "few-shot", "in-context learning", "prompt tuning", "zero-shot",
            "prompt optimization", "system prompt", "instruction engineering",
            "benchmark", "leaderboard", "performance testing", "precision", "recall", "f1 score",
            "human evaluation", "retrieval augmented generation", "document retrieval", "knowledge base",
            "document indexing", "context augmentation",
            
            # 实用应用
            "ide plugin", "vscode extension", "code review", "lint", "static analysis", "code documentation",
            "code search", "code suggestion", "code transformation",
            "information retrieval", "knowledge retrieval", "information extraction", "question answering",
            "document qa", "information synthesis", "summarization",
            "workflow automation", "task automation", "no-code", "low-code", "business process",
            "workflow engine", "automation platform", "robotic process automation",
            "conversational ai", "dialogue system", "customer service bot", "chat interface",
            "chat application", "chat agent",
            "midjourney", "dall-e", "gan", "diffusion model", "image editing", "image inpainting",
            "style transfer", "image synthesis", "generative art",
            "video synthesis", "motion generation", "animation", "video diffusion", "video transformation",
            "frame prediction", "content creation", "blog writer", "story generation", "storytelling",
            "copywriting", "article writer", "essay writing", "creative writing", "script generation",
            "data visualization", "data analysis", "data dashboard", "data exploration",
            
            # 基础设施与支持
            "embedding store", "vector store", "pinecone", "qdrant", "vector index", "semantic cache",
            "similarity search", "approximate nearest neighbors", "ann",
            "model serving", "inference server", "inference api", "serverless inference", "triton",
            "torchserve", "model hosting", "prediction service", "model endpoint", "service mesh",
            "compute management", "gpu management", "cluster management", "kubernetes", "docker",
            "containerization", "orchestration", "resource allocation", "cloud deployment",
            "infrastructure as code", "observability", "logging", "tracing", "prometheus", "grafana",
            "model monitoring", "performance tracking", "data drift", "concept drift", "dashboard",
            "ci/cd", "version control", "debugging", "profiling", "code quality", "testing framework",
            "development kit", "sdk", "api documentation",
            "data versioning", "data lineage", "data catalog", "dvc", "mlflow", "metadata management",
            "feature store", "data registry", "data governance", "data quality",
            
            # 教育与参考资源
            "tutorial", "guide", "course", "learning resource", "documentation", "handbook",
            "getting started", "best practices", "cookbook", "example notebooks", "workbook",
            "awesome list", "awesome repo", "curated list", "resource list", "toolkit",
            "reference implementation", "starter kit", "template collection", "boilerplate",
            "example", "demo", "sample code", "showcase", "demonstration", "case study",
            "proof of concept", "example project", "playground", "interactive demo", "sample application",
            
            # 技术细分领域
            "natural language understanding", "natural language generation", "sentiment analysis",
            "word embedding", "language representation", "text analysis",
            "object detection", "image classification", "segmentation", "face recognition", "pose estimation",
            "depth estimation", "cnn", "rnn", "lstm", "attention mechanism", "backpropagation",
            "gradient descent", "activation function", "classification", "regression", "clustering",
            "decision tree", "svm", "feature engineering", "model selection", "hyperparameter tuning",
            "ensemble methods", "q-learning", "dqn", "policy gradient", "reward function",
            "environment", "action space", "state space", "exploration"
        ]
        
        for keyword in keywords:
            logger.info(f"执行单一关键词搜索: '{keyword}'")
            
            # 单一关键词搜索 - 最简单的查询
            query = f"stars:>={min_stars} \"{keyword}\" in:description,readme"
            
            try:
                keyword_results = self._execute_search(query, page, per_page)
                
                # 检查是否达到了星标下限，如果达到了就跳过当前关键词，继续下一个
                if keyword_results.get("reached_star_limit", False):
                    logger.info(f"关键词 '{keyword}' 搜索结果中存在低于最低星标({min_stars})的仓库，跳过此关键词")
                    continue
                
                self._merge_results(results, keyword_results, min_stars)
                logger.info(f"关键词 '{keyword}' 搜索结果: {len(keyword_results.get('items', []))} 个仓库")
                
                # 立即处理搜索结果
                repos = keyword_results.get("items", [])
                if repos:
                    logger.info(f"立即处理关键词 '{keyword}' 搜索到的 {len(repos)} 个仓库")
                    self._process_repos_batch(repos)
                
                time.sleep(1)  # 短暂延迟避免触发速率限制
            except Exception as e:
                logger.error(f"关键词搜索失败 - {keyword}: {str(e)}")
    
    def _execute_search(self, query: str, page: int, per_page: int) -> Dict:
        """执行单个搜索查询"""
        url = f"{self.BASE_URL}/search/repositories"
        params = {
            "q": query,
            "sort": "stars",
            "order": "desc",
            "page": page,
            "per_page": per_page
        }
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                logger.info(f"执行搜索查询: {query} (第{retry_count+1}次尝试)")
                result = self._make_request(url, params)
                
                # 检查是否成功返回结果
                if result and "items" in result:
                    # 检查是否有低于最低星标的仓库（因为是逆序搜索，如果有一个低于，那么后面都会低于）
                    if result["items"]:
                        # 提取query中的min_stars值
                        min_stars_str = ""
                        for part in query.split():
                            if part.startswith("stars:>="):
                                min_stars_str = part[8:]
                                break
                        
                        if min_stars_str and result["items"][-1].get("stargazers_count", 0) < int(min_stars_str):
                            logger.info(f"搜索结果中存在低于星标要求的仓库，标记为达到星标限制")
                            result["reached_star_limit"] = True
                    
                    logger.info(f"搜索成功，找到 {len(result.get('items', []))} 个仓库")
                    return result
                else:
                    # 检查是否为422错误，这通常表示查询语法有问题
                    if getattr(result, 'status_code', 0) == 422:
                        logger.warning(f"查询语法错误 (422): {query}")
                        # 返回空结果，避免中断整个流程
                        return {"items": [], "total_count": 0}
                    
                    logger.warning(f"搜索结果无效，将重试: {query}")
                    retry_count += 1
                    time.sleep(5)
            except requests.exceptions.RequestException as e:
                logger.error(f"网络错误: {str(e)}")
                retry_count += 1
                time.sleep(5)
            except Exception as e:
                logger.error(f"搜索查询执行错误: {str(e)}")
                retry_count += 1
                time.sleep(5)
        
        logger.error(f"搜索查询'{query}'失败，达到最大重试次数")
        # 返回空结果，避免中断整个流程
        return {"items": [], "total_count": 0}
    
    def _merge_results(self, target: Dict, source: Dict, min_stars: int = 0) -> None:
        """合并搜索结果，去重，并过滤掉星标数低于指定值的仓库"""
        if not source or "items" not in source:
            return
            
        existing_ids = {item["id"] for item in target["items"]}
        
        # 遍历源结果，只合并星标数达到要求的仓库
        for item in source.get("items", []):
            if item["id"] not in existing_ids:
                # 只有星标数大于等于min_stars的仓库才会被合并
                if min_stars <= 0 or item.get("stargazers_count", 0) >= min_stars:
                    target["items"].append(item)
                    existing_ids.add(item["id"])
        
        # 更新总数
        target["total_count"] = len(target["items"])
    
    def get_repository_details(self, repo_full_name: str) -> Dict:
        """获取仓库的详细信息"""
        url = f"{self.BASE_URL}/repos/{repo_full_name}"
        return self._make_request(url)
    
    def get_repository_topics(self, repo_full_name: str) -> List[str]:
        """获取仓库的主题标签"""
        url = f"{self.BASE_URL}/repos/{repo_full_name}/topics"
        response = self._make_request(url)
        return response.get("names", [])
    
    def process_repository(self, repo: Dict, force_update: bool = False) -> Dict:
        """处理单个仓库获取详细信息，支持强制更新"""
        try:
            repo_id = repo.get("id")
            repo_full_name = repo.get("full_name")
            
            # 确保类型一致性
            if repo_id is not None:
                try:
                    repo_id = int(repo_id)
                except (ValueError, TypeError):
                    pass
            
            if not repo_id and repo_full_name:
                # 如果没有ID但有名称，尝试从现有数据中查找
                for existing_repo in self.get_existing_repos():
                    if existing_repo.get("full_name") == repo_full_name:
                        repo_id = existing_repo.get("repo_id")
                        logger.info(f"从现有数据中找到仓库ID: {repo_full_name} -> {repo_id}")
                        break
            
            # 检查是否要跳过处理
            if repo_id in self.processed_repo_ids and not force_update:
                logger.info(f"仓库已处理过，跳过: {repo_full_name}")
                return None
                
            try:
                logger.info(f"获取仓库详情: {repo_full_name}")
                details = self.get_repository_details(repo_full_name)
                if not details:
                    logger.warning(f"无法获取仓库详情: {repo_full_name}")
                    return None
                    
                topics = self.get_repository_topics(repo_full_name)
                details["topics"] = topics
                
                # 检查是否为更新
                is_update = False
                old_data = None
                
                if repo_id:
                    # 查找现有数据
                    for existing_repo in self.get_existing_repos():
                        existing_id = existing_repo.get("repo_id")
                        if existing_id and str(existing_id) == str(repo_id):
                            is_update = True
                            old_data = existing_repo
                            logger.info(f"发现现有数据，将更新 {repo_full_name}")
                            break
                
                # 如果是更新，合并数据
                if is_update and old_data:
                    # 保留一些现有字段
                    for key in ["history", "first_scraped"]:
                        if key in old_data:
                            details[key] = old_data[key]
                            
                    # 设置标记表示这是更新
                    details["is_update"] = True
                    details["repo_id"] = repo_id  # 确保ID一致
                
                # 记录已处理的仓库ID
                if repo_id is not None and repo_id not in self.processed_repo_ids:
                    self.processed_repo_ids.add(repo_id)
                    self._save_processed_repo_ids()
                
                # 保存仓库数据
                self._save_repo_data(details)
                
                logger.info(f"仓库{'更新' if is_update else '处理'}完成: {repo_full_name}")
                return details
            except Exception as e:
                logger.error(f"处理仓库时出错 {repo_full_name}: {str(e)}")
                return None
        except Exception as e:
            logger.error(f"处理仓库 {repo.get('full_name', 'unknown')} 时出现异常: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None
    
    def get_popular_ai_repos_directly(self) -> List[Dict]:
        """直接获取知名AI仓库，不依赖搜索功能"""
        popular_repos = [
            "huggingface/transformers",
            "openai/openai-cookbook",
            "microsoft/DeepSpeed",
            "tensorflow/tensorflow",
            "pytorch/pytorch", 
            "langchain-ai/langchain",
            "microsoft/onnxruntime",
            "google/jax",
            "meta-llama/llama",
            "AUTOMATIC1111/stable-diffusion-webui",
            "Lightning-AI/lightning",
            "scikit-learn/scikit-learn",
            "keras-team/keras",
            "fastai/fastai",
            "ray-project/ray"
        ]
        
        logger.info(f"尝试直接获取{len(popular_repos)}个流行的AI仓库")
        detailed_repos = []
        
        # 使用线程池处理仓库
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 准备仓库信息
            repo_info_list = []
            for repo_full_name in popular_repos:
                repo_info = {"full_name": repo_full_name}
                # 尝试从名称中提取ID（这里只是占位，实际处理时会获取正确的ID）
                repo_info["id"] = repo_full_name.replace("/", "_")
                repo_info_list.append(repo_info)
            
            # 并行处理仓库
            future_to_repo = {executor.submit(self.process_repository, repo): repo for repo in repo_info_list}
            for future in concurrent.futures.as_completed(future_to_repo):
                repo = future_to_repo[future]
                try:
                    details = future.result()
                    if details:
                        detailed_repos.append(details)
                except Exception as e:
                    logger.error(f"处理仓库时出错 {repo.get('full_name')}: {str(e)}")
        
        return detailed_repos
    
    def get_all_ai_repositories(self, min_stars: int = 500, max_pages: int = 50) -> List[Dict]:
        """批量抓取所有符合条件的AI仓库，支持断点续传和并行处理"""
        # 保存已处理仓库的详细信息
        detailed_repos = []
        
        # 尝试直接获取知名仓库
        popular_repos = self.get_popular_ai_repos_directly()
        detailed_repos.extend(popular_repos)
        
        # 使用搜索API批量获取更多仓库
        all_repos = []
        
        # 从多个页面获取搜索结果
        for page in range(1, max_pages + 1):
            try:
                logger.info(f"正在获取第{page}页搜索结果（每页100个）")
                response = self.search_ai_repositories(min_stars, page, 100)
                repos = response.get("items", [])
                
                if not repos:
                    logger.info("没有更多搜索结果")
                    break
                
                # 检查是否有低于最低星标的仓库
                star_limit_reached = False
                if repos:
                    # 检查最后一个仓库的星标数（按照stars:desc排序，最后一个应该是星数最少的）
                    last_repo = repos[-1]
                    if last_repo.get("stargazers_count", 0) < min_stars:
                        logger.info(f"搜索结果中包含星标数低于{min_stars}的仓库，停止获取下一页")
                        star_limit_reached = True
                    
                # 添加新的结果到总列表
                all_repos.extend(repos)
                
                logger.info(f"已获取 {len(all_repos)} 个仓库")
                
                # 立即处理每页的结果，不要等待积累
                logger.info(f"立即处理获取到的 {len(repos)} 个仓库")
                self._process_repos_batch(repos)
                
                # 检查是否需要停止获取下一页
                if star_limit_reached or len(repos) < 100:
                    if len(repos) < 100:
                        logger.info("已到达搜索结果最后一页")
                    break
                
                # 翻页并增加延迟以避免触发GitHub API速率限制
                logger.info(f"延迟3秒后获取下一页")
                time.sleep(3)
            except Exception as e:
                logger.error(f"搜索仓库时出错: {str(e)}")
                # 如果遇到错误，等待一段时间后继续
                logger.info("30秒后重试...")
                time.sleep(30)
                continue
        
        # 处理剩余仓库
        if all_repos:
            self._process_repos_batch(all_repos)
        
        logger.info(f"批量抓取完成，所有数据已保存到 {self.output_dir} 目录")
        
        # 返回已处理的仓库ID列表
        return list(self.processed_repo_ids)
    
    def _process_repos_batch(self, repos: List[Dict], force_update: bool = False) -> None:
        """并行处理一批仓库，支持强制更新"""
        logger.info(f"开始并行处理 {len(repos)} 个仓库")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 这里传递force_update参数
            future_to_repo = {executor.submit(self.process_repository, repo, force_update): repo for repo in repos}
            completed = 0
            
            for future in concurrent.futures.as_completed(future_to_repo):
                repo = future_to_repo[future]
                completed += 1
                
                try:
                    future.result()  # 获取结果，但我们不需要存储它，因为已经在process_repository中保存了
                except Exception as e:
                    logger.error(f"处理仓库时出错 {repo.get('full_name')}: {str(e)}")
                
                # 每处理10个仓库输出一次进度
                if completed % 10 == 0:
                    logger.info(f"进度: {completed}/{len(repos)}")
        
        logger.info(f"批处理完成，共处理 {len(repos)} 个仓库")
    
    def get_existing_repos(self) -> List[Dict]:
        """获取所有已存在的仓库数据"""
        repos_dir = os.path.join(self.output_dir, "repos")
        repos = []
        
        if not os.path.exists(repos_dir):
            logger.warning(f"仓库目录不存在: {repos_dir}")
            return repos
            
        for filename in os.listdir(repos_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(repos_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        repo_data = json.load(f)
                        
                        # 确保处理后的数据有正确的ID格式
                        if "repo_id" in repo_data and repo_data["repo_id"] is not None:
                            # 确保repo_id是整数类型
                            try:
                                repo_data["repo_id"] = int(repo_data["repo_id"])
                            except (ValueError, TypeError):
                                pass  # 如果转换失败，保持原样
                                
                        # 添加到列表
                        repos.append(repo_data)
                except Exception as e:
                    logger.error(f"读取仓库文件时出错 {file_path}: {str(e)}")
        
        logger.info(f"成功加载 {len(repos)} 个已存在的仓库数据")
        return repos
    
    def update_repositories(self, force_all: bool = False, min_stars: int = 500) -> None:
        """更新已有仓库信息并添加新的满足条件的仓库
        
        Args:
            force_all: 是否强制更新所有仓库，即使它们之前已处理过
            min_stars: 最低星标数要求，用于筛选新仓库
        """
        logger.info(f"开始更新仓库信息 (强制更新所有: {force_all}, 最低星标: {min_stars})")
        
        # 1. 更新现有仓库
        if force_all:
            # 强制更新所有已保存的仓库
            existing_repos = self.get_existing_repos()
            logger.info(f"准备强制更新 {len(existing_repos)} 个现有仓库")
            
            # 提取仓库基本信息用于更新
            repos_to_update = []
            for repo in existing_repos:
                repo_info = {
                    "id": repo.get("repo_id"),
                    "full_name": repo.get("full_name"),
                    "name": repo.get("name")
                }
                repos_to_update.append(repo_info)
            
            # 批量处理更新
            self._process_repos_batch(repos_to_update, force_update=True)
        else:
            # 只更新过期的仓库（可以根据上次更新时间或其他条件来决定）
            threshold_days = 30  # 定义多久算作过期，比如30天
            current_time = datetime.utcnow()
            
            # 加载所有现有仓库并检查更新时间
            existing_repos = self.get_existing_repos()
            repos_to_update = []
            
            for repo in existing_repos:
                # 检查上次更新时间
                last_scraped = repo.get("last_scraped", "")
                if not last_scraped:
                    # 如果没有记录更新时间，则更新
                    repos_to_update.append({
                        "id": repo.get("repo_id"),
                        "full_name": repo.get("full_name"),
                        "name": repo.get("name")
                    })
                    continue
                
                try:
                    # 解析上次更新时间
                    last_scraped_time = datetime.fromisoformat(last_scraped)
                    # 计算时间差
                    time_diff = current_time - last_scraped_time
                    # 如果超过阈值天数，则更新
                    if time_diff.days >= threshold_days:
                        repos_to_update.append({
                            "id": repo.get("repo_id"),
                            "full_name": repo.get("full_name"),
                            "name": repo.get("name")
                        })
                except Exception as e:
                    logger.error(f"解析更新时间出错 {repo.get('full_name')}: {str(e)}")
                    # 如果解析出错，也更新
                    repos_to_update.append({
                        "id": repo.get("repo_id"),
                        "full_name": repo.get("full_name"),
                        "name": repo.get("name")
                    })
            
            logger.info(f"需要更新的现有仓库: {len(repos_to_update)}/{len(existing_repos)}")
            
            # 批量处理需要更新的仓库
            if repos_to_update:
                self._process_repos_batch(repos_to_update, force_update=True)
        
        # 2. 获取新的符合条件的仓库
        logger.info(f"开始获取新的符合条件的仓库 (最低星标: {min_stars})")
        self.get_all_ai_repositories(min_stars=min_stars, max_pages=10)  # 限制页数以减少请求
        
        # 3. 更新最后更新时间
        self._save_last_update_time()
        logger.info("仓库更新完成")
    
    def batch_update(self, force_all: bool = False, min_stars: int = 100) -> None:
        """执行批量更新，可选是否强制更新所有仓库"""
        logger.info(f"开始批量更新GitHub AI项目 (强制更新所有: {force_all}, 最低星标: {min_stars})")
        self.update_repositories(force_all, min_stars)
    
    def _search_by_organizations(self, results: Dict, min_stars: int, page: int, per_page: int) -> None:
        """搜索AI相关顶尖机构的仓库"""
        # AI顶尖机构列表
        organizations = [
            # 大型科技公司
            "openai",
            "google",
            "google-research",
            "google-deepmind",
            "meta",
            "facebookresearch",
            "microsoft",
            "microsoft-research",
            "amazon-science",
            "amazon-web-services",
            "aws",
            "aws-samples",
            "ibm-research",
            "apple",
            "apple-machine-learning",
            "anthropic",
            "NVIDIA",
            "NVlabs",
            "nvidia-research",
            "intel-research",
            "intel-analytics",
            "intel-ai",
            "intel-isl",
            "baidu-research",
            "bytedance",
            "pytorch",
            "huggingface",
            "twitter",
            "twitter-research",
            "X-Company",
            "linkedin",
            "linkedin-research",
            "tencent",
            "tencent-ai-lab",
            "alibaba",
            "alipay",
            "ebay",
            "paypal",
            "netflix",
            "netflix-research",
            "uber",
            "uber-research",
            "airbnb",
            "salesforce",
            "salesforce-research",
            "oracle",
            "oracle-devrel",
            "oracle-samples",
            "oracle-ai",
            "adobe",
            "adobe-research",
            "sap",
            "sap-samples",
            "samsung-ai",
            "samsung-research",
            "vmware",
            "vmware-research",
            "ibm",
            "ibm-ai",
            "ibm-watson",
            "adobe",
            "spotify-research",
            "spotify",
            "snap-research",
            "snap",
            "snapchat",
            "dropbox",
            "shopify",
            "coinbase",
            "stripe",
            "stripe-engineering",
            "docker",
            "hashicorp",
            "gitlab",
            
            # 专业AI公司和新兴组织
            "cohere-ai",
            "together-ai",
            "databricks",
            "databrickslabs",
            "mistralai",
            "deepmind",
            "inflection-ai",
            "deeplearning-ai",
            "replit",
            "replit-research",
            "perplexity-ai",
            "modal-labs",
            "mosaic-ml",
            "forefront-ai",
            "anyscale",
            "ray-project",
            "elastic",
            "elasticsearch",
            "vectorinstitute",
            "deepfloyd",
            "claude-ai",
            "character-ai",
            "luma-ai",
            "runwayml",
            "deepset-ai",
            "pinecone-io",
            "chroma-core",
            "milvus-io",
            "qdrant",
            "rockset",
            "neo4j",
            "neo4j-graph-examples",
            "neo4j-contrib",
            "vespa-engine",
            "vespa-ai",
            "faiss-org",
            "autogpt",
            "auto-gpt-agents",
            "deeplearningexamples",
            "claude-ai",
            "cerebras",
            "graphcore",
            
            # 学术机构
            "mit-han-lab",
            "mit-ll",
            "harvard-nrg",
            "Princeton-NLP",
            "stanfordnlp",
            "stanfordmlgroup",
            "berkeley-nlp",
            "berkeley-ai-research",
            "oxford-robotics-institute",
            "cambridge-intelligence",
            "epfl-lts2",
            "eth-sri",
            "mlperf",
            "cmusatyalab",
            "carnegie-mellon-ai",
            "ucberkeley",
            "ucla-vision",
            "cambridge-mlg",
            "oxford-machine-learning",
            "mit-csail",
            "mit-ai",
            
            # 开源AI组织
            "allenai",
            "eleutherai",
            "stability-ai",
            "Lightning-AI",
            "replicate",
            "tensorflow",
            "pytorch",
            "jax-ml",
            "mlflow",
            "gradio-app",
            "LAION-AI",
            "langchain-ai",
            "langflow-ai",
            "flowhub-ai",
            "llamaindex-ai",
            "autogpts",
            "weights-and-biases",
            "ray-project"
        ]
        
        # 首先验证每个组织是否存在
        valid_orgs = []
        for org in organizations:
            # 尝试获取组织信息
            try:
                url = f"{self.BASE_URL}/orgs/{org}"
                response = requests.get(url, headers=self.headers)
                if response.status_code == 200:
                    valid_orgs.append(org)
                    logger.info(f"验证组织存在: {org}")
                else:
                    logger.warning(f"组织不存在或无法访问: {org}，状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"验证组织时发生错误 - {org}: {str(e)}")
            
            # 避免触发速率限制
            time.sleep(0.5)
        
        logger.info(f"验证完成，共有 {len(valid_orgs)}/{len(organizations)} 个有效组织")
        
        # 使用分批处理避免单个请求包含太多条件
        batch_size = 3
        org_batches = [valid_orgs[i:i+batch_size] for i in range(0, len(valid_orgs), batch_size)]
        logger.info(f"将组织分为 {len(org_batches)} 批进行处理")
        
        for batch_idx, org_batch in enumerate(org_batches):
            logger.info(f"处理组织批次 {batch_idx+1}/{len(org_batches)}: {', '.join(org_batch)}")
            
            for org in org_batch:
                logger.info(f"执行顶尖机构搜索: {org}")
                
                # 通过org参数直接搜索组织的仓库
                query = f"stars:>={min_stars} org:{org} sort:stars-desc"
                
                try:
                    org_results = self._execute_search(query, page, per_page)
                    
                    # 检查是否达到了星标下限
                    if org_results.get("reached_star_limit", False):
                        logger.info(f"机构 '{org}' 搜索结果中存在低于最低星标({min_stars})的仓库，跳过此机构")
                        continue
                    
                    self._merge_results(results, org_results, min_stars)
                    logger.info(f"机构 '{org}' 搜索结果: {len(org_results.get('items', []))} 个仓库")
                    
                    # 立即处理搜索结果
                    repos = org_results.get("items", [])
                    if repos:
                        logger.info(f"立即处理机构 '{org}' 搜索到的 {len(repos)} 个仓库")
                        self._process_repos_batch(repos)
                
                except requests.exceptions.RequestException as e:
                    logger.error(f"机构搜索网络错误 - {org}: {str(e)}")
                    time.sleep(5)  # 网络错误后等待更长时间
                    continue
                except Exception as e:
                    logger.error(f"机构搜索失败 - {org}: {str(e)}")
                    continue
                
                time.sleep(2)  # 增加延迟，避免触发速率限制
            
            # 批次处理完后等待更长时间
            logger.info(f"完成批次 {batch_idx+1}/{len(org_batches)}，休息5秒")
            time.sleep(5)
    
    def _search_new_popular_ai_repos(self, min_stars: int = 1000, days: int = 90) -> Dict:
        """搜索最近期间内新增的高星AI项目 - 精简版
        
        Args:
            min_stars: 最低星标数，默认1000
            days: 搜索最近多少天内创建或更新的仓库，默认90天
            
        Returns:
            包含搜索结果的字典
        """
        # 计算日期范围
        date_threshold = (datetime.utcnow() - timedelta(days=days)).strftime("%Y-%m-%d")
        logger.info(f"搜索 {date_threshold} 之后创建的 {min_stars}+ 星标AI项目 (精简模式)")
        
        # 精简的核心查询 - 只保留最有效的搜索策略
        queries = [
            # 核心AI主题
            f"stars:>={min_stars} topic:ai sort:stars-desc created:>={date_threshold}",
            f"stars:>={min_stars} topic:machine-learning sort:stars-desc created:>={date_threshold}",
            f"stars:>={min_stars} topic:deep-learning sort:stars-desc created:>={date_threshold}",
            f"stars:>={min_stars} topic:llm sort:stars-desc created:>={date_threshold}",
            
            # 热门关键词
            f"stars:>={min_stars} language:python ai in:name,description created:>={date_threshold}",
            f"stars:>={min_stars} language:python llm in:name,description created:>={date_threshold}",
            f"stars:>={min_stars} topic:chatgpt sort:stars-desc created:>={date_threshold}",
            f"stars:>={min_stars} topic:transformer sort:stars-desc created:>={date_threshold}",
            
            # 新兴技术
            f"stars:>={min_stars} topic:agents sort:stars-desc created:>={date_threshold}",
            f"stars:>={min_stars} topic:rag sort:stars-desc created:>={date_threshold}",
            f"stars:>={min_stars} topic:multimodal sort:stars-desc created:>={date_threshold}",
            f"stars:>={min_stars} generative-ai in:name,description,topics created:>={date_threshold}",
        ]
        
        # 结果合并
        all_results = {"items": [], "total_count": 0}
        
        # 记录新抓取的项目日志
        new_repos_log_file = os.path.join(self.output_dir, f"new_repos_{datetime.now().strftime('%Y%m%d')}.log")
        
        # 1. 执行核心查询
        logger.info(f"开始执行 {len(queries)} 个核心查询")
        for idx, query in enumerate(queries):
            logger.info(f"执行查询 ({idx+1}/{len(queries)}): {query}")
            try:
                query_results = self._execute_search(query, 1, 100)
                
                # 检查每个项目是否真的是在指定日期后创建的
                if 'items' in query_results:
                    verified_items = []
                    for item in query_results['items']:
                        created_at = item.get('created_at', '')
                        if created_at and created_at >= date_threshold:
                            verified_items.append(item)
                            # 记录新项目到日志
                            with open(new_repos_log_file, 'a', encoding='utf-8') as log_file:
                                repo_info = f"新项目: {item.get('full_name')} | 星标: {item.get('stargazers_count')} | 创建时间: {created_at} | 描述: {item.get('description', '无描述')[:100]}\n"
                                log_file.write(repo_info)
                        else:
                            logger.info(f"跳过项目 {item.get('full_name')} - 创建于 {created_at}，早于 {date_threshold}")
                    
                    # 使用验证过的项目替换原始结果
                    query_results['items'] = verified_items
                    query_results['total_count'] = len(verified_items)
                
                self._merge_results(all_results, query_results)
                logger.info(f"查询结果: 找到 {len(query_results.get('items', []))} 个符合日期要求的仓库")
                
                # 立即处理每个查询的结果
                repos = query_results.get("items", [])
                if repos:
                    logger.info(f"立即处理查询到的 {len(repos)} 个仓库")
                    self._process_repos_batch(repos)
                
                # 缩短延迟时间
                time.sleep(1)
            except Exception as e:
                logger.error(f"执行查询失败 - {query}: {str(e)}")
        
        # 2. 精简的顶级机构搜索
        logger.info(f"开始搜索核心AI机构的新项目 (created:>={date_threshold})")
        
        # 只保留最核心的顶级机构
        core_organizations = [
            # 顶级科技公司AI部门
            "openai",
            "google-research", 
            "google-deepmind",
            "facebookresearch",
            "microsoft-research",
            "anthropic",
            "huggingface",
            
            # 专业AI公司
            "mistralai",
            "stability-ai",
            "NVlabs",
            
            # 顶级学术机构
            "stanfordnlp",
            "allenai",
            "mit-han-lab",
            
            # 重要开源组织  
            "langchain-ai",
            "eleutherai",
        ]
        
        logger.info(f"搜索 {len(core_organizations)} 个核心机构")
        
        # 直接搜索，不再验证机构存在性以节省时间
        for idx, org in enumerate(core_organizations):
            logger.info(f"搜索机构 ({idx+1}/{len(core_organizations)}): {org}")
            
            # 通过org参数直接搜索组织的仓库，并添加日期限制
            query = f"stars:>={min_stars} org:{org} created:>={date_threshold} sort:stars-desc"
            
            try:
                org_results = self._execute_search(query, 1, 100)
                
                # 检查每个项目是否真的是在指定日期后创建的
                if 'items' in org_results:
                    verified_items = []
                    for item in org_results['items']:
                        created_at = item.get('created_at', '')
                        if created_at and created_at >= date_threshold:
                            verified_items.append(item)
                            # 记录新项目到日志
                            with open(new_repos_log_file, 'a', encoding='utf-8') as log_file:
                                repo_info = f"新机构项目: {item.get('full_name')} | 星标: {item.get('stargazers_count')} | 创建时间: {created_at} | 描述: {item.get('description', '无描述')[:100]}\n"
                                log_file.write(repo_info)
                        else:
                            logger.info(f"跳过机构项目 {item.get('full_name')} - 创建于 {created_at}，早于 {date_threshold}")
                    
                    # 使用验证过的项目替换原始结果
                    org_results['items'] = verified_items
                    org_results['total_count'] = len(verified_items)
                
                self._merge_results(all_results, org_results)
                logger.info(f"机构查询结果: 找到 {len(org_results.get('items', []))} 个符合日期要求的仓库")
                
                # 立即处理每个查询的结果
                repos = org_results.get("items", [])
                if repos:
                    logger.info(f"立即处理查询到的 {len(repos)} 个仓库")
                    self._process_repos_batch(repos)
            
            except requests.exceptions.RequestException as e:
                logger.error(f"机构搜索网络错误 - {org}: {str(e)}")
                time.sleep(2)  # 网络错误后等待短时间
                continue
            except Exception as e:
                logger.error(f"执行机构查询失败 - {org}: {str(e)}")
                continue
            
            # 缩短延迟时间
            time.sleep(1)
        
        # 记录总结到日志
        with open(new_repos_log_file, 'a', encoding='utf-8') as log_file:
            log_file.write(f"\n总计: 在 {date_threshold} 之后创建的 {min_stars}+ 星标项目 {len(all_results['items'])} 个\n")
            
        logger.info(f"精简搜索完成，共找到 {len(all_results['items'])} 个独特仓库")
        return all_results
    
    def get_new_high_starred_repos(self, min_stars: int = 1000, days: int = 90, max_workers: Optional[int] = None) -> List[Dict]:
        """专注获取新的高星AI项目
        
        Args:
            min_stars: 最低星标数，默认1000
            days: 搜索最近多少天内的仓库，默认90天
            max_workers: 最大并行工作线程数，若不指定则使用当前配置
            
        Returns:
            已处理的仓库列表
        """
        logger.info(f"开始专注抓取最近 {days} 天内新增的 {min_stars}+ 星标AI项目")
        
        # 临时提高并行度以加快处理
        original_max_workers = self.max_workers
        if max_workers:
            self.max_workers = max_workers
            logger.info(f"临时提高并行处理线程数至 {max_workers}")
        
        try:
            # 使用专门的搜索方法查询新项目
            all_results = self._search_new_popular_ai_repos(min_stars, days)
            repos = all_results.get("items", [])
            
            # 在搜索期间已经对每个查询结果进行了处理，这里只是确保不遗漏
            if repos:
                logger.info(f"对全部 {len(repos)} 个高星项目进行最终检查处理")
                self._process_repos_batch(repos)
            
            # 保存更新时间
            self._save_last_update_time()
            
            logger.info(f"高星项目抓取完成，共处理 {len(repos)} 个仓库")
            return repos
        finally:
            # 恢复原来的并行度
            if max_workers:
                self.max_workers = original_max_workers
                logger.info(f"已恢复并行处理线程数至 {original_max_workers}")
    
    def fast_update_high_starred_repos(self, min_stars: int = 1000):
        """执行快速更新，专注于高星项目"""
        logger.info(f"开始快速更新高星AI项目 (最低星标: {min_stars})")
        self.get_new_high_starred_repos(min_stars=min_stars, max_workers=10) 