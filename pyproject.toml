[project]
name = "camel-example"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiohttp>=3.11.14",
    "beautifulsoup4>=4.13.3",
    "camel-ai[all]>=0.2.68",
    "docling>=2.26.0",
    "duckduckgo-search>=6.4.2",
    "edge-tts>=7.0.0",
    "loguru>=0.7.3",
    "manim-voiceover>=0.3.7",
    "manim>=0.19.0",
    "markdownify>=1.1.0",
    "playwright>=1.51.0",
    "pre-commit>=3.8.0",
    "pyexcalidraw@git+https://github.com/RustingSword/pyexcalidraw.git",
    "pydantic>=2.11.3",
    "pocketflow>=0.0.2",
    "mistune>=3.1.3",
    "webdriver-manager>=4.0.2",
    "opencv-python>=*********",
]
[tool.uv]
override-dependencies = [
    "pandas>=2.1.4",
    "typer>=0.15.2",
    "pytest>=7,<8",
    "python-dotenv>=1.0.0,<2.0.0",
    "numpy>=1.26,<2.dev0"
]

[tool.ruff]
lint.ignore = ["E402", "F403", "F405", "E501"]
lint.select = ["E", "F", "I"]
line-length = 120
fix = true

[tool.ruff.lint.per-file-ignores]
"examples/generated/*.py" = ["F841"]

[dependency-groups]
dev = [
    "pytest>=7.4.4",
]
