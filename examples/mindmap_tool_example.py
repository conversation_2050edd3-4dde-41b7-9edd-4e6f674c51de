#!/usr/bin/env python3
"""
思维导图工具使用示例
演示如何使用新创建的思维导图工具生成animate_mindmap所需的配置
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from tools.enhance_tools.mindmap_tool import MindmapTool


def example_usage():
    """演示思维导图工具的使用方法"""
    print("🧠 思维导图工具使用示例")
    print("=" * 50)
    
    # 示例内容：适合生成思维导图的层次化内容
    sample_content = """
    深度学习技术体系全景

    深度学习是人工智能领域的重要分支，它通过模拟人脑神经网络的结构和功能来实现机器学习。

    ## 基础架构

    ### 神经网络基础
    神经网络是深度学习的基石：
    - 感知机：最简单的神经网络单元
    - 多层感知机：具有隐藏层的前馈网络
    - 激活函数：ReLU、Sigmoid、Tanh等
    - 反向传播：训练神经网络的核心算法

    ### 深度网络架构
    现代深度学习的主要架构：
    - 卷积神经网络（CNN）：专门处理图像数据
    - 循环神经网络（RNN）：处理序列数据
    - 长短期记忆网络（LSTM）：解决RNN的长期依赖问题
    - 门控循环单元（GRU）：LSTM的简化版本
    - Transformer：基于注意力机制的架构

    ## 应用领域

    ### 计算机视觉
    深度学习在视觉领域的应用：
    - 图像分类：识别图像中的主要对象
    - 目标检测：定位和识别图像中的多个对象
    - 语义分割：像素级别的图像理解
    - 人脸识别：身份验证和安全应用
    - 图像生成：GAN、VAE等生成模型

    ### 自然语言处理
    深度学习在NLP中的突破：
    - 词嵌入：Word2Vec、GloVe等词向量技术
    - 语言模型：GPT系列、BERT等预训练模型
    - 机器翻译：神经机器翻译系统
    - 文本生成：自动写作和对话系统
    - 情感分析：理解文本的情感倾向

    ### 语音技术
    深度学习在语音领域的应用：
    - 语音识别：将语音转换为文本
    - 语音合成：文本到语音的转换
    - 语音增强：噪声抑制和信号处理
    - 说话人识别：声纹识别技术

    ## 训练技术

    ### 优化算法
    深度学习的优化方法：
    - 梯度下降：基础优化算法
    - 随机梯度下降（SGD）：处理大数据集
    - Adam优化器：自适应学习率方法
    - 学习率调度：动态调整学习率

    ### 正则化技术
    防止过拟合的方法：
    - Dropout：随机丢弃神经元
    - 批量归一化：稳定训练过程
    - 数据增强：扩充训练数据
    - 早停法：防止过度训练

    ## 前沿发展

    ### 新兴架构
    最新的网络架构创新：
    - Vision Transformer：将Transformer应用于视觉
    - 图神经网络：处理图结构数据
    - 神经架构搜索：自动设计网络结构
    - 联邦学习：分布式隐私保护学习

    ### 应用创新
    深度学习的新应用方向：
    - 自动驾驶：端到端的驾驶决策
    - 药物发现：加速新药研发
    - 科学计算：物理仿真和预测
    - 创意生成：艺术创作和设计
    """
    
    print("📝 示例内容:")
    print(f"   内容长度: {len(sample_content)} 字符")
    print(f"   内容类型: 技术体系介绍（适合思维导图）")
    
    # 创建工具实例（模拟配置）
    mock_config = {
        "model": {
            "type": "openai/gpt-4o-mini",
            "api": {
                "openai_compatibility_api_key": "your_api_key_here",
                "openai_compatibility_api_base_url": "https://api.openai.com/v1"
            }
        },
        "material": {
            "material_enhance": {
                "mindmap_generation": True
            }
        }
    }
    
    print("\n🔧 创建思维导图工具:")
    tool = MindmapTool(mock_config)
    print(f"   工具名称: {tool.tool_name}")
    print(f"   工具描述: {tool.tool_description}")
    
    # 获取工具信息
    print("\n📋 工具信息:")
    tool_info = tool.get_tool_info()
    print(f"   分类: {tool_info['category']}")
    print(f"   适用场景: {len(tool_info['suitable_content'])} 种")
    print(f"   不适用场景: {len(tool_info['unsuitable_content'])} 种")
    
    # 检查可用性
    print("\n✅ 可用性检查:")
    can_apply = tool.can_apply(sample_content, "教学视频", {})
    print(f"   基本检查结果: {'通过' if can_apply else '未通过'}")
    
    if not can_apply:
        print("   ⚠️  注意: 由于缺少Camel模块或API密钥，工具无法完全运行")
        print("   💡 在实际使用中，请确保:")
        print("      - 安装camel-ai包: pip install camel-ai")
        print("      - 配置正确的API密钥")
    
    # 展示期望的输出格式
    print("\n📊 期望的输出格式:")
    expected_output = {
        "suitable": True,
        "reason": "内容包含清晰的层次结构，适合生成思维导图",
        "mindmap_data": {
            "标题": "深度学习技术体系",
            "子章节": [
                {
                    "标题": "基础架构",
                    "子章节": [
                        {"标题": "神经网络基础"},
                        {"标题": "深度网络架构"}
                    ]
                },
                {
                    "标题": "应用领域",
                    "子章节": [
                        {"标题": "计算机视觉"},
                        {"标题": "自然语言处理"},
                        {"标题": "语音技术"}
                    ]
                },
                {
                    "标题": "训练技术",
                    "子章节": [
                        {"标题": "优化算法"},
                        {"标题": "正则化技术"}
                    ]
                },
                {
                    "标题": "前沿发展",
                    "子章节": [
                        {"标题": "新兴架构"},
                        {"标题": "应用创新"}
                    ]
                }
            ]
        },
        "layout_style": "balance",
        "max_depth": 3,
        "focus_sequence": ["深度学习技术体系", "基础架构", "应用领域"],
        "narration": "让我们通过这个思维导图来了解深度学习的完整技术体系。",
        "metadata": {
            "total_nodes": 17,
            "max_depth": 3,
            "structure_quality": "结构清晰，层次分明"
        }
    }
    
    print(json.dumps(expected_output, ensure_ascii=False, indent=2))
    
    # 展示生成的animate_mindmap配置
    print("\n🎬 生成的animate_mindmap配置:")
    mindmap_config = {
        "type": "animate_mindmap",
        "params": {
            "data_source": expected_output["mindmap_data"],
            "layout_style": expected_output["layout_style"],
            "max_depth": expected_output["max_depth"],
            "focus_sequence": expected_output["focus_sequence"],
            "narration": expected_output["narration"],
            "id": "deep_learning_mindmap"
        }
    }
    
    print(json.dumps(mindmap_config, ensure_ascii=False, indent=2))
    
    print("\n💡 使用说明:")
    print("1. 将上述配置保存为JSON文件")
    print("2. 在DSL脚本中使用animate_mindmap函数")
    print("3. 系统将自动创建交互式思维导图动画")
    
    print("\n🎯 最佳实践:")
    print("- 确保内容具有清晰的层次结构")
    print("- 避免过深的嵌套（建议最多3层）")
    print("- 使用简洁明了的标题和分支名称")
    print("- 为不同类型的内容设计合适的聚焦序列")
    
    print("\n✨ 工具特点:")
    print("- 🤖 智能判断内容是否适合生成思维导图")
    print("- 🎨 自动提取层次结构和关键概念")
    print("- 📐 生成符合animate_mindmap.py要求的数据格式")
    print("- 🎬 支持自定义布局样式和聚焦序列")
    print("- 🔧 完全集成到素材扩充系统中")


if __name__ == "__main__":
    example_usage()
