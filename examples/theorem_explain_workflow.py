from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import sys
import asyncio
from typing import List, Dict, Optional, Union, Any

import yaml
from camel.agents import ChatAgent as CamelChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from loguru import logger
import re

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入定理解释相关代理
from agents.theorem_agents.scene_plan_agent import ScenePlanAgent
from agents.theorem_agents.vision_storyboard_agent import VisionStoryboardAgent
from agents.theorem_agents.technical_implementation_agent import TechnicalImplementationAgent
from agents.theorem_agents.animation_narration_agent import AnimationNarrationAgent
from agents.theorem_agents.code_generation_agent import CodeGenerationAgent

# 导入工具包
from tools.manim_toolkit import ManimToolkit
from tools.rag_toolkit import RAGToolkit
from utils.format import extract_xml

# 导入提示模板
from prompts.theorem_agents import (
    SCENE_PLAN_PROMPT,
    VISION_STORYBOARD_PROMPT,
    TECHNICAL_IMPLEMENTATION_PROMPT,
    ANIMATION_NARRATION_PROMPT,
    CODE_GENERATION_PROMPT,
    CODE_FIX_PROMPT
)

# 设置日志
log_file = f"output/theorem_workflow_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# Scene计划输出格式提示
SCENE_PLAN_FORMAT_PROMPT = """
<SCENE_OUTLINE>
场景1: [场景名称]
- [关键内容点1]
- [关键内容点2]
...

场景2: [场景名称]
- [关键内容点1]
- [关键内容点2]
...
</SCENE_OUTLINE>
"""

class TheoremExplainWorkflow:
    """
    定理解释视频生成工作流类
    
    实现完整的定理解释视频生成流程，包括：
    1. 场景规划代理生成整体场景大纲
    2. 视觉故事板代理生成具体视觉表现
    3. 技术实现代理生成技术实现计划
    4. 动画叙述代理生成旁白脚本
    5. 代码生成代理生成Manim代码
    6. 视频渲染执行和错误修复
    7. 视频评估和质量控制
    """
    
    def __init__(self, config_path="config/config.yaml"):
        """初始化工作流"""
        # 保存配置路径
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 初始化模型
        self.model = self._create_model()
        logger.info("模型初始化完成")
        
        # 初始化工具包
        self.toolkits = self._initialize_toolkits()
        logger.info("工具包初始化完成")
        
        # 初始化代理
        self.agents = self._initialize_agents()
        logger.info("代理初始化完成")
        
        # 会话ID - 每次执行生成一个新的会话ID
        self.session_id = str(datetime.datetime.now().strftime('%Y%m%d_%H%M%S'))
        
    def load_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path) as file:
                config = yaml.safe_load(file)
                
            # 设置配置属性
            self.model_config = config.get("model", {})
            self.file_config = config.get("files", {})
            self.agent_config = config.get("agents", {})
            self.manim_config = config.get("manim", {})
            
            # 加载工作流控制配置
            self.workflow_config = config.get("workflow", {})
            # 默认从头开始执行
            self.start_stage = self.workflow_config.get("start_stage", None)
            # 加载是否启用RAG和上下文学习
            self.enable_rag = self.workflow_config.get("enable_rag", True)
            self.enable_context_learning = self.workflow_config.get("enable_context_learning", True)
            self.enable_visual_fix = self.workflow_config.get("enable_visual_fix", True)
            
            # 设置各阶段的输出目录和文件
            self.output_dir = self.file_config.get("output_dir", "output")
            
            # 设置各阶段的输出文件路径
            self.stage_files = {
                "scene_plan": self.workflow_config.get("scene_plan_file", "output/{topic}/scene_outline.txt"),
                "vision_storyboard": self.workflow_config.get("vision_storyboard_dir", "output/{topic}/vision_storyboard"),
                "technical_implementation": self.workflow_config.get("technical_implementation_dir", "output/{topic}/technical_implementation"),
                "animation_narration": self.workflow_config.get("animation_narration_dir", "output/{topic}/animation_narration"),
                "code": self.workflow_config.get("code_dir", "output/{topic}/code"),
                "rendered_video": self.workflow_config.get("video_dir", "output/{topic}/videos"),
                "evaluation": self.workflow_config.get("evaluation_file", "output/{topic}/evaluation.json"),
            }
            
            logger.info("从 %s 加载配置", config_path)
        except Exception as e:
            logger.error(f"加载配置错误: {str(e)}")
            # 设置默认值
            self.model_config = {"type": "openai/gpt-4o-mini"}
            self.file_config = {"output_dir": "output"}
            self.agent_config = {}
            self.manim_config = {}
            self.workflow_config = {}
            self.start_stage = None
            self.stage_files = {}
            self.enable_rag = True
            self.enable_context_learning = True
            self.enable_visual_fix = True
            self.output_dir = "output"
            
    def _create_model(self):
        """创建模型实例"""
        # 从配置中获取API设置
        api_config = self.model_config.get("api", {})
        
        return ModelFactory.create(
            model_platform=ModelPlatformType.OPENROUTER,
            model_type=self.model_config.get("type", "openai/gpt-4o-mini"),
            api_key=api_config.get("openrouter_api_key"),
            url=api_config.get("openrouter_api_base_url"),
        )
        
    def _initialize_toolkits(self):
        """初始化各种工具包"""
        return {
            "manim": ManimToolkit(),
            "rag": RAGToolkit(
                chroma_db_path=self.workflow_config.get("chroma_db_path", "data/rag/chroma_db"),
                manim_docs_path=self.workflow_config.get("manim_docs_path", "data/rag/manim_docs"),
                embedding_model=self.workflow_config.get("embedding_model", "text-embedding-3-large")
            ) if self.enable_rag else None,
        }
        
    def _initialize_agents(self):
        """初始化所有需要的代理"""
        agents = {}
        
        # 场景规划代理
        agents["scene_plan"] = ScenePlanAgent(
            model=self.model,
            config_path=self.config_path
        )
        
        # 视觉故事板代理
        agents["vision_storyboard"] = VisionStoryboardAgent(
            model=self.model,
            config_path=self.config_path
        )
        
        # 技术实现代理
        agents["technical_implementation"] = TechnicalImplementationAgent(
            model=self.model,
            config_path=self.config_path
        )
        
        # 动画叙述代理
        agents["animation_narration"] = AnimationNarrationAgent(
            model=self.model,
            config_path=self.config_path
        )
        
        # 代码生成代理
        agents["code_generator"] = CodeGenerationAgent(
            model=self.model,
            config_path=self.config_path
        )
        
        # 代码修复代理
        agents["code_fixer"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Code Fixer",
                content="你是一位专业的Manim代码修复专家，善于识别和修复Manim代码中的错误，特别是布局、动画时序和空间约束问题。",
            ),
            model=self.model,
        )
        
        # 视觉反馈代理
        agents["visual_feedback"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Visual Feedback Agent",
                content="你是一位专业的视觉反馈专家，善于分析视频渲染结果，检测视觉问题并提供改进建议，特别是对元素位置、大小、层次结构和动画时序的优化。",
            ),
            model=self.model,
        )
        
        # 评估代理
        agents["evaluator"] = CamelChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Evaluation Agent",
                content="你是一位专业的教育视频评估专家，善于从教学效果、视觉表现、内容准确性和观众体验等角度评估定理讲解视频的质量。",
            ),
            model=self.model,
        )
        
        return agents 

    async def generate_scene_planning(self, topic, description) -> Dict[str, Any]:
        """生成场景规划"""
        try:
            # 获取场景规划代理
            scene_plan_agent = self.agents.get("scene_plan")
            if not scene_plan_agent:
                logger.error("Scene plan agent not found")
                return {"status": "error", "message": "Scene plan agent not found"}
            
            # 准备RAG上下文
            rag_context = None
            if self.workflow_config.get("enable_rag", False):
                rag_context = self.toolkits["rag"].search_relevant_context(
                    query=f"场景规划：{topic} {description}",
                    limit=5
                )
            
            # 生成场景规划
            scene_plan_result = scene_plan_agent.generate(
                topic=topic,
                description=description,
                rag_context=rag_context,
                session_id=self.session_id
            )
            
            # 保存场景规划结果
            if scene_plan_result.get("status") == "success":
                scene_plan = scene_plan_result.get("scene_outline", "")
                output_path = self.workflow_config.get("scene_plan_file", f"output/{topic.replace(' ', '_')}/scene_outline.txt")
                
                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # 保存场景规划到文件
                with open(output_path, "w", encoding="utf-8") as f:
                    f.write(scene_plan)
                
                logger.info(f"Scene plan saved to {output_path}")
                
                return {
                    "status": "success",
                    "scene_plan": scene_plan,
                    "file_path": output_path
                }
            else:
                return scene_plan_result
        except Exception as e:
            logger.error(f"Error generating scene planning: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_vision_storyboard(self, topic, description, scene_outline) -> Dict[str, Any]:
        """生成视觉故事板"""
        try:
            # 获取视觉故事板代理
            vision_agent = self.agents.get("vision_storyboard")
            if not vision_agent:
                logger.error("Vision storyboard agent not found")
                return {"status": "error", "message": "Vision storyboard agent not found"}
            
            # 解析场景大纲，提取场景数量
            scenes = self._parse_scene_outline(scene_outline)
            if not scenes:
                logger.error("Failed to parse scene outline")
                return {"status": "error", "message": "Failed to parse scene outline"}
            
            results = []
            for scene_number, scene_info in enumerate(scenes, 1):
                logger.info(f"Generating vision storyboard for scene {scene_number}")
                
                # 检测相关插件
                relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(
                    topic=topic,
                    description=f"{description} {scene_info}"
                ) if self.workflow_config.get("enable_rag", False) else ""
                
                # 准备RAG上下文
                rag_context = None
                if self.workflow_config.get("enable_rag", False):
                    rag_context = self.toolkits["rag"].search_relevant_context(
                        query=f"视觉故事板：{topic} {scene_info}",
                        limit=5
                    )
                
                # 生成视觉故事板
                vision_storyboard = await self._generate_vision_storyboard(
                    scene_number, topic, description, scene_info, relevant_plugins, f"{topic.replace(' ', '_')}_{scene_number}"
                )
                
                # 保存视觉故事板结果
                if vision_storyboard.get("status") == "success":
                    storyboard = vision_storyboard.get("vision_storyboard", "")
                    output_dir = self.workflow_config.get("vision_storyboard_dir", "output/{topic}/vision_storyboard")
                    output_dir = output_dir.format(topic=topic.replace(" ", "_"))
                    
                    # 确保输出目录存在
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # 保存视觉故事板到文件
                    output_path = os.path.join(output_dir, f"scene_{scene_number}_storyboard.txt")
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(storyboard)
                    
                    logger.info(f"Vision storyboard for scene {scene_number} saved to {output_path}")
                    
                    results.append({
                        "scene_number": scene_number,
                        "vision_storyboard": storyboard,
                        "file_path": output_path
                    })
                else:
                    logger.error(f"Failed to generate vision storyboard for scene {scene_number}: {vision_storyboard.get('message', 'Unknown error')}")
                    results.append({
                        "scene_number": scene_number,
                        "status": "error",
                        "message": vision_storyboard.get("message", "Unknown error")
                    })
            
            return {
                "status": "success",
                "results": results
            }
        except Exception as e:
            logger.error(f"Error generating vision storyboard: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_technical_implementation(self, topic, description, scene_outline, vision_storyboards) -> Dict[str, Any]:
        """生成技术实现计划"""
        try:
            # 获取技术实现代理
            tech_agent = self.agents.get("technical_implementation")
            if not tech_agent:
                logger.error("Technical implementation agent not found")
                return {"status": "error", "message": "Technical implementation agent not found"}
            
            results = []
            for storyboard_info in vision_storyboards:
                scene_number = storyboard_info.get("scene_number")
                vision_storyboard = storyboard_info.get("vision_storyboard")
                
                if not vision_storyboard:
                    logger.error(f"Vision storyboard for scene {scene_number} not found")
                    continue
                
                logger.info(f"Generating technical implementation for scene {scene_number}")
                
                # 提取当前场景的大纲
                scene_info = self._get_scene_by_number(scene_outline, scene_number)
                if not scene_info:
                    logger.error(f"Scene outline for scene {scene_number} not found")
                    continue
                
                # 检测相关插件
                relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(
                    topic=topic,
                    description=f"{description} {scene_info} {vision_storyboard}"
                ) if self.workflow_config.get("enable_rag", False) else ""
                
                # 准备RAG上下文
                rag_context = None
                if self.workflow_config.get("enable_rag", False):
                    rag_context = self.toolkits["rag"].search_relevant_context(
                        query=f"技术实现：{topic} {scene_info} {vision_storyboard[:200]}",
                        limit=5
                    )
                
                # 生成技术实现计划
                technical_implementation = await tech_agent.generate(
                    topic=topic,
                    description=description,
                    scene_number=scene_number,
                    scene_outline=scene_info,
                    vision_storyboard=vision_storyboard,
                    relevant_plugins=relevant_plugins,
                    session_id=self.session_id
                )
                
                # 保存技术实现计划结果
                if technical_implementation.get("status") == "success":
                    implementation = technical_implementation.get("technical_implementation", "")
                    output_dir = self.workflow_config.get("technical_implementation_dir", "output/{topic}/technical_implementation")
                    output_dir = output_dir.format(topic=topic.replace(" ", "_"))
                    
                    # 确保输出目录存在
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # 保存技术实现计划到文件
                    output_path = os.path.join(output_dir, f"scene_{scene_number}_implementation.txt")
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(implementation)
                    
                    logger.info(f"Technical implementation for scene {scene_number} saved to {output_path}")
                    
                    results.append({
                        "scene_number": scene_number,
                        "technical_implementation": implementation,
                        "file_path": output_path
                    })
                else:
                    logger.error(f"Failed to generate technical implementation for scene {scene_number}: {technical_implementation.get('message', 'Unknown error')}")
                    results.append({
                        "scene_number": scene_number,
                        "status": "error",
                        "message": technical_implementation.get("message", "Unknown error")
                    })
            
            return {
                "status": "success",
                "results": results
            }
        except Exception as e:
            logger.error(f"Error generating technical implementation: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_animation_narration(self, topic, description, scene_outline, vision_storyboards, technical_implementations) -> Dict[str, Any]:
        """生成动画叙述"""
        try:
            # 获取动画叙述代理
            narration_agent = self.agents.get("animation_narration")
            if not narration_agent:
                logger.error("Animation narration agent not found")
                return {"status": "error", "message": "Animation narration agent not found"}
            
            results = []
            for tech_info in technical_implementations:
                scene_number = tech_info.get("scene_number")
                technical_implementation = tech_info.get("technical_implementation")
                
                if not technical_implementation:
                    logger.error(f"Technical implementation for scene {scene_number} not found")
                    continue
                
                # 获取对应的视觉故事板
                vision_storyboard = self._get_storyboard_by_scene_number(vision_storyboards, scene_number)
                if not vision_storyboard:
                    logger.error(f"Vision storyboard for scene {scene_number} not found")
                    continue
                
                logger.info(f"Generating animation narration for scene {scene_number}")
                
                # 提取当前场景的大纲
                scene_info = self._get_scene_by_number(scene_outline, scene_number)
                if not scene_info:
                    logger.error(f"Scene outline for scene {scene_number} not found")
                    continue
                
                # 检测相关插件
                relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(
                    topic=topic,
                    description=f"{description} {scene_info} {vision_storyboard}"
                ) if self.workflow_config.get("enable_rag", False) else ""
                
                # 准备RAG上下文
                rag_context = None
                if self.workflow_config.get("enable_rag", False):
                    rag_context = self.toolkits["rag"].search_relevant_context(
                        query=f"动画叙述：{topic} {scene_info} {vision_storyboard[:100]} {technical_implementation[:100]}",
                        limit=5
                    )
                
                # 生成动画叙述
                animation_narration = await narration_agent.generate(
                    topic=topic,
                    description=description,
                    scene_number=scene_number,
                    scene_outline=scene_info,
                    vision_storyboard=vision_storyboard,
                    technical_implementation=technical_implementation,
                    relevant_plugins=relevant_plugins,
                    session_id=self.session_id
                )
                
                # 保存动画叙述结果
                if animation_narration.get("status") == "success":
                    narration = animation_narration.get("animation_narration", "")
                    output_dir = self.workflow_config.get("animation_narration_dir", "output/{topic}/animation_narration")
                    output_dir = output_dir.format(topic=topic.replace(" ", "_"))
                    
                    # 确保输出目录存在
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # 保存动画叙述到文件
                    output_path = os.path.join(output_dir, f"scene_{scene_number}_narration.txt")
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(narration)
                    
                    logger.info(f"Animation narration for scene {scene_number} saved to {output_path}")
                    
                    results.append({
                        "scene_number": scene_number,
                        "animation_narration": narration,
                        "file_path": output_path
                    })
                else:
                    logger.error(f"Failed to generate animation narration for scene {scene_number}: {animation_narration.get('message', 'Unknown error')}")
                    results.append({
                        "scene_number": scene_number,
                        "status": "error",
                        "message": animation_narration.get("message", "Unknown error")
                    })
            
            return {
                "status": "success",
                "results": results
            }
        except Exception as e:
            logger.error(f"Error generating animation narration: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_code(self, topic, description, technical_implementations, animation_narrations) -> Dict[str, Any]:
        """生成代码"""
        try:
            # 获取代码生成代理
            code_agent = self.agents.get("code_generator")
            if not code_agent:
                logger.error("Code generation agent not found")
                return {"status": "error", "message": "Code generation agent not found"}
            
            results = []
            for narration_info in animation_narrations:
                scene_number = narration_info.get("scene_number")
                animation_narration = narration_info.get("animation_narration")
                
                if not animation_narration:
                    logger.error(f"Animation narration for scene {scene_number} not found")
                    continue
                
                # 获取对应的技术实现计划
                technical_implementation = self._get_implementation_by_scene_number(technical_implementations, scene_number)
                if not technical_implementation:
                    logger.error(f"Technical implementation for scene {scene_number} not found")
                    continue
                
                logger.info(f"Generating code for scene {scene_number}")
                
                # 检测相关插件
                relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(
                    topic=topic,
                    description=f"{description} {technical_implementation[:100]} {animation_narration[:100]}"
                ) if self.workflow_config.get("enable_rag", False) else ""
                
                # 准备RAG上下文
                rag_context = None
                if self.workflow_config.get("enable_rag", False):
                    rag_context = self.toolkits["rag"].search_relevant_context(
                        query=f"Manim代码生成：{topic} {technical_implementation[:100]} {animation_narration[:100]}",
                        limit=5
                    )
                
                # 生成代码
                code_result = await code_agent.generate(
                    topic=topic,
                    description=description,
                    scene_number=scene_number,
                    technical_implementation=technical_implementation,
                    animation_narration=animation_narration,
                    session_id=self.session_id
                )
                
                # 保存代码结果
                if code_result.get("status") == "success":
                    code = code_result.get("code", "")
                    output_dir = self.workflow_config.get("code_dir", "output/{topic}/code")
                    output_dir = output_dir.format(topic=topic.replace(" ", "_"))
                    
                    # 确保输出目录存在
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # 保存代码到文件
                    output_path = os.path.join(output_dir, f"scene_{scene_number}.py")
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(code)
                    
                    logger.info(f"Code for scene {scene_number} saved to {output_path}")
                    
                    results.append({
                        "scene_number": scene_number,
                        "code": code,
                        "file_path": output_path
                    })
                else:
                    logger.error(f"Failed to generate code for scene {scene_number}: {code_result.get('message', 'Unknown error')}")
                    results.append({
                        "scene_number": scene_number,
                        "status": "error",
                        "message": code_result.get("message", "Unknown error")
                    })
            
            return {
                "status": "success",
                "results": results
            }
        except Exception as e:
            logger.error(f"Error generating code: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_scene_implementation(self, topic: str, description: str, scene_plan: str) -> Dict[int, Dict[str, str]]:
        """生成场景实现计划，包括视觉故事板、技术实现和动画叙述"""
        logger.info(f"开始生成场景实现计划: {topic}")
        
        scene_implementations = {}
        
        # 从scene_plan字符串中提取场景
        scenes = self._parse_scene_outline(scene_plan)
        
        # 检测需要使用的插件
        relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(
            topic=topic,
            description=description
        ) if self.workflow_config.get("enable_rag", False) else []
        
        # 为每个场景并行生成实现计划
        implementation_tasks = []
        
        for scene_number, scene_outline_i in enumerate(scenes, 1):
            implementation_tasks.append(
                self._generate_scene_implementation_single(
                    topic=topic,
                    description=description,
                    scene_outline_i=scene_outline_i,
                    scene_number=scene_number,
                    file_prefix=topic.replace(" ", "_"),
                    relevant_plugins=relevant_plugins
                )
            )
        
        # 并行执行所有场景的实现计划生成
        implementations = await asyncio.gather(*implementation_tasks)
        
        # 整理结果
        for scene_number, implementation in enumerate(implementations, 1):
            scene_implementations[scene_number] = implementation
        
        return scene_implementations
    
    async def _generate_scene_implementation_single(
        self, 
        topic: str, 
        description: str, 
        scene_outline_i: str, 
        scene_number: int, 
        file_prefix: str, 
        relevant_plugins: List[str]
    ) -> Dict[str, str]:
        """
        为单个场景生成实现计划
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_outline_i: 当前场景大纲
            scene_number: 场景编号
            file_prefix: 文件前缀
            relevant_plugins: 相关插件列表
            
        Returns:
            Dict: 包含视觉故事板、技术实现和动画叙述的字典
        """
        # 创建场景目录
        scene_dir = os.path.join(self.output_dir, file_prefix, f"scene{scene_number}")
        os.makedirs(scene_dir, exist_ok=True)
        
        # 定义文件路径
        vision_path = os.path.join(scene_dir, f"scene{scene_number}_vision_storyboard.txt")
        technical_path = os.path.join(scene_dir, f"scene{scene_number}_technical_implementation.txt")
        narration_path = os.path.join(scene_dir, f"scene{scene_number}_animation_narration.txt")
        
        # 检查是否加载现有结果
        if self.start_stage and self.start_stage not in ["scene_plan", "vision_storyboard"]:
            if os.path.exists(vision_path) and os.path.exists(technical_path) and os.path.exists(narration_path):
                try:
                    with open(vision_path, 'r') as f:
                        vision_storyboard = f.read()
                    with open(technical_path, 'r') as f:
                        technical_implementation = f.read()
                    with open(narration_path, 'r') as f:
                        animation_narration = f.read()
                    logger.info(f"已加载场景{scene_number}的实现计划")
                    return {
                        "vision_storyboard": vision_storyboard,
                        "technical_implementation": technical_implementation,
                        "animation_narration": animation_narration
                    }
                except Exception as e:
                    logger.error(f"加载场景{scene_number}实现计划失败: {str(e)}")
        
        # 1. 生成视觉故事板
        vision_storyboard = await self._generate_vision_storyboard(
            scene_number, topic, description, scene_outline_i, relevant_plugins, file_prefix
        )
        
        # 保存视觉故事板
        with open(vision_path, 'w') as f:
            f.write(vision_storyboard)
        logger.info(f"视觉故事板已保存到: {vision_path}")
        
        # 2. 生成技术实现计划
        technical_implementation = await self._generate_technical_implementation(
            scene_number, topic, description, scene_outline_i, vision_storyboard, relevant_plugins, file_prefix
        )
        
        # 保存技术实现计划
        with open(technical_path, 'w') as f:
            f.write(technical_implementation)
        logger.info(f"技术实现计划已保存到: {technical_path}")
        
        # 3. 生成动画叙述
        animation_narration = await self._generate_animation_narration(
            scene_number, topic, description, scene_outline_i, vision_storyboard, technical_implementation, relevant_plugins, file_prefix
        )
        
        # 保存动画叙述
        with open(narration_path, 'w') as f:
            f.write(animation_narration)
        logger.info(f"动画叙述已保存到: {narration_path}")
        
        return {
            "vision_storyboard": vision_storyboard,
            "technical_implementation": technical_implementation,
            "animation_narration": animation_narration
        }
        
    async def _generate_vision_storyboard(
        self,
        scene_number: int,
        topic: str,
        description: str,
        scene_outline: str,
        relevant_plugins: List[str],
        file_prefix: str
    ) -> str:
        """
        生成视觉故事板
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            file_prefix: 文件前缀
            
        Returns:
            str: 生成的视觉故事板
        """
        logger.info(f"生成场景{scene_number}的视觉故事板")
        
        # 获取视觉故事板代理
        vision_agent = self.agents["vision_storyboard"]
        
        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            # 为视觉故事板生成查询
            queries = self.toolkits["rag"].generate_queries_for_vision_storyboard(
                scene_outline, relevant_plugins, topic, scene_number, self.session_id
            )
            # 使用生成的查询获取上下文
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
        
        # 生成视觉故事板
        vision_result = await vision_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            scene_outline=scene_outline,
            relevant_plugins=relevant_plugins,
            session_id=self.session_id
        )
        
        # 检查生成结果是否为字典类型
        if isinstance(vision_result, dict):
            vision_storyboard = vision_result.get("vision_storyboard", "")
            # 如果vision_storyboard为空但有status为success，提取其他可能有用的字段
            if not vision_storyboard and vision_result.get("status") == "success":
                # 尝试将字典转换为可读字符串
                import json
                vision_storyboard = json.dumps(vision_result, ensure_ascii=False, indent=2)
            elif vision_result.get("status") == "error":
                # 如果是错误状态，记录错误并返回错误消息
                error_msg = vision_result.get("message", "生成视觉故事板出错")
                logger.error(f"生成视觉故事板失败: {error_msg}")
                return f"<SCENE_VISION_STORYBOARD>\n生成失败: {error_msg}\n</SCENE_VISION_STORYBOARD>"
        else:
            vision_storyboard = vision_result
        
        # 提取标签中的内容
        vision_match = extract_xml(vision_storyboard, "SCENE_VISION_STORYBOARD")
        return vision_match if vision_match else vision_storyboard
        
    async def _generate_technical_implementation(
        self,
        scene_number: int,
        topic: str,
        description: str,
        scene_outline: str,
        vision_storyboard: str,
        relevant_plugins: List[str],
        file_prefix: str
    ) -> str:
        """
        生成技术实现计划
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            file_prefix: 文件前缀
            
        Returns:
            str: 生成的技术实现计划
        """
        logger.info(f"生成场景{scene_number}的技术实现计划")
        
        # 获取技术实现代理
        tech_agent = self.agents["technical_implementation"]
        
        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            # 为技术实现生成查询
            queries = self.toolkits["rag"].generate_queries_for_technical(
                vision_storyboard, relevant_plugins, topic, scene_number, self.session_id
            )
            # 使用生成的查询获取上下文
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
        
        # 生成技术实现计划
        tech_result = await tech_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            relevant_plugins=relevant_plugins,
            session_id=self.session_id
        )
        
        # 检查生成结果是否为字典类型
        if isinstance(tech_result, dict):
            technical_implementation = tech_result.get("technical_implementation", "")
            # 如果technical_implementation为空但有status为success，提取其他可能有用的字段
            if not technical_implementation and tech_result.get("status") == "success":
                # 尝试将字典转换为可读字符串
                import json
                technical_implementation = json.dumps(tech_result, ensure_ascii=False, indent=2)
            elif tech_result.get("status") == "error":
                # 如果是错误状态，记录错误并返回错误消息
                error_msg = tech_result.get("message", "生成技术实现计划出错")
                logger.error(f"生成技术实现计划失败: {error_msg}")
                return f"<TECHNICAL_IMPLEMENTATION>\n生成失败: {error_msg}\n</TECHNICAL_IMPLEMENTATION>"
        else:
            technical_implementation = tech_result
        
        # 提取标签中的内容
        tech_match = extract_xml(technical_implementation, "TECHNICAL_IMPLEMENTATION")
        return tech_match if tech_match else technical_implementation
        
    async def _generate_animation_narration(
        self,
        scene_number: int,
        topic: str,
        description: str,
        scene_outline: str,
        vision_storyboard: str,
        technical_implementation: str,
        relevant_plugins: List[str],
        file_prefix: str
    ) -> str:
        """
        生成动画旁白
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            technical_implementation: 技术实现计划
            relevant_plugins: 相关插件列表
            file_prefix: 文件前缀
            
        Returns:
            str: 生成的动画旁白
        """
        logger.info(f"生成场景{scene_number}的动画旁白")
        
        # 获取动画叙述代理
        narration_agent = self.agents["animation_narration"]
        
        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            # 为动画叙述生成查询
            queries = self.toolkits["rag"].generate_queries_for_narration(
                vision_storyboard, relevant_plugins, topic, scene_number, self.session_id
            )
            # 使用生成的查询获取上下文
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
        
        # 生成动画叙述
        narration_result = await narration_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            technical_implementation=technical_implementation,
            relevant_plugins=relevant_plugins,
            session_id=self.session_id
        )
        
        # 检查生成结果是否为字典类型
        if isinstance(narration_result, dict):
            animation_narration = narration_result.get("animation_narration", "")
            # 如果animation_narration为空但有status为success，提取其他可能有用的字段
            if not animation_narration and narration_result.get("status") == "success":
                # 尝试将字典转换为可读字符串
                import json
                animation_narration = json.dumps(narration_result, ensure_ascii=False, indent=2)
            elif narration_result.get("status") == "error":
                # 如果是错误状态，记录错误并返回错误消息
                error_msg = narration_result.get("message", "生成动画叙述出错")
                logger.error(f"生成动画叙述失败: {error_msg}")
                return f"<ANIMATION_NARRATION>\n生成失败: {error_msg}\n</ANIMATION_NARRATION>"
        else:
            animation_narration = narration_result
        
        # 提取标签中的内容
        narration_match = extract_xml(animation_narration, "ANIMATION_NARRATION")
        return narration_match if narration_match else animation_narration
    
    async def generate_code_and_render(
        self, 
        topic: str, 
        description: str, 
        scene_implementations: Dict[int, Dict[str, str]]
    ) -> Dict[int, Dict[str, Any]]:
        """
        生成Manim代码并渲染视频
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_implementations: 场景实现计划
            
        Returns:
            Dict: 场景编号到渲染结果的映射
        """
        logger.info(f"开始生成代码并渲染视频: {topic}")
        
        file_prefix = topic.lower().replace(' ', '_').replace('-', '_')
        scene_results = {}
        
        # 检测需要使用的插件
        relevant_plugins = []
        if self.enable_rag and self.toolkits["rag"]:
            relevant_plugins = self.toolkits["rag"].detect_relevant_plugins(topic, description)
        
        # 为每个场景生成代码和渲染视频
        for scene_number, implementation in scene_implementations.items():
            scene_result = await self._process_scene(
                topic=topic,
                description=description,
                scene_number=scene_number,
                scene_outline=implementation.get("scene_outline", ""),
                scene_implementation=implementation,
                file_prefix=file_prefix,
                relevant_plugins=relevant_plugins
            )
            scene_results[scene_number] = scene_result
            
        return scene_results
    
    async def _process_scene(
        self,
        topic: str,
        description: str,
        scene_number: int,
        scene_outline: str,
        scene_implementation: Dict[str, str],
        file_prefix: str,
        relevant_plugins: List[str]
    ) -> Dict[str, Any]:
        """
        处理单个场景的代码生成和渲染
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_number: 场景编号
            scene_outline: 场景大纲
            scene_implementation: 场景实现计划
            file_prefix: 文件前缀
            relevant_plugins: 相关插件列表
            
        Returns:
            Dict: 处理结果
        """
        logger.info(f"处理场景{scene_number}的代码生成和渲染")
        
        # 创建场景目录
        scene_dir = os.path.join(self.output_dir, file_prefix, f"scene{scene_number}")
        code_dir = os.path.join(scene_dir, "code")
        os.makedirs(code_dir, exist_ok=True)
        
        # 提取实现细节
        vision_storyboard = scene_implementation.get("vision_storyboard", "")
        technical_implementation = scene_implementation.get("technical_implementation", "")
        animation_narration = scene_implementation.get("animation_narration", "")
        
        # 1. 生成Manim代码
        code, version = await self._generate_manim_code(
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            scene_implementation=scene_implementation,
            scene_number=scene_number,
            file_prefix=file_prefix,
            relevant_plugins=relevant_plugins
        )
        
        # 保存初始代码
        code_path = os.path.join(code_dir, f"{file_prefix}_scene{scene_number}_v{version}.py")
        with open(code_path, 'w') as f:
            f.write(code)
        logger.info(f"初始代码已保存到: {code_path}")
        
        # 2. 渲染和修复过程
        media_dir = os.path.join(self.output_dir, file_prefix, "media")
        os.makedirs(media_dir, exist_ok=True)
        
        max_fix_attempts = self.workflow_config.get("max_fix_attempts", 3)
        current_version = version
        final_code = code
        error_logs = []
        
        for attempt in range(max_fix_attempts):
            # 尝试渲染
            code_path = os.path.join(code_dir, f"{file_prefix}_scene{scene_number}_v{current_version}.py")
            render_result, error = await self._render_scene(code_path, media_dir)
            
            if error:
                # 记录错误
                error_logs.append({
                    "version": current_version,
                    "error": error
                })
                
                # 尝试修复代码
                logger.info(f"尝试修复代码 (尝试 {attempt+1}/{max_fix_attempts})")
                fixed_code = await self._fix_code_errors(
                    final_code, 
                    error, 
                    technical_implementation,
                    scene_number,
                    topic
                )
                
                # 保存修复的代码
                current_version += 1
                code_path = os.path.join(code_dir, f"{file_prefix}_scene{scene_number}_v{current_version}.py")
                with open(code_path, 'w') as f:
                    f.write(fixed_code)
                logger.info(f"修复的代码已保存到: {code_path}")
                
                # 更新当前代码
                final_code = fixed_code
            else:
                # 如果启用视觉修复，尝试进行视觉优化
                if self.enable_visual_fix:
                    video_path = os.path.join(media_dir, "videos", f"{file_prefix}_scene{scene_number}_v{current_version}.mp4")
                    if os.path.exists(video_path):
                        visual_improvements = await self._visual_feedback(
                            video_path,
                            final_code,
                            technical_implementation,
                            scene_number,
                            topic
                        )
                        
                        # 如果有改进建议，应用它们
                        if visual_improvements and "<LGTM>" not in visual_improvements:
                            current_version += 1
                            code_path = os.path.join(code_dir, f"{file_prefix}_scene{scene_number}_v{current_version}.py")
                            with open(code_path, 'w') as f:
                                f.write(visual_improvements)
                            logger.info(f"视觉优化的代码已保存到: {code_path}")
                            
                            # 更新当前代码并重新渲染
                            final_code = visual_improvements
                            render_result, error = await self._render_scene(code_path, media_dir)
                            if error:
                                error_logs.append({
                                    "version": current_version,
                                    "error": error
                                })
                                continue
                
                # 渲染成功，退出循环
                break
                
        # 记录最终结果
        result = {
            "code_versions": current_version,
            "final_code_path": os.path.join(code_dir, f"{file_prefix}_scene{scene_number}_v{current_version}.py"),
            "final_video_path": os.path.join(media_dir, "videos", f"{file_prefix}_scene{scene_number}_v{current_version}.mp4"),
            "error_logs": error_logs,
            "success": len(error_logs) < max_fix_attempts
        }
        
        return result
    
    async def _generate_manim_code(
        self,
        topic: str,
        description: str,
        scene_outline: str,
        scene_implementation: Dict[str, str],
        scene_number: int,
        file_prefix: str,
        relevant_plugins: List[str]
    ) -> tuple[str, int]:
        """
        生成Manim代码
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            scene_implementation: 场景实现计划
            scene_number: 场景编号
            file_prefix: 文件前缀
            relevant_plugins: 相关插件列表
            
        Returns:
            tuple: (代码, 版本号)
        """
        logger.info(f"为场景{scene_number}生成Manim代码")
        
        # 提取实现细节
        vision_storyboard = scene_implementation.get("vision_storyboard", "")
        technical_implementation = scene_implementation.get("technical_implementation", "")
        animation_narration = scene_implementation.get("animation_narration", "")
        
        # 获取代码生成代理
        code_agent = self.agents["code_generator"]
        
        # 检查是否有之前的代码
        code_dir = os.path.join(self.output_dir, file_prefix, f"scene{scene_number}", "code")
        version = 1
        
        if self.start_stage and self.start_stage != "code_generation":
            # 尝试找到最新版本的代码
            existing_files = [f for f in os.listdir(code_dir) if f.endswith('.py') and f"scene{scene_number}" in f]
            if existing_files:
                # 提取版本号并找到最高版本
                versions = [int(f.split('_v')[-1].split('.')[0]) for f in existing_files]
                if versions:
                    max_version = max(versions)
                    version = max_version
                    code_path = os.path.join(code_dir, f"{file_prefix}_scene{scene_number}_v{version}.py")
                    try:
                        with open(code_path, 'r') as f:
                            code = f.read()
                        logger.info(f"已加载之前的代码: {code_path}")
                        return code, version
                    except Exception as e:
                        logger.error(f"加载之前的代码失败: {str(e)}")
        
        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            # 为代码生成生成查询
            queries = self.toolkits["rag"].generate_queries_for_code(
                technical_implementation, relevant_plugins, topic, scene_number, self.session_id
            )
            # 使用生成的查询获取上下文
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
        
        # 生成代码
        code_result = await code_agent.generate(
            topic=topic,
            description=description,
            scene_number=scene_number,
            technical_implementation=technical_implementation,
            animation_narration=animation_narration,
            session_id=self.session_id
        )
        
        # 检查结果是否为字典，并提取代码
        if isinstance(code_result, dict):
            code = code_result.get("code", "")
            if not code and code_result.get("status") == "error":
                error_msg = code_result.get("message", "生成代码出错")
                logger.error(f"生成代码失败: {error_msg}")
                code = f"# 代码生成失败: {error_msg}\n\nfrom manim import *\n\nclass Scene{scene_number}(Scene):\n    def construct(self):\n        self.add(Text('代码生成失败，请重试'))"
        else:
            code = code_result
        
        # 提取<CODE>标签中的内容
        code_match = extract_xml(code, "CODE")
        if code_match:
            # 去除可能的三重反引号
            code = code_match.replace("```python", "").replace("```", "").strip()
        
        return code, version
    
    async def _render_scene(self, code_path: str, media_dir: str) -> tuple[Any, Optional[str]]:
        """
        渲染场景
        
        Args:
            code_path: 代码文件路径
            media_dir: 媒体目录
            
        Returns:
            tuple: (渲染结果, 错误信息)
        """
        try:
            # 使用Manim工具包渲染场景
            return self.toolkits["manim"].render_scene(code_path, media_dir), None
        except Exception as e:
            error_message = str(e)
            logger.error(f"渲染错误: {error_message}")
            return None, error_message
    
    async def _fix_code_errors(
        self, 
        code: str, 
        error: str, 
        implementation_plan: str,
        scene_number: int,
        topic: str
    ) -> str:
        """
        修复代码错误
        
        Args:
            code: 原始代码
            error: 错误信息
            implementation_plan: 实现计划
            scene_number: 场景编号
            topic: 主题
            
        Returns:
            str: 修复后的代码
        """
        logger.info("尝试修复代码错误")
        
        # 获取代码修复代理
        code_fixer = self.agents["code_fixer"]
        
        # 获取RAG上下文（如果启用）
        rag_context = None
        if self.enable_rag and self.toolkits["rag"]:
            # 为错误修复生成查询
            queries = self.toolkits["rag"].generate_queries_for_fix_error(
                error, code, topic, scene_number, self.session_id
            )
            # 使用生成的查询获取上下文
            rag_context = self.toolkits["rag"].search_with_queries(queries, limit_per_query=3)
            
        # 构建提示
        prompt = f"""
你是一位专业的Manim代码修复专家，需要修复以下Manim代码中的错误。

错误信息:
{error}

当前代码:
```
{code}
```

技术实现计划:
{implementation_plan}

请分析错误原因，特别关注以下常见问题:
1. 空间位置和布局问题
2. 动画时序和同步问题
3. 对象创建和属性设置问题
4. 导入错误和依赖问题

{rag_context if rag_context else ""}

请提供完整的修复后代码。不要省略任何部分，确保代码可以直接执行。
"""
        
        # 修复代码
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
        fixed_code_response = code_fixer.step(user_message)
        fixed_code = fixed_code_response.msg.content
        
        # 尝试提取代码块
        code_blocks = re.findall(r"```(?:python)?\n(.*?)\n```", fixed_code, re.DOTALL)
        if code_blocks:
            fixed_code = code_blocks[0]
        
        return fixed_code
    
    async def _visual_feedback(
        self, 
        video_path: str, 
        code: str, 
        implementation_plan: str,
        scene_number: int,
        topic: str
    ) -> Optional[str]:
        """
        根据视频渲染结果提供视觉反馈
        
        Args:
            video_path: 视频文件路径
            code: 当前代码
            implementation_plan: 实现计划
            scene_number: 场景编号
            topic: 主题
            
        Returns:
            Optional[str]: 修改后的代码或None
        """
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return None
        
        logger.info("分析视频渲染结果并提供视觉反馈")
        
        # 获取视觉反馈代理
        visual_agent = self.agents["visual_feedback"]
        
        # 构建提示
        prompt = f"""
你是一位专业的视觉反馈专家，需要分析以下Manim生成的视频并提供改进建议。

请评估以下视频渲染结果的效果：
视频路径: {video_path}

当前代码:
```
{code}
```

技术实现计划:
{implementation_plan}

请分析以下视觉呈现问题:
1. 元素位置和空间布局 - 是否有重叠、错位或不平衡的情况
2. 文本可读性 - 是否有文本太小、颜色对比度不足的问题
3. 动画时序 - 动画是否流畅、时间分配是否合理
4. 视觉层次结构 - 重要元素是否突出，次要元素是否适当降低视觉权重

如果视频渲染效果已经很好，请回复"<LGTM>"(Looks Good To Me)。
如果需要改进，请提供完整的修改后代码。
"""
        
        # 获取视觉反馈
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
        feedback_response = visual_agent.step(user_message)
        feedback = feedback_response.msg.content
        
        # 检查是否需要修改
        if "<LGTM>" in feedback:
            logger.info("视频渲染效果良好，无需修改")
            return None
        
        # 尝试提取代码块
        code_blocks = re.findall(r"```(?:python)?\n(.*?)\n```", feedback, re.DOTALL)
        if code_blocks:
            improved_code = code_blocks[0]
            return improved_code
        
        return None
    
    async def evaluate_video(
        self, 
        topic: str, 
        description: str, 
        scene_results: Dict[int, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        评估生成的视频质量
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_results: 场景渲染结果
            
        Returns:
            Dict: 评估结果
        """
        logger.info(f"评估视频质量: {topic}")
        
        file_prefix = topic.lower().replace(' ', '_').replace('-', '_')
        
        # 检查所有场景是否成功渲染
        all_scenes_success = all(result.get("success", False) for result in scene_results.values())
        if not all_scenes_success:
            logger.warning("部分场景渲染失败，这可能影响评估结果")
        
        # 收集所有视频路径进行评估
        video_paths = []
        for scene_number, result in scene_results.items():
            video_path = result.get("final_video_path")
            if video_path and os.path.exists(video_path):
                video_paths.append(video_path)
        
        if not video_paths:
            logger.error("没有找到可评估的视频文件")
            return {"overall_score": 0, "error": "没有找到可评估的视频文件"}
        
        # 构建评估文本
        transcript = self._extract_transcripts(scene_results)
        
        # 获取评估代理
        evaluator = self.agents["evaluator"]
        
        # 构建提示
        prompt = f"""
你是一位专业的教育视频评估专家，需要评估以下数学定理讲解视频的质量。

视频信息:
- 主题: {topic}
- 描述: {description}
- 视频文件: {', '.join(video_paths)}
- 讲解文本: {transcript}

请从以下维度评估视频质量:

1. 教学效果 (30分)
   - 概念解释清晰度
   - 逻辑结构和连贯性
   - 内容深度与适当的复杂度
   
2. 视觉呈现 (30分)
   - 动画质量和流畅度
   - 空间布局和视觉平衡
   - 颜色使用和视觉层次结构
   
3. 内容准确性 (20分)
   - 数学定理和证明的准确性
   - 术语使用的精确性
   - 无概念性错误
   
4. 观众体验 (20分)
   - 节奏和时长适当性
   - 叙述风格和表达清晰度
   - 视听体验的整体协调性

请为每个维度提供详细评分和具体评语，指出优点和不足。
最后给出总体评分(满分100分)和整体评价。
"""
        
        # 获取评估结果
        user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
        evaluation_response = evaluator.step(user_message)
        evaluation_text = evaluation_response.msg.content
        
        # 提取总分
        score_pattern = r"总体评分[:：]\s*(\d+(?:\.\d+)?)"
        score_match = re.search(score_pattern, evaluation_text, re.IGNORECASE)
        overall_score = float(score_match.group(1)) if score_match else 0
        
        # 保存评估结果
        evaluation_file = os.path.join(self.output_dir, file_prefix, "evaluation.json")
        evaluation_result = {
            "overall_score": overall_score,
            "evaluation_text": evaluation_text,
            "scene_results": {k: {key: value for key, value in v.items() if key != "error_logs"} 
                             for k, v in scene_results.items()}
        }
        
        with open(evaluation_file, 'w') as f:
            json.dump(evaluation_result, f, indent=2)
        logger.info(f"评估结果已保存到: {evaluation_file}")
        
        return evaluation_result
    
    def _extract_transcripts(self, scene_results: Dict[int, Dict[str, Any]]) -> str:
        """
        从场景结果中提取讲解文本
        
        Args:
            scene_results: 场景渲染结果
            
        Returns:
            str: 合并的讲解文本
        """
        transcripts = []
        
        for scene_number, result in sorted(scene_results.items()):
            code_path = result.get("final_code_path")
            if code_path and os.path.exists(code_path):
                try:
                    with open(code_path, 'r') as f:
                        code = f.read()
                    
                    # 提取voiceover文本
                    voiceover_pattern = r'self\.voiceover\(text="([^"]+)"\)'
                    voiceovers = re.findall(voiceover_pattern, code)
                    
                    if voiceovers:
                        scene_transcript = f"场景{scene_number}讲解内容:\n"
                        for i, vo in enumerate(voiceovers, 1):
                            scene_transcript += f"{i}. {vo}\n"
                        transcripts.append(scene_transcript)
                except Exception as e:
                    logger.error(f"提取场景{scene_number}讲解文本失败: {str(e)}")
        
        return "\n".join(transcripts)
    
    async def combine_videos(self, topic: str, scene_results: Dict[int, Dict[str, Any]]) -> Optional[str]:
        """
        合并所有场景视频
        
        Args:
            topic: 定理主题
            scene_results: 场景渲染结果
            
        Returns:
            Optional[str]: 合并后的视频路径
        """
        logger.info(f"合并视频: {topic}")
        
        file_prefix = topic.lower().replace(' ', '_').replace('-', '_')
        
        # 收集所有视频路径
        video_paths = []
        for scene_number, result in sorted(scene_results.items()):
            video_path = result.get("final_video_path")
            if video_path and os.path.exists(video_path):
                video_paths.append(video_path)
        
        if not video_paths:
            logger.error("没有找到可合并的视频文件")
            return None
        
        # 使用Manim工具包合并视频
        try:
            combined_video_path = os.path.join(self.output_dir, file_prefix, f"{file_prefix}_combined.mp4")
            self.toolkits["manim"].combine_videos(video_paths, combined_video_path)
            logger.info(f"合并视频已保存到: {combined_video_path}")
            return combined_video_path
        except Exception as e:
            logger.error(f"合并视频失败: {str(e)}")
            return None
    
    async def run_workflow(self, topic: str, description: str) -> Dict[str, Any]:
        """运行完整的工作流程

        Args:
            topic: 定理主题
            description: 定理描述

        Returns:
            Dict[str, Any]: 工作流结果
        """
        logger.info(f"开始运行工作流，处理定理: {topic}")
        logger.info(f"工作流起始阶段: {self.start_stage}")
        logger.info(f"RAG增强: {'启用' if self.enable_rag else '禁用'}")
        logger.info(f"上下文学习: {'启用' if self.enable_context_learning else '禁用'}")
        
        result = {}
        
        # 执行场景规划阶段
        if self.start_stage == "scene_plan":
            # 生成场景规划
            scene_plan_result = await self.generate_scene_planning(topic, description)
            if scene_plan_result.get("status") != "success":
                return scene_plan_result
            
            scene_plan = scene_plan_result.get("scene_plan", "")
            result["scene_plan"] = scene_plan
            
            if self.start_stage != "vision_storyboard":  # 如果起始阶段不是视觉故事板，继续下一阶段
                self.start_stage = "vision_storyboard"
        
        # 如果有起始场景计划和已设置的，从结果或文件中加载
        if "scene_plan" not in result and self.start_stage != "scene_plan":
            # 尝试从文件加载
            output_path = self.workflow_config.get("scene_plan_file", f"output/{topic.replace(' ', '_')}/scene_outline.txt")
            try:
                with open(output_path, "r", encoding="utf-8") as f:
                    scene_plan = f.read()
                result["scene_plan"] = scene_plan
            except Exception as e:
                logger.error(f"加载场景规划失败: {str(e)}")
                return {"status": "error", "message": f"加载场景规划失败: {str(e)}"}
        
        # 后续处理阶段...
        
        # 场景实现和代码生成
        scene_implementations = await self.generate_scene_implementation(topic, description, result.get("scene_plan", ""))
        result["scene_implementations"] = scene_implementations
        
        # 代码生成
        scene_results = await self.generate_code_and_render(topic, description, scene_implementations)
        result["scene_results"] = scene_results
        
        # 评估视频
        evaluation_result = await self.evaluate_video(topic, description, scene_results)
        result["evaluation"] = evaluation_result
        
        # 合并视频
        combined_video = await self.combine_videos(topic, scene_results)
        result["combined_video"] = combined_video
        
        # 保存最终结果
        file_prefix = topic.lower().replace(' ', '_').replace('-', '_')
        final_result_path = os.path.join(self.output_dir, file_prefix, "workflow_result.json")
        
        with open(final_result_path, 'w') as f:
            # 过滤掉太大的字段
            filtered_result = {
                "topic": topic,
                "description": description,
                "scene_plan": scene_plan,
                "scene_counts": len(scene_implementations),
                "evaluation": evaluation_result,
                "combined_video": combined_video,
                "completion_time": datetime.datetime.now().isoformat()
            }
            json.dump(filtered_result, f, indent=2)
        
        logger.info(f"工作流完成，结果已保存到: {final_result_path}")
        return result

    # 添加辅助方法
    def _parse_scene_outline(self, scene_outline: str) -> List[str]:
        """解析场景大纲，提取各个场景的内容"""
        if not scene_outline:
            return []
            
        scenes = []
        current_scene = ""
        scene_content = []
        
        for line in scene_outline.strip().split('\n'):
            # 跳过XML标签
            if line.strip() == "<SCENE_OUTLINE>" or line.strip() == "</SCENE_OUTLINE>":
                continue
                
            if line.startswith('场景') and ':' in line:
                # 如果已有场景内容，保存之前的场景
                if current_scene and scene_content:
                    scenes.append('\n'.join(scene_content))
                    scene_content = []
                
                # 开始新场景
                current_scene = line
                scene_content.append(line)
            elif line.strip() and current_scene:
                scene_content.append(line)
        
        # 添加最后一个场景
        if current_scene and scene_content:
            scenes.append('\n'.join(scene_content))
            
        return scenes

    def _get_scene_by_number(self, scene_outline: str, scene_number: int) -> Optional[str]:
        """获取指定编号的场景"""
        scenes = self._parse_scene_outline(scene_outline)
        if scene_number <= 0 or scene_number > len(scenes):
            return None
        return scenes[scene_number - 1]

    def _get_storyboard_by_scene_number(self, storyboards: List[Dict], scene_number: int) -> Optional[str]:
        """获取指定场景编号的视觉故事板"""
        for sb in storyboards:
            if sb.get("scene_number") == scene_number:
                return sb.get("vision_storyboard")
        return None

    def _get_implementation_by_scene_number(self, implementations: List[Dict], scene_number: int) -> Optional[str]:
        """获取指定场景编号的技术实现计划"""
        for impl in implementations:
            if impl.get("scene_number") == scene_number:
                return impl.get("technical_implementation")
        return None

async def main():
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="运行定理解释视频生成工作流")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--topic", required=True, help="定理主题")
    parser.add_argument("--description", required=True, help="定理描述")
    parser.add_argument("--start-stage", choices=["scene_plan", "vision_storyboard", "technical_implementation", "animation_narration", "code_generation", "rendering", "evaluation"],
        help="起始阶段")
    args = parser.parse_args()
    
    # 初始化工作流
    workflow = TheoremExplainWorkflow(config_path=args.config)
    
    # 设置起始阶段
    if args.start_stage:
        workflow.start_stage = args.start_stage
    
    # 运行工作流
    result = await workflow.run_workflow(args.topic, args.description)
    
    print(f"工作流完成。最终评分: {result.get('evaluation', {}).get('overall_score', 0)}")
    if result.get("combined_video"):
        print(f"合并视频路径: {result['combined_video']}")

if __name__ == "__main__":
    asyncio.run(main()) 