# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: 测试
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class Scene_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_step_by_step
        animate_step_by_step(
            scene=self,
            steps=[{'step_number': '1', 'title': '第一步', 'content': '## 开始\n这是第一个步骤的内容。', 'color': '#FF6B6B'}, {'step_number': '2', 'title': '第二步', 'content': '## 继续\n这是第二个步骤的内容。', 'color': '#4ECDC4'}, {'step_number': '3', 'title': '第三步', 'content': '## 完成\n这是最后一个步骤的内容。', 'color': '#45B7D1'}],
            title="简单步骤演示",
            subtitle="测试分步骤动画",
            content_narration="让我们看看这个简单的三步骤演示"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
