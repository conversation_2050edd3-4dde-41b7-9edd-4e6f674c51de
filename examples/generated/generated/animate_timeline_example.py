# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os

root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered


class animate_timeline_(FeynmanScene):
    config.background_color = "#FFFFFF"

    def construct(self):
        self.add_background()

        # Action 1: animate_timeline
        animate_timeline(
            scene=self,
            events=[
                {
                    "year": "1950",
                    "title": "开始",
                    "description": "新时代",
                    "emoji": "🌍",
                    "color": "#F0E68C",
                },
                {
                    "year": "1965",
                    "title": "发现",
                    "description": "关键发现",
                    "emoji": "🌞",
                    "color": "#FFA500",
                },
                {
                    "year": "1980",
                    "title": "扩张",
                    "description": "增长",
                    "emoji": "🌛",
                    "color": "#B22222",
                },
                {
                    "year": "2000",
                    "title": "新世纪",
                    "description": "继续",
                    "emoji": "🪵",
                    "color": "#FFC0CB",
                },
                {
                    "year": "2025",
                    "title": "现代",
                    "description": "爆炸",
                    "emoji": "🚀",
                    "color": "#9370DB",
                },
            ],
            title="时间轴示例",
            narration="A journey through time, highlighting key moments.",
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
