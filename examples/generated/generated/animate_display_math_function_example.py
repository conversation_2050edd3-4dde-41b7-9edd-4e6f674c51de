# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_display_math_function_(FeynmanScene):
    def construct(self):
        # Action 1: display_math_function
        display_math_function(
            scene=self,
            function_str="x**2",
            properties=['Parabola opening upwards', 'Vertex at (0,0)', 'Symmetric about the y-axis'],
            x_range=[-3, 3, 0.1],
            y_range=[0, 9, 1],
            title="Graph of y = x^2",
            properties_title="Key Features",
            voiceover_text="Here we see the graph of y equals x squared, a parabola, along with its key features."
        )

        # Action 2: display_math_function
        display_math_function(
            scene=self,
            function_str="np.sin(x)",
            properties=['Periodic function', 'Range: [-1, 1]', 'Period: 2π'],
            x_range=[-6.28, 6.28, 0.1],
            graph_color="GREEN",
            voiceover_text="This is the sine function, notice its wave-like pattern."
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
