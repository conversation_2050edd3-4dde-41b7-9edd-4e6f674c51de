# animate_display_math_function

## 效果

Displays a mathematical function's graph on the left side of the screen
and a list of its properties or related text on the right side.
Supports customizable graph appearance, titles, and voiceover.


## 使用场景

- Visualizing a mathematical function like y = x^2 or y = sin(x).
- Presenting key characteristics of a function alongside its plot.
- Educational content explaining mathematical concepts.
- Comparing the visual form of a function with its analytical properties.

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim scene instance (automatically passed by the system). | 是 | - |
| function_str | str | The mathematical function to plot, as a string. Uses 'x' as the variable. Examples: 'x**2', 'np.sin(x)', 'lambda x: x**3 - x'. | 是 | - |
| properties | list[str] | A list of strings, where each string is a property or feature of the function to be displayed. Each item will be a bullet point. | 是 | - |
| x_range | tuple[float, float, float] | The range of x-values for the plot, as (x_min, x_max, x_step). Example: (-5, 5, 0.1) | 否 | [-5, 5, 0.1] |
| y_range | tuple[float, float, float] | The range of y-values for the plot, as (y_min, y_max, y_step). Example: (0, 25, 5). If None, y-range is auto-determined. | 否 | None |
| graph_color | str | Color of the function graph. Example: 'BLUE', '#FF0000'. | 否 | YELLOW |
| title | str | Optional main title displayed above the graph and properties. | 否 | None |
| properties_title | str | Optional title for the properties section. | 否 | None |
| voiceover_text | str | Text for voiceover narration for this animation segment. | 否 | None |
| graph_width_ratio | float | Ratio of screen width for the graph (0.0 to 1.0). Properties take (1 - ratio). | 否 | 0.55 |
| text_scale | float | Scaling factor for all text elements (titles, properties). | 否 | 0.6 |

## DSL示例

### 示例 1

```json
{
  "type": "display_math_function",
  "params": {
    "function_str": "x**2",
    "properties": [
      "Parabola opening upwards",
      "Vertex at (0,0)",
      "Symmetric about the y-axis"
    ],
    "x_range": [
      -3,
      3,
      0.1
    ],
    "y_range": [
      0,
      9,
      1
    ],
    "title": "Graph of y = x^2",
    "properties_title": "Key Features",
    "voiceover_text": "Here we see the graph of y equals x squared, a parabola, along with its key features."
  }
}
```

### 示例 2

```json
{
  "type": "display_math_function",
  "params": {
    "function_str": "np.sin(x)",
    "properties": [
      "Periodic function",
      "Range: [-1, 1]",
      "Period: 2π"
    ],
    "x_range": [
      -6.28,
      6.28,
      0.1
    ],
    "graph_color": "GREEN",
    "voiceover_text": "This is the sine function, notice its wave-like pattern."
  }
}
```

## 注意事项

- The `function_str` is evaluated in a restricted environment. Ensure it's safe and uses 'x' as the variable. NumPy functions (e.g., 'np.sin') are available.
- If `y_range` is not provided, Manim will attempt to auto-scale the y-axis, which might not always be optimal.
- Ensure `properties` list is not excessively long to maintain readability.
- The layout is responsive, but very long text in properties might require adjustments to `graph_width_ratio` or `text_scale`.

