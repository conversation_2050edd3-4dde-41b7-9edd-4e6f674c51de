
5.13 
按需交付：输入需求->拆解步骤（需求洞察）->{每步骤的功能详细描述->框架选择->代码实现（包括human in the loop）->测试优化}（费曼分镜）-> 可视化呈现
特点：1 端到端交付 2 知识库提准确率，工具/模型-例子代码-API-框架原理-背景知识构建知识库；3 可视化呈现(录屏、代码、例子、基础、框架等)
路径：1 使用一个框架实现具体功能，跑通子环节（6.15）；2 候选多框架实现一个功能，跑通按需交付链路，生成视频（7.15）；3 建立知识库，通过Rag、Agent优化、代码模型微调提准确率，提升链路效果和效率（9.15）； 4 App和平台交互式优化（10.15）
卡点：
    1 单框架自动生成代码效果（聚焦头部框架，跑通的例子做案例，API和自带例子，RAG代码库，theorem_agents分层，手写例子选择）
    2 任务拆解到多个框架选择准确性(框架的描述，框架知识库)；
    3 多框架多环节代码连调准确性；
趋势：MCP取代各种项目框架，减少代码开发量；多种能力逐渐被头部几个模型覆盖，那退化成优化prompt,多次调用模型. 参考windsurf最新功能

5.22
1 框架逻辑: xx2markdown + prompt(含画像意图目的侧重、prompt学习) -> materialAgent(提炼、费曼)->renderAgent(渲染)
2 知识库建设、prompt学习、费曼优化

5.27 manim
1 icon/svg library
2 Wolfram Alpha 等MCP验证准确性
3 丰富manim-physics、manim-chemistry、manim-circuit、manim-ml插件、完善的manim文档、例子RAG库！
4 生成非常详细的prompt适合manim，类似TheromExplainer的流程
5 类似的kodisc、video tutor
