# animate_step_by_step

## 效果

在Manim场景中创建并播放一个分步骤讲解的动画。
左侧展示步骤节点，每一步包含序号和操作描述，节点动态生成并向上移动。
右侧展示每一步的具体内容（markdown格式），支持代码、列表、文本等。
最后所有步骤节点缩放移动到画面正中，展示整体概念。


## 使用场景

- 教学演示中的分步骤讲解，如算法步骤、操作流程等
- 产品功能介绍，逐步展示各个功能点
- 项目开发流程演示，突出每个阶段的重点

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| steps | list[StepData | dict[str, Any]] | 步骤列表。每个元素包含step_number(步骤序号), title(步骤标题), content(步骤内容，markdown格式), color(节点颜色，可选), narration(步骤旁白，可选)等属性 | 是 | - |
| intro_narration | str | 开场介绍语音旁白文本 | 否 | None |
| outro_narration | str | 结尾总结语音旁白文本 | 否 | None |
| title | str | 整体标题 | 否 | None |
| subtitle | str | 副标题 | 否 | None |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_step_by_step",
  "params": {
    "steps": [
      {
        "step_number": "1",
        "title": "初始化数据",
        "content": "## 创建数组\n```python\narr = [64, 34, 25, 12, 22, 11, 90]\n```\n- 准备待排序的数组\n- 记录数组长度\n",
        "color": "#FF6B6B",
        "narration": "首先我们初始化一个待排序的数组"
      },
      {
        "step_number": "2",
        "title": "选择最小元素",
        "content": "## 查找最小值\n```python\nmin_idx = 0\nfor i in range(1, len(arr)):\n    if arr[i] < arr[min_idx]:\n        min_idx = i\n```\n- 遍历未排序部分\n- 找到最小元素的索引\n",
        "color": "#4ECDC4",
        "narration": "接下来在未排序部分找到最小的元素"
      },
      {
        "step_number": "3",
        "title": "交换元素",
        "content": "## 元素交换\n```python\narr[0], arr[min_idx] = arr[min_idx], arr[0]\n```\n- 将最小元素移到已排序部分的末尾\n- 扩大已排序区域\n",
        "color": "#45B7D1",
        "narration": "然后将最小元素与第一个位置交换"
      }
    ],
    "title": "选择排序算法演示",
    "subtitle": "逐步理解排序过程",
    "intro_narration": "今天我们来学习选择排序算法的工作原理",
    "outro_narration": "通过这三个步骤，我们完成了选择排序的一轮操作"
  }
}
```

## 注意事项

- 步骤按数组中的顺序呈现，每个步骤的内容支持完整的markdown语法
- 左侧节点会动态生成并向上移动，右侧内容会淡出后显示新内容
- 可以为每个步骤指定颜色，或使用默认颜色方案
- 最后所有步骤节点会缩放移动到画面中央，形成整体概览
- 与timeline的差别是，step_by_step需要讲解每个步骤中的详细内容，因此更适合例子讲解等场景，而timeline只是展示事件的整体脉络

