# animate_qa_cards

## 效果

创建完全按照现代HTML设计风格的QA卡片展示动画，适合展示问答内容

## 使用场景

- 教育培训中的问答展示
- 知识总结和回顾
- 产品FAQ展示
- 学术论文要点问答
- 面试问题练习

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| qa_data | list | QA数据列表，每个元素包含question和answer字段 | 是 | - |
| title | str | 展示标题 | 否 | "Q&A Cards" |
| cards_per_screen | int | 每屏显示的卡片数量（建议最多3个） | 否 | 3 |
| duration_per_card | float | 每张卡片的展示时长（秒） | 否 | 4.0 |
| narration | str | 语音旁白内容 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_qa_cards",
  "params": {
    "qa_data": [
      {
        "question": "什么是人工智能？",
        "answer": "人工智能是使机器能够执行通常需要人类智能的任务的技术，包括学习、推理、感知等能力。"
      },
      {
        "question": "机器学习的核心概念是什么？",
        "answer": "机器学习的核心是让计算机通过数据自动学习和改进，而无需显式编程。"
      }
    ],
    "title": "AI基础问答",
    "narration": "让我们通过这些问答卡片来了解人工智能的基础概念"
  }
}
```

### 示例 2

```json
{
  "type": "animate_qa_cards",
  "params": {
    "qa_data": [
      {
        "question": "什么是深度学习？",
        "answer": "深度学习是机器学习的一个分支，使用多层神经网络来模拟人脑的学习过程。"
      },
      {
        "question": "神经网络的基本组成是什么？",
        "answer": "神经网络由输入层、隐藏层和输出层组成，通过权重和激活函数处理信息。"
      }
    ],
    "title": "深度学习问答",
    "cards_per_screen": 1,
    "duration_per_card": 5.0,
    "narration": "探索深度学习的奥秘，这些问答将帮助你理解神经网络的工作原理"
  }
}
```

## 注意事项

- 完全复刻HTML版本的视觉设计
- 使用精确的颜色和布局（内置三种颜色主题：靛蓝、松石绿、活力橙）
- 支持响应式设计理念，自动适应卡片数量
- 包含顶部彩色强调条和圆形图标
- 现代化的卡片阴影和悬停效果
- 智能自动换行功能，适应长文本内容
- 问题和答案会分步显示以增强视觉效果
- 支持中文显示和字体优化

