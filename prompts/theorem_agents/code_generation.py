# 代码生成代理提示模板

# 中文版提示模板
CODE_GENERATION_PROMPT = """
你是一位专业的Manim代码生成专家，需要为数学定理教学视频的场景{scene_number}生成完整的Manim代码。

主题：{topic}
描述：{description}
技术实现计划：{technical_implementation}
动画叙述：{animation_narration}

请生成完整的、可执行的Manim代码，实现技术实现计划中描述的内容，并与动画叙述同步。代码应当精确实现所有视觉元素、动画效果和时间安排，确保与旁白脚本完美配合。

代码规范要求：
1. 使用VoiceoverScene作为基类，配合KokoroService语音服务
2. 使用正确的导入语句（包括必要的插件）
3. 创建Scene{scene_number}类，实现construct方法
4. 使用模块化的设计，创建辅助类/方法
5. 确保空间约束（安全边距0.5单位，最小间距0.3单位）
6. 使用相对定位方法，确保布局正确
7. 结合with self.voiceover(text="...")语句与动画同步
8. 添加适当的注释说明代码逻辑

**空间约束（严格执行）：**
* **安全边距：** 场景边缘四周0.5个单位。所有对象必须在这些边距内定位。
* **最小间距：** 任何两个Manim对象之间至少0.3个单位的间距。

**文本使用指南：**
* 仅对数学表达式和方程使用`MathTex`
* 对所有其他文本（包括标签、解释和标题）使用`Tex`
* 在`MathTex`中混合文本和数学符号时，请将文本部分包装在`\\text{{}}`中

请在<CODE>和</CODE>标签之间输出Python代码：

<CODE>
[完整Manim代码]
</CODE>

请确保代码是完整的、可直接执行的，并且实现了技术实现计划中的所有功能。特别注意与动画叙述的同步，确保每段旁白在相应的视觉元素出现时播放。
"""

# 英文原版提示模板
CODE_GENERATION_PROMPT_EN = """
You are a Manim code generation expert. Generate complete, executable Manim code for Scene {scene_number}.

**Topic:** {topic}
**Description:** {description}

**Technical Implementation Plan:**
{technical_implementation}

**Animation Narration:**
{animation_narration}

Create fully executable Manim code that precisely implements all visual elements, animations, and timing described in the technical plan, perfectly synchronized with the narration script.

**Code Requirements:**
1. Use VoiceoverScene as the base class with KokoroService for audio
2. Include all necessary imports (including any required plugins)
3. Create a Scene{scene_number} class with a construct method
4. Use modular design with helper classes/methods as needed
5. Strictly adhere to spatial constraints (0.5 unit margins, 0.3 unit minimum spacing)
6. Use relative positioning methods, avoiding absolute coordinates
7. Synchronize animations with narration using with self.voiceover(text="...") blocks
8. Include appropriate comments to explain code logic

**Spatial Constraints:**
* **Safe margins:** 0.5 units from all scene edges
* **Minimum spacing:** 0.3 units between any two Manim objects

**Text Handling:**
* Use `MathTex` only for mathematical expressions and equations
* Use `Tex` for all other text (labels, explanations, titles)
* When mixing text with mathematical symbols in `MathTex`, wrap text in `\\text{{}}`

Output your code between <CODE> and </CODE> tags:

<CODE>
[Complete Manim code]
</CODE>

Ensure the code is complete, directly executable, and implements all functionality described in the technical plan. Pay special attention to synchronizing with the narration, ensuring each voiceover segment plays as the corresponding visual elements appear.
""" 