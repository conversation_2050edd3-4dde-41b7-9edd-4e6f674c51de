#!/usr/bin/env python3
import sys
from agents.material_enhancement import MaterialEnhancer
from utils.create_llm_model import create_model
from utils.common import Config

config = Config()

original_file = sys.argv[1]
material_file = sys.argv[2]

# 1. 构造示例输入
original_content = open(original_file, "r", encoding="utf-8").read()
material_content = open(material_file, "r", encoding="utf-8").read()
purpose = "给技术爱好者介绍这个AI项目，目的介绍项目价值、具体核心能力, 项目架构等，风格是吸引用户试用，视频4分钟"

# 2. 实例化MaterialEnhancer
enhancer = MaterialEnhancer(config_dict=config.config, model=create_model())

# 3. 调用插桩分析方法
marked_content = enhancer.analyze_and_inject_tool_markers(original_content, material_content, purpose)

# 4. 保存插桩后内容
output_file = "tool_marked.md"
with open(output_file, "w", encoding="utf-8") as f:
    f.write(marked_content)
print(f"插桩后内容已保存到 {output_file}")

# 5. 测试自动融合工具结果
# 可直接读取刚才的marked_content，也可从文件读取
with open(output_file, "r", encoding="utf-8") as f:
    marked_content = f.read()
enhanced_content, enhancements = enhancer.apply_tools_and_merge(marked_content, output_dir="tool_enhance")
enhanced_output_file = "tool_enhanced.md"
with open(enhanced_output_file, "w", encoding="utf-8") as f:
    f.write(enhanced_content)
print(f"融合工具结果后的内容已保存到 {enhanced_output_file}")
print(f"本次自动调用的工具数量: {len(enhancements)}")
for i, enh in enumerate(enhancements, 1):
    print(f"[{i}] 工具: {enh['tool_name']}\n用途: {enh['suggested_usage']}\n结果: {enh['result']}\n")