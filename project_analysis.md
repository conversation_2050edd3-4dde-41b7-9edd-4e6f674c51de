# Chili3D: 浏览器中的3D CAD设计利器深度解析

## 1. 项目概览

Chili3D是一个基于Web的3D CAD（计算机辅助设计）应用，旨在提供强大的在线模型设计和编辑功能。它以其直观的界面、丰富的功能以及无需本地安装的便利性，为设计师、工程师以及3D打印爱好者提供了一个前所未有的在线设计平台。Chili3D在GitHub上拥有1385 Stars（截至报告日期），这充分表明了其在开源社区的受欢迎程度和认可度。

该项目的核心价值在于赋能用户，通过Web技术实现高性能的专业级三维设计能力，极大地降低了CAD软件的使用门槛和部署成本。它致力于将复杂的3D CAD能力带入浏览器，打破了传统桌面CAD软件的局限性，使得用户可以随时随地进行3D模型设计和协作。

![Chili3D 界面截图](output/chili3d/media/media_0.png)

## 2. 核心能力

Chili3D凭借其强大的功能集，覆盖了从基础建模到高级操作，再到数据管理和用户体验的多个维度，使其成为一个功能全面且用户友好的在线CAD工具。

### 2.1 建模工具

Chili3D提供了丰富的建模工具，支持用户快速创建和操作各种3D几何体，是构建复杂模型的基础：

*   **基础几何**：轻松创建盒体、圆柱体、圆锥体、球体、棱锥等常见几何形状。
*   **2D 草图绘制**：支持绘制线、弧、圆、椭圆、矩形、多边形和贝塞尔曲线，为3D模型的精确构建提供基础。
*   **高级实体操作**：
    *   **布尔运算**：支持并集、差集、交集等，用于组合或切割复杂几何体。
    *   **拉伸 (Extrusion)**：将2D草图沿指定方向拉伸成3D实体。
    *   **旋转 (Revolution)**：将2D草图绕轴旋转生成3D实体。
    *   **扫掠 (Sweeping)**：创建沿着指定路径变化的复杂曲面或实体。
    *   **放样 (Lofting)**：在多个截面之间创建平滑过渡的实体或曲面。
    *   **偏置曲面**：生成原始曲面的偏移曲面，常用于创建壳体或间隙结构。
    *   **截面创建**：生成模型的横截面视图，便于内部结构分析。

### 2.2 精准捕捉与追踪

为确保设计的精确性，Chili3D引入了先进的捕捉和追踪系统：

*   **对象捕捉**：精确捕捉到几何体的特征点（顶点、中点、交点、端点、直线、中点、象限点、切点、垂足、平行线、垂线等），显著提高建模精度。
*   **工作平面捕捉**：自动捕捉到当前工作平面，确保所有操作都在正确的平面上执行。
*   **轴线追踪**：沿着预设的轴线或自定义方向追踪，辅助精确对齐和放置对象。
*   **特征点检测**：自动识别并捕捉关键几何特征点。
*   **追踪可视化**：提供可视化的追踪线和参考点，辅助用户进行精确操作。

### 2.3 高效编辑工具

Chili3D提供了全面的编辑功能，支持用户修改和优化现有模型：

*   **形状修改**：
    *   **倒角 (Chamfer)**：对边缘进行直线切角处理。
    *   **圆角 (Fillet)**：对边缘进行圆弧过渡处理，改善模型外观或功能。
    *   **修剪 (Trim)**：剪除多余的几何部分。
    *   **打断 (Break)**：将几何体在特定点或位置断开。
    *   **拆分 (Split)**：将一个几何体分割成多个独立的部分。
*   **几何变换**：移动、旋转、缩放、镜像等基本变换操作，灵活调整模型的位置、方向和尺寸。
*   **高级编辑**：
    *   **特征移除**：智能地移除模型上的特定几何特征。
    *   **子部件操作**：支持对复杂装配体中的子部件进行独立编辑和管理。
    *   **分解复合对象**：将复合的几何对象分解为独立的组成部分，便于精细化编辑。

### 2.4 测量与分析工具

内置的测量工具使得用户能够准确获取模型尺寸信息，支持设计验证和质量控制：

*   长度、距离、角度测量。
*   面积和体积计算。
*   总长度计算。

### 2.5 文档管理与协同

Chili3D提供了便捷的文档管理功能，并支持行业标准格式，便于协作：

*   **文件操作**：创建、打开和保存文档。
*   **历史管理**：完整的撤销/重做堆栈和事务历史记录，确保操作可追溯和恢复。
*   **格式兼容性**：支持导入/导出工业标准格式，如STEP、IGES、BREP，方便与其他CAD软件协同工作和数据交换。

### 2.6 直观用户界面 (UI/UX)

Chili3D的用户界面设计注重效率和易用性，旨在提供桌面级应用的体验：

*   **Office 风格界面**：提供类似Microsoft Office应用的界面布局，命令组织清晰，工具栏和菜单项上下文相关，降低用户学习曲线。
*   **分层装配管理**：灵活的分组功能支持分层装配管理，便于管理复杂的项目结构。
*   **动态工作平面**：支持动态工作平面，能够根据当前操作或选定的几何体自动调整，适应不同角度的设计需求。
*   **高级 3D 视口**：具备丰富的摄像机控制（平移、旋转、缩放），支持多角度观察模型，提供透视和正交两种投影模式。
*   **摄像机位置记忆**：能够记忆摄像机位置，方便用户快速切换不同视角，提高工作效率。

### 2.7 本地化支持

*   **多语言界面**：内置国际化（i18n）支持，目前提供中文和英文界面，并欢迎社区贡献其他语言包，以服务全球用户。

![Chili3D 界面示例](output/chili3d/media/media_1.png)

## 3. 技术亮点

Chili3D的技术栈是其能够实现桌面级性能和体验的关键，它巧妙地结合了前端Web技术与高性能的3D几何内核，展现了Web工程的最新进展。

*   **核心技术栈概览**:
    *   **CAD 几何内核**: OpenCascade (通过WebAssembly)
    *   **3D 渲染**: Three.js
    *   **前端语言**: TypeScript
    *   **构建工具**: Rspack
    *   **测试框架**: Jest

*   **OpenCascade (OCCT) 与 WebAssembly 的突破性结合**: 这是Chili3D最突出的技术创新。OpenCascade Technology (OCCT) 是一个业界领先的B-Rep（边界表示）几何建模内核，广泛应用于高端的专业CAD软件中。Chili3D通过将OCCT编译为WebAssembly (WASM) 模块，成功地将高性能、复杂的几何计算能力直接带入Web浏览器。WebAssembly提供了近乎原生的执行速度，这使得Chili3D能够在客户端（用户浏览器）本地进行实时的布尔运算、曲面建模、参数化编辑等CAD核心操作，而无需依赖后端服务器进行繁重的计算。这一架构显著提升了用户体验流畅性，降低了服务器负载，并保障了数据隐私。

*   **Three.js 驱动的交互式 3D 渲染**: Three.js 是一个成熟且流行的JavaScript 3D库，负责Chili3D的3D场景渲染和交互可视化。它能够高效地将OCCT计算出的复杂几何模型转化为可在Web浏览器中实时、交互式显示的视觉效果。Three.js 提供的丰富功能（如材质、光照、后处理效果、摄像机控制、拾取选择等）使得Chili3D能够提供高质量的视觉呈现和流畅的用户交互体验。

*   **TypeScript 的强类型与可维护性**: 项目采用TypeScript进行开发，为复杂的CAD应用提供了强大的类型安全保障。这不仅提升了开发效率，减少了运行时错误，还极大地增强了代码的可读性和长期可维护性，对于开源项目尤为重要。

*   **Rspack 优化的构建流程**: Rspack是一个专为Web应用设计的高性能构建工具，它能有效优化Chili3D项目的打包效率，缩短开发周期，并提升最终部署的性能和加载速度，确保用户获得快速响应的应用体验。

*   **完全浏览器内运行的无缝体验**: Chili3D的核心优势之一是其真正的“零安装”特性。用户无需下载任何插件或进行本地安装，只需一个现代的Web浏览器即可立即开始设计。这种便捷性极大地简化了部署和使用流程，特别适合快速原型、在线协作、远程教育和公共工作站等场景。

## 4. 应用场景与价值

Chili3D的出现，为多个领域带来了独特的价值和无限可能，赋能更广泛的用户群体：

*   **教育与学习平台**: 对于CAD初学者、工程设计专业的学生以及希望学习3D建模的爱好者，Chili3D提供了一个易于上手、无需昂贵软件授权的实践平台。学生可以随时随地进行3D建模练习，深化对几何、设计原则和工程概念的理解。
*   **快速原型与概念验证**: 设计师和工程师可以利用Chili3D进行快速的概念设计和原型迭代。其高效建模能力和在线共享特性，有助于在项目早期阶段迅速验证想法、收集反馈并进行敏捷开发。
*   **3D 打印与创客社区**: 3D打印爱好者可以直接在浏览器中设计可打印的模型，并通过支持标准格式（如STEP）进行导入导出，简化了从设计、修改到最终3D打印的工作流程，降低了技术门槛。
*   **在线协作与远程办公**: Chili3D的Web原生特性使其成为理想的在线协作工具。团队成员可以轻松地共享设计文件，进行实时讨论和共同修改，打破物理地理位置的限制，尤其适用于分布式团队或跨部门协作。
*   **跨平台普适性**: 无论用户使用的是Windows、macOS、Linux，还是ChromeOS，只要拥有现代Web浏览器，就能无缝使用Chili3D，这极大地拓宽了用户基础和应用场景，真正实现了“随时随地设计”。

Chili3D通过将OpenCascade与WebAssembly的创新结合，成功地在Web端复刻了专业级CAD软件的核心能力，不仅显著降低了专业3D设计的门槛，也为未来的在线协同设计、云计算驱动的制造以及Web-CAD-SaaS模式提供了坚实的技术基础和无限的想象空间。对于技术爱好者而言，深入研究Chili3D的源码，特别是其WebAssembly与Three.js的集成方式，无疑是一个了解前沿Web技术和高性能图形计算的绝佳案例。