"""
effect: |
    创建现代化的深度洞察卡片动画，采用"Sparkling Insight Card"设计风格，支持闪光特效和优雅的悬浮动画

use_cases:
    - 深度洞察分析结果展示
    - 思维启发和认知增强
    - 学术论文的核心观点提炼
    - 商业分析的关键发现
    - 创新思维的火花展示

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  insights_data:
    type: list
    desc: 深度洞察数据列表，每个元素包含insight_title和insight_description字段
    required: true
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  cards_per_screen:
    type: int
    desc: 每屏显示的卡片数量（建议最多3个）
    required: false
    default: 3
  duration_per_card:
    type: float
    desc: 每张卡片的展示时长（秒）
    required: false
    default: 4.0
  theme:
    type: str
    desc: 主题风格。可选值：'sparkling'（闪光）, 'elegant'（优雅）, 'modern'（现代）
    required: false
    default: sparkling
  narration:
    type: str
    desc: 语音旁白内容
    required: true

dsl_examples:
  - type: animate_deep_insight
    params:
      insights_data:
        - insight_title: "用户流失的真正原因"
          insight_description: "用户流失的真正原因，不是功能缺失，而是体验上的细微摩擦累积。"
        - insight_title: "数据驱动决策的核心"
          insight_description: "有效的数据分析不在于数据量的大小，而在于找到能直接影响业务结果的关键指标。"
        - insight_title: "创新的本质特征"
          insight_description: "真正的创新往往来自于对现有问题的重新定义，而不是对现有解决方案的改进。"
      title: "商业洞察精选"
      theme: "sparkling"
      narration: "让我们一起探索这些闪闪发光的商业洞察，每一个都可能改变我们的思维方式"
  - type: animate_deep_insight
    params:
      insights_data:
        - insight_title: "算法偏见的根源"
          insight_description: "机器学习算法的偏见不是技术问题，而是训练数据中隐含的社会偏见的放大。"
      title: "AI伦理思考"
      cards_per_screen: 1
      duration_per_card: 6.0
      theme: "elegant"
      narration: "深入思考人工智能发展过程中的伦理挑战和社会责任"

notes:
  - 完全复刻HTML版本的"Sparkling Insight Card"设计
  - 柔和的浅蓝色背景营造轻松氛围
  - 白色卡片背景配合圆角和阴影效果
  - 金色✨图标和顶部闪光条特效
  - 轻微倾斜和悬浮动画增强视觉吸引力
  - 支持智能文本换行和响应式布局
"""

import logging

from manim import *

from dsl.v2.core.scene import FeynmanScene
from dsl.v2.themes.theme_manager import get_current_theme, get_theme_value

logger = logging.getLogger(__name__)


def animate_deep_insight(
    scene: FeynmanScene,
    insights_data: list[dict[str, str]],
    title: str,
    cards_per_screen: int = None,  # 改为可选参数，如果不提供则自动计算
    duration_per_card: float = 4.0,
    theme: str = "sparkling",
    narration: str = "让我们来探索这些闪闪发光的深度洞察",
) -> None:
    """
    创建现代化的深度洞察卡片展示动画，采用Sparkling Insight Card设计风格
    自动根据洞察数量决定每屏显示的卡片数量，一次最多3个
    """
    logger.info(f"开始创建深度洞察卡片动画，共{len(insights_data)}个洞察")

    try:
        # 获取当前主题
        theme = get_current_theme()
        
        # 创建标题 - 使用主题字体配置
        title_text = Text(
            title,
            font_size=theme.typography.h2_size,  # 使用主题的h2字体大小
            color=theme.colors.text_primary,     # 使用主题的主要文字颜色
            font=theme.typography.primary_font,  # 使用主题的主要字体
            weight=BOLD,
        ).to_edge(UP, buff=theme.spacing.lg)    # 使用主题间距

        # Handle clear_current_mobject for transition support
        scene.clear_current_mobj(new_mobj=title_text if scene.transition_enabled else None)

        # 设置背景颜色 - 使用主题背景色
        scene.camera.background_color = theme.colors.background

        # 自动决定每屏卡片数量，如果没有指定的话
        if cards_per_screen is None:
            total_insights = len(insights_data)
            if total_insights == 1:
                cards_per_screen = 1
            elif total_insights == 2:
                cards_per_screen = 2
            elif total_insights <= 6:
                cards_per_screen = 3  # 3-6个洞察，每屏显示3个
            else:
                cards_per_screen = 3  # 超过6个，每屏显示3个

        # 确保最多3个卡片
        cards_per_screen = min(cards_per_screen, 3)

        with scene.voiceover(narration) as tracker:  # noqa: F841
            # Handle animation section for transition support
            if not scene.transition_enabled:
                # Normal animation - show title first
                scene.play(Create(title_text), run_time=1.0)
                scene.wait(0.5)
            else:
                scene.add(title_text)

            # 计算需要的屏幕数
            total_screens = (len(insights_data) + cards_per_screen - 1) // cards_per_screen

            for screen_idx in range(total_screens):
                start_idx = screen_idx * cards_per_screen
                end_idx = min(start_idx + cards_per_screen, len(insights_data))
                screen_insights_data = insights_data[start_idx:end_idx]

                # 创建当前屏幕的卡片
                cards = []
                card_group = VGroup()

                for i, insight in enumerate(screen_insights_data):
                    card = create_sparkling_insight_card(
                        insight["insight_title"], insight["insight_description"], len(screen_insights_data), theme
                    )
                    cards.append(card)
                    card_group.add(card)

                # 横向排列卡片 - 使用主题间距系统，整体往上移
                if len(screen_insights_data) == 1:
                    card_group.move_to(ORIGIN + UP * theme.spacing.md)
                else:
                    # 使用主题间距
                    card_group.arrange(RIGHT, buff=theme.spacing.md)
                    card_group.move_to(ORIGIN + UP * theme.spacing.md)

                    # 放宽屏幕边界限制，允许更大的卡片
                    max_width = 14
                    if card_group.get_width() > max_width:
                        card_group.scale_to_fit_width(max_width)

                # 应用依次展现动画
                apply_insight_animation(scene, cards)

                # 等待展示
                scene.wait(duration_per_card)

                # 如果不是最后一屏，清除当前卡片
                if screen_idx < total_screens - 1:
                    scene.play(*[FadeOut(card) for card in cards], run_time=1.0)

            # Create display group with title and cards
            display_group = VGroup(title_text, *cards)
            display_group.arrange(DOWN, buff=0.5)

            # Update current_mobject saving - ensure title is first submobject
            scene.current_mobj = display_group
            scene.save_scene_state("deep_insight", "deep_insight")

    except Exception as e:
        logger.error(f"创建深度洞察卡片动画时出错: {e}")
        # 创建错误提示
        error_text = Text(f"深度洞察卡片创建失败\n错误: {str(e)[:50]}...", font_size=24, color=RED, font="Arial").move_to(
            ORIGIN
        )
        scene.add(error_text)
        scene.current_mobj = error_text


def create_sparkling_insight_card(title: str, description: str, total: int, theme_style: str) -> VGroup:
    """创建主题化的深度洞察卡片，使用当前主题配置"""
    
    # 获取当前主题
    theme = get_current_theme()

    # 根据卡片数量调整尺寸（响应式设计）
    if total == 1:
        card_width, card_height = 8, 6
    elif total == 2:
        card_width, card_height = 8, 6
    else:  # 3个或更多
        card_width, card_height = 6.5, 6

    # 创建卡片背景 - 使用主题表面色和圆角配置
    bg_color = theme.colors.surface
    stroke_color = theme.colors.text_secondary
    shadow_color = theme.colors.text_primary
    shadow_opacity = 0.1 if theme.name == "light" else 0.2

    card_bg = RoundedRectangle(
        corner_radius=theme.components.background_corner_radius,
        width=card_width,
        height=card_height,
        fill_color=bg_color,
        fill_opacity=theme.components.background_opacity,
        stroke_color=stroke_color,
        stroke_width=theme.components.stroke_width * 0.25,  # 较细的边框
        stroke_opacity=0.3,
    )

    # 创建卡片阴影效果 - 使用主题间距
    shadow = card_bg.copy()
    shadow.set_fill(color=shadow_color, opacity=shadow_opacity)
    shadow.set_stroke(width=0)
    shadow.shift(DOWN * theme.spacing.xs * 0.5 + RIGHT * theme.spacing.xs * 0.5)

    # 创建顶部彩色强调条 - 使用主题配置
    accent_height = theme.spacing.xs * 0.8  # 使用主题最小间距作为强调条高度
    top_accent = Rectangle(
        width=card_width,
        height=accent_height,
        fill_color=theme.colors.cyclic_colors[0],  # 使用主题循环颜色
        fill_opacity=1.0,
        stroke_width=0,
    )
    top_accent.move_to(card_bg.get_top())

    # 创建洞察图标 - 使用主题配置
    icon_radius = theme.spacing.md * 0.875  # 基于主题间距计算图标大小
    insight_circle = Circle(
        radius=icon_radius,
        fill_color=theme.colors.cyclic_colors[0],  # 使用主题循环颜色
        fill_opacity=1.0,
        stroke_width=0,
    )
    insight_label = Text(
        "💡", 
        font_size=theme.typography.body_size, 
        color=theme.colors.surface,  # 使用对比色确保可见性
        font=theme.typography.primary_font, 
        weight=BOLD
    )
    insight_icon = VGroup(insight_circle, insight_label)

    # 移除"深度洞察"标签，保持简洁设计

    # 智能换行函数
    def smart_wrap_insight_text(text: str, max_width: float, font_size: int, max_lines: int = 3) -> str:
        """为洞察内容进行智能换行"""
        char_width_ratio = font_size * 0.65
        chars_per_line = int(max_width / char_width_ratio * 1.8)
        chars_per_line = max(15, min(chars_per_line, 40))

        def smart_break(text, width):
            if len(text) <= width:
                return [text]

            lines = []
            remaining = text

            while remaining and len(lines) < max_lines - 1:
                if len(remaining) <= width:
                    lines.append(remaining)
                    remaining = ""  # 修复：将remaining置空，避免重复添加
                    break

                # 寻找合适的断点
                break_point = width
                for i in range(width, max(width // 2, 10), -1):
                    if i < len(remaining) and remaining[i] in "，。！？；：、":
                        break_point = i + 1
                        break
                    elif i < len(remaining) and remaining[i] == " ":
                        break_point = i
                        break

                lines.append(remaining[:break_point].strip())
                remaining = remaining[break_point:].strip()

            if remaining and len(lines) < max_lines:
                if len(remaining) <= width * 1.2:
                    lines.append(remaining)
                else:
                    lines.append(remaining[: width - 3] + "...")
            elif remaining:
                if lines:
                    lines[-1] = lines[-1][: width - 3] + "..."

            return lines

        wrapped_lines = smart_break(text, chars_per_line)
        return "\n".join(wrapped_lines)

    # 文字大小调整 - 使用主题字体系统
    if total == 1:
        title_font_size = theme.typography.h3_size      # 大标题
        desc_font_size = theme.typography.h4_size       # 中等标题
        text_width = card_width - theme.spacing.xl
    elif total == 2:
        title_font_size = theme.typography.h3_size      # 大标题
        desc_font_size = theme.typography.h4_size       # 中等标题
        text_width = card_width - theme.spacing.xl
    else:
        title_font_size = theme.typography.h4_size      # 中等标题
        desc_font_size = theme.typography.body_size     # 正文
        text_width = card_width - theme.spacing.lg

    # 创建洞察标题文本 - 使用黄色字体
    wrapped_title = smart_wrap_insight_text(title, text_width, title_font_size, max_lines=2)

    title_text = Text(
        wrapped_title,
        font_size=title_font_size,
        color=YELLOW,  # 使用黄色字体
        font=theme.typography.primary_font,
        weight=BOLD,
        line_spacing=theme.typography.line_spacing,
    )
    title_text.set_max_width(text_width)

    # 创建标题和正文之间的短分割线 - 使用主题颜色
    divider_width = text_width * 0.6  # 分割线长度为文本宽度的60%
    divider = Rectangle(
        width=divider_width,
        height=0.03,  # 细线条
        fill_color=theme.colors.text_secondary,  # 使用主题次要文字颜色
        fill_opacity=0.8,
        stroke_width=0,
    )

    # 创建描述文本 - 使用纯白色字体
    wrapped_description = smart_wrap_insight_text(description, text_width, desc_font_size, max_lines=4)

    description_text = Text(
        wrapped_description,
        font_size=desc_font_size,
        color=WHITE,  # 使用纯白色字体
        font=theme.typography.primary_font,
        line_spacing=theme.typography.line_spacing * 1.3,  # 稍微增加行间距
    )
    description_text.set_max_width(text_width)

    # 垂直排列内容 - 使用主题间距（移除subtitle）
    content_group = VGroup(insight_icon, title_text, divider, description_text).arrange(
        DOWN, buff=theme.spacing.lg, aligned_edge=ORIGIN
    )

    # 内容居中定位
    content_group.move_to(card_bg.get_center())

    # 创建悬停效果 - 使用主题配置
    hover_glow = card_bg.copy()
    hover_glow.set_fill(opacity=0)
    hover_glow.set_stroke(
        color=theme.colors.cyclic_colors[0], 
        width=theme.components.stroke_width * 0.5, 
        opacity=0.4
    )

    # 组合所有元素
    card = VGroup(shadow, card_bg, top_accent, hover_glow, content_group)

    return card


def apply_insight_animation(scene: FeynmanScene, cards: list[VGroup]) -> None:
    """应用主题化的依次展现动画 - 每次只显示一张卡片"""
    # 获取当前主题
    theme = get_current_theme()
    
    # 获取动画速率函数
    rate_func_map = {
        "smooth": smooth,
        "ease_in": rush_into,
        "ease_out": rush_from,
        "ease_in_out": smooth,
        "linear": linear,
    }
    rate_func = rate_func_map.get(theme.animation.default_rate_func, smooth)
    
    if len(cards) == 1:
        # 单张卡片的简单动画
        card = cards[0]
        card.shift(DOWN * theme.spacing.md + LEFT * 2)
        card.set_opacity(0)
        
        scene.play(
            card.animate.shift(UP * theme.spacing.md + RIGHT * 2).set_opacity(1),
            run_time=theme.animation.fade_duration,
            rate_func=rate_func,
        )
        
        # 轻微的悬停效果
        scene.play(
            card.animate.shift(UP * theme.spacing.xs), 
            run_time=theme.animation.highlight_duration, 
            rate_func=there_and_back
        )
    else:
        # 多张卡片的依次展现动画 - 每次只显示一张
        
        # 1. 记录所有卡片的最终正确位置（横向排列）
        final_positions = []
        for card in cards:
            final_positions.append(card.get_center())
        
        # 2. 初始化：所有卡片设置到正确位置但都隐藏
        for i, (card, final_pos) in enumerate(zip(cards, final_positions)):
            card.move_to(final_pos)
            card.set_opacity(0)  # 初始全部隐藏
        
        # 3. 依次显示每张卡片：出现 -> 放大 -> 停留 -> 缩回
        for i, (card, final_pos) in enumerate(zip(cards, final_positions)):
            # 先让卡片出现在正确位置
            scene.play(
                card.animate.set_opacity(1),
                run_time=theme.animation.fade_duration * 0.3,
                rate_func=rate_func,
            )
            
            # 短暂停顿后放大到屏幕中心
            scene.wait(0.2)
            scene.play(
                card.animate.scale(1.5).move_to(ORIGIN),
                run_time=theme.animation.default_duration * 0.4,
                rate_func=rate_func,
            )
            
            # 停留展示
            scene.wait(2)
            
            # 缩回到原位置和大小
            scene.play(
                card.animate.scale(1/1.5).move_to(final_pos),
                run_time=theme.animation.default_duration * 0.4,
                rate_func=rate_func,
            )
            
            # 卡片间的停顿（但保持可见）
            if i < len(cards) - 1:
                scene.wait(0.3)
        
        # 4. 最后给所有可见的卡片添加轻微的悬停效果
        scene.wait(0.5)
        # 对所有卡片应用悬停效果
        for card in cards:
            scene.play(
                card.animate.shift(UP * theme.spacing.xs), 
                run_time=theme.animation.highlight_duration, 
                rate_func=there_and_back
            )
