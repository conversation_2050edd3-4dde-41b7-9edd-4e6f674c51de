"""
effect: |
    创建交互式思维导图，支持节点聚焦、子树展开和动态布局调整。

use_cases:
    - 展示知识结构和概念层次关系
    - 教学中的概念图解和思路梳理
    - 项目规划和任务分解可视化
    - 复杂信息的结构化展示

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  data_source:
    type: str | dict
    desc: 思维导图数据源，可以是JSON文件路径或字典数据. 如果复用之前已创建的mindmap（需要提供id），可以不设置 data_source
    default: None
  layout_style:
    type: str
    desc: 布局样式。可选值：'balance'（平衡布局）, 'left_to_right'（从左到右）, 'right_to_left'（从右到左）
    default: balance
  max_depth:
    type: int
    desc: 显示的最大层级深度
    default: 3
  focus_sequence:
    type: list[str]
    desc: 按顺序聚焦的节点文本列表，用于引导观众注意力
    default: None
  narration:
    type: str
    desc: 在思维导图显示时播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_mindmap
    params:
      title: 人工智能知识体系
      data_source: |
        {
          "标题": "人工智能",
          "子章节": [
            {
              "标题": "机器学习",
              "子章节": [
                {"标题": "监督学习"},
                {"标题": "无监督学习"},
                {"标题": "强化学习"}
              ]
            },
            {
              "标题": "深度学习",
              "子章节": [
                {"标题": "神经网络"},
                {"标题": "卷积网络"},
                {"标题": "循环网络"}
              ]
            }
          ]
        }
      layout_style: balance
      max_depth: 3
      id: AI_mindmap
      focus_sequence: ["人工智能", "机器学习", "深度学习"]
      narration: 让我们通过这个思维导图来了解人工智能的主要分支和技术体系。
  - type: animate_mindmap
    params:
      title: 机器学习深入分析
      id: AI_mindmap
      focus_sequence: ["机器学习", "监督学习", "无监督学习"]
      narration: 让我们更深入地了解一下机器学习的两个主要分支，监督学习和无监督学习。
  - type: animate_mindmap
    params:
      title: 项目结构图
      data_source: assets/mindmap_data.json
      layout_style: left_to_right
      max_depth: 2
      narration: 这是一个从文件加载的思维导图结构。

notes:
  - 数据源支持JSON文件路径或直接传入字典格式
  - focus_sequence参数可以创建引导式的节点聚焦动画
  - 思维导图会自动调整布局以适应屏幕尺寸
  - 支持中文文本和自动换行
"""
from __future__ import annotations

import json
import os
import queue
from collections import defaultdict
from typing import TYPE_CHECKING

import numpy as np
from loguru import logger
from manim import *

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene
from utils.format import wrap_text

################################################################################
# 常量配置
################################################################################


class MindMapConfig:
    """思维导图配置常量"""

    DEPTH_WITH_RECTANGLE = 3
    DEFAULT_FONT_SIZE = 24
    ROOT_FONT_SIZE = 36
    DEFAULT_FONT = "Microsoft YaHei"
    CORNER_RADIUS_FACTOR = 0.2
    PADDING = MED_LARGE_BUFF
    CURVE_FACTOR = 0.2
    FRAME_SCALE_FACTOR = 0.9

    # 文本宽度阈值配置
    TEXT_WIDTH_THRESHOLDS = [(5, 20), (10, 40), (15, 60), (20, 80)]
    DEFAULT_TEXT_WIDTH = 100


################################################################################
# 数据结构与辅助函数
################################################################################


class Node:
    """
    表示思维导图中的节点
    """

    def __init__(self, data: str, children: list[Node] | None = None):
        self.data: str = data
        self.children: list[Node] = children if children is not None else []
        self.parent: Node | None = None
        for child in self.children:
            child.parent = self
        self.depth: int = 0
        self.pos: np.ndarray = np.array([0.0, 0.0, 0.0])
        self.manim_object: Mobject | None = None
        self.line_object: Mobject | None = None
        self.direction: str = "right"
        self.color: ManimColor | None = None

    def add_child(self, child: Node) -> None:
        """
        为当前节点添加子节点，同时建立双向关联
        """
        child.parent = self
        self.children.append(child)


def assign_levels(node: Node, depth: int = 0) -> None:
    """
    递归设置各节点的深度值
    """
    node.depth = depth
    for child in node.children:
        assign_levels(child, depth + 1)


def get_all_nodes(node: Node) -> list[Node]:
    """
    递归收集树中所有节点
    """
    nodes = [node]
    for child in node.children:
        nodes.extend(get_all_nodes(child))
    return nodes


def assign_direction(node: Node, direction: str) -> None:
    """
    递归为子树中的每个节点设置方向
    """
    node.direction = direction
    for child in node.children:
        assign_direction(child, direction)


################################################################################
# 文本处理工具类
################################################################################


class TextUtils:
    """文本处理工具类"""

    @staticmethod
    def get_target_text_width(node_num: int) -> int:
        """根据当前层级的节点数确定文本换行宽度"""
        for threshold, width in MindMapConfig.TEXT_WIDTH_THRESHOLDS:
            if node_num < threshold:
                return width
        return MindMapConfig.DEFAULT_TEXT_WIDTH

    @staticmethod
    def create_wrapped_text(text: str, width: int) -> str:
        """根据指定宽度将文本内容换行"""
        return "\n".join(wrap_text(text, width))


################################################################################
# 几何计算工具类
################################################################################


class GeometryUtils:
    """几何计算工具类"""

    @staticmethod
    def get_tree_bounding_box(nodes: list[Node]) -> tuple[float, float, float, float]:
        """返回节点列表构成的包围盒（min_x, max_x, min_y, max_y）"""
        valid_nodes = [node for node in nodes if node.manim_object]
        if not valid_nodes:
            return 0, 0, 0, 0

        lefts = [node.manim_object.get_left()[0] for node in valid_nodes]
        rights = [node.manim_object.get_right()[0] for node in valid_nodes]
        bottoms = [node.manim_object.get_bottom()[1] for node in valid_nodes]
        tops = [node.manim_object.get_top()[1] for node in valid_nodes]

        return min(lefts), max(rights), min(bottoms), max(tops)

    @staticmethod
    def calculate_focus_transform(
        nodes: list[Node], frame_width: float, frame_height: float
    ) -> tuple[np.ndarray, float]:
        """计算聚焦变换的平移和缩放参数"""
        min_x, max_x, min_y, max_y = GeometryUtils.get_tree_bounding_box(nodes)

        tree_center = np.array([(max_x + min_x) / 2, (max_y + min_y) / 2, 0])
        scene_center = np.array([0, 0, 0])
        translation = scene_center - tree_center

        tree_width = max_x - min_x
        tree_height = max_y - min_y

        scale_factor = min(
            MindMapConfig.FRAME_SCALE_FACTOR * frame_width / tree_width,
            MindMapConfig.FRAME_SCALE_FACTOR * frame_height / tree_height,
        )

        if len(nodes) < 3:
            # HACK: 如果子树节点数太少，不要放大太多
            ref = Text("中", font_size=96)
            scale_factor = min(scale_factor, max(1, ref.height / tree_height))

        return translation, scale_factor


################################################################################
# 布局算法工具类
################################################################################


class LayoutUtils:
    """布局算法工具类"""

    @staticmethod
    def layout_subtree_vertical(node: Node, y_offset: float, sibling_distance: float) -> tuple[float, float]:
        """对子树进行垂直布局，返回最终 y 轴偏移值和当前节点的 y 坐标"""
        if not node.children:
            node.pos[1] = y_offset
            return y_offset + sibling_distance, node.pos[1]

        child_ys = []
        current_offset = y_offset
        for child in node.children:
            current_offset, child_y = LayoutUtils.layout_subtree_vertical(child, current_offset, sibling_distance)
            child_ys.append(child_y)

        node_y = (min(child_ys) + max(child_ys)) / 2
        node.pos[1] = node_y
        return current_offset, node_y


def count_leaves(node: Node) -> int:
    """
    递归计算节点子树中的叶子节点数
    """
    if not node.children:
        return 1
    return sum(count_leaves(child) for child in node.children)


def get_left_and_right_subtree_nodes(root: Node, layout: str) -> tuple[list[Node], list[Node]]:
    """
    将根节点的子树依据布局方式划分为左侧和右侧节点集合
    """
    if layout == "left_to_right":
        left_nodes = []
        right_nodes = sum([get_all_nodes(child) for child in root.children], [])
    elif layout == "right_to_left":
        left_nodes = sum([get_all_nodes(child) for child in root.children], [])
        right_nodes = []
    else:  # balance 布局：前半节点放右侧，后一半放左侧
        total_leaves = sum(count_leaves(child) for child in root.children)
        half_leaves = total_leaves / 2.0
        cumulative = 0
        split_idx = 0
        for i, child in enumerate(root.children):
            cumulative += count_leaves(child)
            if cumulative >= half_leaves:
                split_idx = i + 1
                break
        # 按原逻辑，前半部分放右侧，后半部分放左侧
        right_children = root.children[:split_idx]
        left_children = root.children[split_idx:]
        left_nodes = sum([get_all_nodes(child) for child in left_children], [])
        right_nodes = sum([get_all_nodes(child) for child in right_children], [])
    return left_nodes, right_nodes


################################################################################
# 布局类封装
################################################################################


class MindMapLayout:
    def __init__(
        self,
        scene: FeynmanScene,
        root: Node,
        level_distance: float = 4.0,
        sibling_distance: float = 1.0,
        layout: str = "left_to_right",
    ):
        self.scene = scene
        self.root = root
        self.level_distance = level_distance
        self.sibling_distance = sibling_distance
        assign_levels(self.root)
        self.all_nodes: list[Node] = get_all_nodes(self.root)
        self.text_colors = [PINK, MAROON, PURPLE, BLUE, RED, TEAL, TEAL_E, GREEN, RED_D, BLUE_E, MAROON_B]
        # random.shuffle(self.text_colors)
        self.colors = [
            (rgb_to_color([0.91, 0.94, 0.98]), rgb_to_color([0.2, 0.3, 0.4])),  # 淡蓝色背景,深蓝色文字
            (rgb_to_color([0.95, 0.90, 0.93]), rgb_to_color([0.4, 0.2, 0.3])),  # 淡粉色背景,深红色文字
            (rgb_to_color([0.90, 0.95, 0.91]), rgb_to_color([0.2, 0.4, 0.3])),  # 淡绿色背景,深绿色文字
            (rgb_to_color([0.95, 0.93, 0.88]), rgb_to_color([0.4, 0.3, 0.2])),  # 淡黄色背景,深棕色文字
            (rgb_to_color([0.92, 0.90, 0.95]), rgb_to_color([0.3, 0.2, 0.4])),  # 淡紫色背景,深紫色文字
            (rgb_to_color([0.89, 0.93, 0.95]), rgb_to_color([0.2, 0.3, 0.4])),  # 淡青色背景,深蓝色文字
            (rgb_to_color([0.98, 0.94, 0.91]), rgb_to_color([0.35, 0.25, 0.25])),  # 淡橙色背景,深橙色文字
            (rgb_to_color([0.91, 0.95, 0.96]), rgb_to_color([0.25, 0.3, 0.35])),  # 淡薄荷色背景,深蓝灰色文字
        ]
        self.left_subtree_nodes, self.right_subtree_nodes = get_left_and_right_subtree_nodes(self.root, layout)
        for node in self.left_subtree_nodes:
            node.direction = "left"
        for node in self.right_subtree_nodes:
            node.direction = "right"
        self.visited = {}
        self.color_index = 0

    def create_manim_objects(self, nodes: list[Node], scale: float = 1.0) -> None:
        """为每个节点创建 Manim 文本对象并进行换行设置"""
        level_nodes: dict[int, list[Node]] = defaultdict(list)
        for node in nodes:
            level_nodes[node.depth].append(node)

        for node in nodes:
            current_level_count = len(level_nodes[node.depth])
            target_width = TextUtils.get_target_text_width(current_level_count)
            wrapped_text = TextUtils.create_wrapped_text(node.data, target_width)

            # 确定颜色
            if node.parent and node.parent.depth > 0 and node.parent.color is not None:
                color = node.parent.color
                node.color = color
                text_color = self._get_text_color_for_background(color)
            else:
                color, text_color = self.colors[self.color_index % len(self.colors)]
                node.color = color
                self.color_index += 1

            # 创建节点对象
            if node.depth < MindMapConfig.DEPTH_WITH_RECTANGLE:
                node.manim_object = self._create_rectangle_node(wrapped_text, color, text_color, scale)
            else:
                color = self.text_colors[node.depth]
                node.manim_object = self._create_underlined_node(wrapped_text, color, text_color, scale)

    def _create_rectangle_node(self, text: str, bg_color, text_color, scale: float) -> VGroup:
        """创建带矩形背景的节点"""
        text_obj = Paragraph(
            text,
            font_size=MindMapConfig.DEFAULT_FONT_SIZE,
            color=text_color,
            alignment="center",
            font=MindMapConfig.DEFAULT_FONT,
            weight=BOLD,
        ).scale(scale)

        rect = RoundedRectangle(
            corner_radius=MindMapConfig.CORNER_RADIUS_FACTOR
            * min(text_obj.width + MindMapConfig.PADDING, text_obj.height + MindMapConfig.PADDING),
            width=text_obj.width + MindMapConfig.PADDING,
            height=text_obj.height + MindMapConfig.PADDING,
            fill_color=bg_color,
            fill_opacity=1,
        )

        group = VGroup(rect, text_obj)
        group.move_to(text_obj.get_center())
        return group

    def _create_underlined_node(self, text: str, color: str, text_color: str, scale: float) -> VGroup:
        """创建带下划线的节点"""
        text_obj = Paragraph(
            text,
            font_size=MindMapConfig.DEFAULT_FONT_SIZE,
            color=text_color,
            alignment="center",
            font=MindMapConfig.DEFAULT_FONT,
            weight=NORMAL,
        ).scale(scale)

        underline = Underline(text_obj).set_color(color).set_stroke(width=2)
        group = VGroup(underline, text_obj)
        group.move_to(text_obj.get_center())
        return group

    def _get_text_color_for_background(self, bg_color) -> ManimColor:
        """根据背景色获取合适的文本颜色"""
        # 简化实现，实际可以根据背景色亮度计算
        # TODO: 实现基于背景色亮度的文本颜色选择
        _ = bg_color  # 避免未使用参数警告
        return BLACK

    def horizontal_layout(self) -> None:
        """
        根据节点深度与方向设置水平坐标
        """
        for node in self.all_nodes:
            offset = self.level_distance * node.depth
            node.pos[0] = offset * (-1 if node.direction == "left" else 1)

    def vertical_layout(self) -> None:
        """调整左右子树的垂直分布，使新根的 y 坐标处于左右子树中心的均值位置"""
        root_y = self.root.pos[1]

        def get_children_center(children: list[Node]) -> float:
            if not children:
                return root_y
            dummy = Node("dummy", children=children)
            dummy.pos = np.copy(self.root.pos)
            _, center = LayoutUtils.layout_subtree_vertical(dummy, root_y, self.sibling_distance)
            return center

        left_children = [child for child in self.root.children if child.direction == "left"]
        right_children = [child for child in self.root.children if child.direction == "right"]

        left_center = get_children_center(left_children)
        right_center = get_children_center(right_children)
        new_root_y = (left_center + right_center) / 2
        self.root.pos[1] = new_root_y

        def shift_subtree(node: Node, delta: float) -> None:
            node.pos[1] += delta
            for child in node.children:
                shift_subtree(child, delta)

        for children, center in [(left_children, left_center), (right_children, right_center)]:
            delta = new_root_y - center
            for child in children:
                shift_subtree(child, delta)

        # 确保所有直接子节点的 parent 指针指向根节点
        for child in self.root.children:
            child.parent = self.root

    def align_mobject_to_node(self, node: Node) -> None:
        """
        使节点对应的 Manim 对象移动到指定位置，并根据父节点方向调整对齐方式
        """
        if node.manim_object:
            node.manim_object.move_to(node.pos)
            if node.parent and node.manim_object:
                alignment = LEFT if node.direction == "right" else RIGHT
                node.manim_object.align_to(node.pos, alignment)

    def align_mobjects(self) -> None:
        """
        对所有节点进行对齐操作
        """
        for node in self.all_nodes:
            self.align_mobject_to_node(node)

    def fix_overlap(self) -> None:
        """
        调整节点重叠：每层内进行缩放并保持父子节点之间左右不重叠
        """
        level_groups: dict[int, dict[str, list[Node]]] = defaultdict(lambda: {"left": [], "right": []})
        for node in self.all_nodes:
            level_groups[node.depth][node.direction].append(node)

        def adjust_node_overlap(node: Node) -> None:
            if node.parent and node.manim_object and node.parent.manim_object:
                parent_obj = node.parent.manim_object
                if node.direction == "right":
                    if node.manim_object.get_left()[0] < parent_obj.get_right()[0] + LARGE_BUFF:
                        node.pos[0] = parent_obj.get_right()[0] + LARGE_BUFF
                        node.manim_object.align_to(node.pos, LEFT)
                else:
                    if node.manim_object.get_right()[0] > parent_obj.get_left()[0] - LARGE_BUFF:
                        node.pos[0] = parent_obj.get_left()[0] - LARGE_BUFF
                        node.manim_object.align_to(node.pos, RIGHT)

        def scale_nodes(nodes: list[Node]) -> None:
            if len(nodes) < 2:
                return
            scale_factor = 1.0
            for lower, upper in zip(nodes, nodes[1:]):
                gap = upper.pos[1] - lower.pos[1]
                candidate = 2 * gap / (lower.manim_object.height + upper.manim_object.height + self.sibling_distance)
                scale_factor = min(scale_factor, candidate)
            if scale_factor < 1.0:
                for node in nodes:
                    node.manim_object.scale(scale_factor)

        max_depth = max(level_groups.keys(), default=0)
        for depth in range(1, max_depth + 1):
            for direction in ("left", "right"):
                nodes = level_groups[depth][direction]
                if nodes:
                    nodes.sort(key=lambda n: n.pos[1])
                    scale_nodes(nodes)
                    for node in nodes:
                        self.align_mobject_to_node(node)
                        adjust_node_overlap(node)

    def play_animation(
        self,
        scene: FeynmanScene,
        animations,
        run_time: float = 1,
        script: str = None,
        tts_duration: float = 0,
    ) -> None:
        """播放动画的统一接口"""
        if script:
            with scene.voiceover(script):
                scene.play(animations, run_time=run_time)
        else:
            scene.play(animations, run_time=run_time)
            scene.wait(max(0, tts_duration - run_time) + 0.1)

    def _find_node_by_text(self, text: str) -> Node:
        """根据文本查找节点"""
        node = next((n for n in self.all_nodes if n.data == text), None)
        if node is None:
            raise ValueError(f"Node with text '{text}' not found.")
        return node

    def _analyze_node_hierarchy(self, node: Node) -> tuple[Node | None, Node | None]:
        """分析节点层次结构，返回父节点和子节点"""
        if node.depth == 1:
            return node, None
        elif node.depth >= 2:
            current = node
            child_node = None
            parent_node = None

            while current:
                if current.depth == 2:
                    child_node = current
                elif current.depth == 1:
                    parent_node = current
                    break
                current = current.parent

            return parent_node, child_node
        return None, None

    def _focus_parent_node(
        self, parent_node: Node, pre_text: str, script: str, scene: FeynmanScene, run_time: float, keywords: list
    ) -> list:
        """聚焦父节点"""
        if pre_text != "-1":
            self.focus(node=parent_node.parent, script=script, scene=scene, run_time=1, tts_duration=0)
        self.focus(node=parent_node, script=script, scene=scene, run_time=run_time, tts_duration=0)
        return self.display_keywords(keywords=keywords)

    def display_keywords(self, keywords: list = None) -> list:
        """显示关键词（占位方法）"""
        if keywords is None:
            keywords = []
        # TODO: 实现关键词显示逻辑
        return []

    def _create_root_node_object(self) -> None:
        """创建根节点的 Manim 对象"""
        color, text_color = self.colors[self.color_index % len(self.colors)]
        self.color_index += 1

        text_obj = Paragraph(
            TextUtils.create_wrapped_text(self.root.data, 15),
            font_size=MindMapConfig.ROOT_FONT_SIZE,
            color=text_color,
            alignment="center",
            font=MindMapConfig.DEFAULT_FONT,
            weight=BOLD,
        )

        rect = RoundedRectangle(
            corner_radius=MindMapConfig.CORNER_RADIUS_FACTOR
            * min(text_obj.width + MindMapConfig.PADDING, text_obj.height + MindMapConfig.PADDING),
            width=text_obj.width + MindMapConfig.PADDING,
            height=text_obj.height + MindMapConfig.PADDING,
            fill_color=color,
            fill_opacity=1,
        )

        group = VGroup(rect, text_obj)
        group.move_to(text_obj.get_center())
        self.root.manim_object = group
        self.root.color = color

    def _setup_child_visibility(self, parent_node: Node, child_list: dict[Node, int], display_level: int) -> None:
        """设置子节点的可见性"""
        # 隐藏所有节点
        NodeVisibilityManager.hide_all_nodes(self.all_nodes)

        # 显示父节点
        if parent_node.manim_object:
            parent_node.manim_object.set_opacity(1)
            if parent_node.line_object:
                parent_node.line_object.set_stroke(opacity=1)

        # 显示相关子节点
        if child_list:
            self._show_children_by_status(child_list, display_level)

    def _show_children_by_status(self, child_list: dict[Node, int], display_level: int) -> None:
        """根据状态显示子节点"""
        node_queue = queue.Queue()

        for child, status in child_list.items():
            if child.depth <= display_level:
                node_queue.put((child, status))

        while not node_queue.empty():
            node, status = node_queue.get()
            opacity = 1.0 if status >= 1 else 0.1

            if node.manim_object:
                node.manim_object.set_opacity(opacity)
            if node.line_object:
                node.line_object.set_stroke(opacity=opacity)

            # 添加子节点到队列
            for child in node.children:
                if child.depth <= display_level:
                    node_queue.put((child, status))

    def _execute_focus_animation(
        self, node: Node, scene: FeynmanScene, run_time: float, script: str, tts_duration: float
    ) -> None:
        """执行聚焦动画"""
        print(f"focus: {node.data}, script {script}")

        subtree_nodes = get_all_nodes(node)
        translation, scale_factor = GeometryUtils.calculate_focus_transform(
            subtree_nodes, scene.camera.frame_width, scene.camera.frame_height
        )

        all_group = VGroup(*[n.manim_object for n in self.all_nodes if n.manim_object])
        all_group.add(*[n.line_object for n in self.all_nodes if n.line_object])

        tree_center = np.array(
            [
                (
                    GeometryUtils.get_tree_bounding_box(subtree_nodes)[0]
                    + GeometryUtils.get_tree_bounding_box(subtree_nodes)[1]
                )
                / 2,
                (
                    GeometryUtils.get_tree_bounding_box(subtree_nodes)[2]
                    + GeometryUtils.get_tree_bounding_box(subtree_nodes)[3]
                )
                / 2,
                0,
            ]
        )

        animation = all_group.animate.scale(scale_factor, about_point=tree_center).shift(translation)
        self.play_animation(scene, animation, run_time=run_time, script=script, tts_duration=tts_duration)
        scene.wait(0.1)

    def get_level(self, cur_text: str):
        cur_node = next((n for n in self.all_nodes if n.data == cur_text), None)
        if cur_node is None:
            raise ValueError(f"Node with text '{cur_text}' not found.")
        return cur_node.depth

    def focus(
        self,
        node: Node = None,
        text: str = None,
        scene: FeynmanScene | None = None,
        first_call: bool = False,
        display_level: int = 4,
        run_time: float = 1,
        script: str = None,
        tts_duration: float = 0,
    ) -> None:
        """将指定节点及其子树置于屏幕中心，并通过平移和缩放动画实现焦点切换效果"""
        # 参数验证和节点查找
        target_node = self._resolve_target_node(node, text)
        scene = scene or self.scene

        # 设置节点可见性
        # if display_level >= 0:
        # self._setup_focus_visibility(target_node, display_level)

        print(f"focus: {target_node.data}, script {script}")

        # 执行聚焦动画
        self._execute_focus_animation(
            target_node,
            scene,
            run_time,
            is_first_call=first_call,
            display_level=display_level,
            script=script,
            tts_duration=tts_duration,
        )

        scene.wait(0.1)

    def _resolve_target_node(self, node: Node, text: str) -> Node:
        """解析目标节点"""
        if node is None and text is None:
            raise ValueError("Either node or text should be provided.")

        if node is None:
            node = next((n for n in self.all_nodes if n.data == text), None)
            if node is None:
                raise ValueError(f"Node with text '{text}' not found.")

        return node

    def _setup_focus_visibility(self, target_node: Node, display_level: int) -> None:
        """设置聚焦时的节点可见性"""
        # 隐藏所有节点
        NodeVisibilityManager.hide_all_nodes(self.all_nodes)

        # 显示路径节点
        path_nodes = NodeVisibilityManager.get_path_to_root(target_node)
        NodeVisibilityManager.show_nodes_with_opacity(path_nodes, 1.0)

        # 显示目标节点的子节点
        subtree_nodes = NodeVisibilityManager.collect_subtree_nodes(target_node, display_level)
        NodeVisibilityManager.show_nodes_with_opacity(subtree_nodes, 1.0)

    def _execute_focus_animation(
        self,
        node: Node,
        scene: FeynmanScene,
        run_time: float,
        is_first_call: bool = False,
        display_level: int = 4,
        script: str = None,
        tts_duration: float = None,
    ) -> None:
        """执行聚焦动画 - 统一的动画执行方法"""
        # 计算变换参数（两种动画都需要）
        subtree_nodes = get_all_nodes(node)
        translation, scale_factor = GeometryUtils.calculate_focus_transform(
            subtree_nodes, scene.camera.frame_width, scene.camera.frame_height
        )
        tree_center = self._calculate_tree_center(subtree_nodes)

        if is_first_call:
            self._execute_layered_animation(
                node, scene, tree_center, translation, scale_factor, display_level, run_time
            )
        else:
            self._execute_simple_animation(
                scene, tree_center, translation, scale_factor, run_time, script, tts_duration
            )

    def _calculate_tree_center(self, subtree_nodes: list[Node]) -> np.ndarray:
        """计算树的中心点"""
        bbox = GeometryUtils.get_tree_bounding_box(subtree_nodes)
        return np.array([(bbox[0] + bbox[1]) / 2, (bbox[2] + bbox[3]) / 2, 0])

    def _execute_simple_animation(
        self,
        scene: FeynmanScene,
        tree_center: np.ndarray,
        translation: np.ndarray,
        scale_factor: float,
        run_time: float,
        script: str,
        tts_duration: float,
    ) -> None:
        """执行简单的缩放平移动画"""
        all_group = VGroup(*[n.manim_object for n in self.all_nodes if n.manim_object])
        all_group.add(*[n.line_object for n in self.all_nodes if n.line_object])

        animation = all_group.animate.scale(scale_factor, about_point=tree_center).shift(translation)
        self.play_animation(scene, animation, run_time=run_time, script=script, tts_duration=tts_duration)

    def _execute_layered_animation(
        self,
        node: Node,
        scene: FeynmanScene,
        tree_center: np.ndarray,
        translation: np.ndarray,
        scale_factor: float,
        display_level: int,
        run_time: float,
    ) -> None:
        """执行分层渐现动画"""
        node_queue = queue.Queue()
        node_queue.put(node)
        all_level_anims = []

        while not node_queue.empty():
            level_size = node_queue.qsize()
            level_group = VGroup()
            line_group = VGroup()

            # 收集当前层节点
            current_level_nodes = []
            for _ in range(level_size):
                current_node = node_queue.get()
                current_level_nodes.append(current_node)
                for child in current_node.children:
                    node_queue.put(child)

            # 按父节点分组并添加到组中
            self._group_nodes_by_parent(current_level_nodes, level_group, line_group)

            # 应用变换
            level_group.scale(scale_factor, about_point=tree_center).shift(translation)
            line_group.scale(scale_factor, about_point=tree_center).shift(translation)

            # 创建动画
            level_anims = self._create_level_animations(line_group, level_group, display_level, scene, run_time)
            if display_level < 0:
                all_level_anims.extend(level_anims)

        if display_level < 0:
            scene.play(AnimationGroup(*all_level_anims, lag_ratio=0.5), run_time=run_time)

    def _group_nodes_by_parent(self, nodes: list[Node], level_group: VGroup, line_group: VGroup) -> None:
        """按父节点分组节点"""
        parent_groups = defaultdict(list)
        for node in nodes:
            parent_id = id(node.parent) if node.parent else None
            parent_groups[parent_id].append(node)

        for nodes_group in parent_groups.values():
            for node in nodes_group:
                if node.manim_object:
                    level_group.add(node.manim_object)
                if node.line_object:
                    line_group.add(node.line_object)

    def _create_level_animations(
        self, line_group: VGroup, level_group: VGroup, display_level: int, scene: FeynmanScene, run_time: float
    ) -> list:
        """创建层级动画"""
        level_anims = []

        if len(line_group) > 0:
            line_animations = [Create(line_object) for line_object in line_group]
            if display_level < 0:
                level_anims.append(AnimationGroup(*line_animations))
            else:
                scene.play(AnimationGroup(*line_animations), run_time=run_time)

        if display_level < 0:
            node_anim = AnimationGroup(*[FadeIn(mob) for mob in level_group])
            level_anims.append(node_anim)
        else:
            scene.play(FadeIn(level_group), run_time=run_time)

        return level_anims

    def apply_layout(
        self,
        scene: FeynmanScene | None = None,
        display_level: int = 2,
        script: str = None,
        tts_duration: float = None,
        create_mind: bool = True,
    ) -> None:
        """
        执行整体布局：创建节点对象、水平/垂直排列、调整重叠并生成连线
        """
        if scene is None:
            scene = self.scene
        # 为根节点创建 Manim 对象
        self._create_root_node_object()
        # 分别为左右子树创建 Manim 对象
        self.create_manim_objects(self.left_subtree_nodes)
        self.create_manim_objects(self.right_subtree_nodes)
        self.horizontal_layout()
        self.vertical_layout()
        self.fix_overlap()
        self.align_mobjects()
        if scene is not None:
            line_config = LineConfig(style="curved", width=3, color=GRAY)
            get_edge_lines(self.root, self.text_colors, line_config)
            for node in self.all_nodes:
                if node.depth >= MindMapConfig.DEPTH_WITH_RECTANGLE:
                    current_center = node.manim_object.get_center()
                    offset = current_center - node.manim_object.get_bottom()
                    node.manim_object.shift(offset)
            if create_mind:
                self.focus(
                    self.root,
                    first_call=True,
                    run_time=1,
                    display_level=display_level,
                    script=script,
                    tts_duration=tts_duration,
                )

    def toggle_visibility(
        self,
        visible: bool,
        run_time: float = 1,
        script: str = None,
        tts_duration: float = 0,
    ) -> None:
        """
        隐藏或展示整个思维导图
        visible 为 True 时，通过 FadeIn 动效显示，
        为 False 时，通过 FadeOut 动效隐藏。
        """
        # 收集所有节点和连线对应的 Manim 对象
        all_objects = [node.manim_object for node in self.all_nodes if node.manim_object] + [
            node.line_object for node in self.all_nodes if node.line_object
        ]
        group = VGroup(*all_objects)
        if visible:
            animation = FadeIn(group)
        else:
            animation = FadeOut(group)
        self.play_animation(
            self.scene,
            animation,
            run_time=run_time,
            script=script,
            tts_duration=tts_duration,
        )

    def show(self, run_time: float = 1, script: str = None, tts_duration: float = 0) -> None:
        self.toggle_visibility(True, run_time=run_time, script=script, tts_duration=tts_duration)

    def hide(self, run_time: float = 1, script: str = None, tts_duration: float = 0) -> None:
        self.toggle_visibility(False, run_time=run_time, script=script, tts_duration=tts_duration)


################################################################################
# 节点可见性管理类
################################################################################


class NodeVisibilityManager:
    """管理节点可见性的工具类"""

    @staticmethod
    def hide_all_nodes(nodes: list[Node]) -> None:
        """隐藏所有节点"""
        for node in nodes:
            if node.manim_object:
                node.manim_object.set_opacity(0.0)
                if node.line_object:
                    node.line_object.set_stroke(opacity=0.0)

    @staticmethod
    def show_nodes_with_opacity(nodes: list[Node], opacity: float = 1.0) -> None:
        """显示指定节点并设置透明度"""
        for node in nodes:
            if node.manim_object:
                node.manim_object.set_opacity(opacity)
                if node.line_object:
                    node.line_object.set_stroke(opacity=opacity)

    @staticmethod
    def get_path_to_root(node: Node) -> list[Node]:
        """获取从节点到根节点的路径"""
        path = []
        current = node
        while current:
            path.append(current)
            current = current.parent
        return list(reversed(path))

    @staticmethod
    def collect_subtree_nodes(node: Node, max_depth: int = float("inf")) -> list[Node]:
        """收集子树中指定深度内的所有节点"""
        nodes = []
        if node.depth <= max_depth:
            nodes.append(node)
            for child in node.children:
                nodes.extend(NodeVisibilityManager.collect_subtree_nodes(child, max_depth))
        return nodes


################################################################################
# JSON 到树结构及边的绘制
################################################################################


def create_node_from_json(json_data: dict, current_level: int, max_level: int) -> Node:
    """
    递归根据 JSON 数据生成节点树
    """
    children = [
        create_node_from_json(child, current_level + 1, max_level)
        for child in json_data.get("子章节", [])
        if current_level <= max_level
    ]
    # 注意：此处进行 children 的反转，确保添加顺序与原逻辑一致
    return Node(json_data["标题"], children=list(reversed(children)))


class LineConfig(dict):
    def __init__(
        self,
        style: str = "straight",
        width: int = 3,
        color=GRAY,
        curve_factor: float = 0.2,
    ):
        super().__init__()
        self["style"] = style
        self["width"] = width
        self["color"] = color
        self["curve_factor"] = curve_factor


def get_edge_lines(node: Node, colors: list, config: LineConfig | None = None) -> list[Mobject]:
    """
    递归生成节点之间的连线
    """
    if config is None:
        config = LineConfig()
    for child in node.children:
        if node.manim_object and child.manim_object:
            if child.direction == "right":
                start = node.manim_object.get_right()
                end = child.manim_object.get_left()
            else:
                start = node.manim_object.get_left()
                end = child.manim_object.get_right()

            if node.depth < MindMapConfig.DEPTH_WITH_RECTANGLE - 1:
                stroke_width = config["width"]
            else:
                stroke_width = min(2, config["width"])  # 和underline的width一致
            if config["style"] == "straight":
                line = Line(start, end, stroke_width=stroke_width, color=colors[child.depth])
            elif config["style"] == "curved":
                parent_y = node.manim_object.get_center()[1]
                child_y = child.manim_object.get_center()[1]
                direction_factor = 1 if child_y >= parent_y else -1
                curve_factor = config.get("curve_factor", 0.2)
                mid_x = (start[0] + end[0]) / 2
                ctrl1 = np.array(
                    [
                        mid_x,
                        start[1] + direction_factor * curve_factor * abs(end[0] - start[0]) * 0.5,
                        0,
                    ]
                )
                ctrl2 = np.array(
                    [
                        mid_x,
                        end[1] - direction_factor * curve_factor * abs(end[0] - start[0]) * 0.5,
                        0,
                    ]
                )
                line = CubicBezier(
                    start,
                    ctrl1,
                    ctrl2,
                    end,
                    stroke_width=stroke_width,
                    color=colors[child.depth],
                )
                # color=config["color"],
            child.line_object = line
            get_edge_lines(child, colors, config)


################################################################################
# Manim Scene
################################################################################


# 主要的动画函数入口
def animate_mindmap(
    scene: FeynmanScene,
    title: str,
    data_source: str | dict | None = None,
    layout_style: str = "balance",
    max_depth: int = 3,
    focus_sequence: list[str] | None = None,
    narration: str | None = None,
    id: str | None = None,
) -> None:
    """
    创建交互式思维导图的主要函数
    """
    logger.info(f"Creating mindmap with layout_style='{layout_style}', max_depth={max_depth}")

    if not data_source and not id:
        raise ValueError("data_source is required for a new mindmap")

    # Create title object
    from dsl.v2.themes.theme_utils import ThemeUtils

    title_text = Text(
        title,
        font=ThemeUtils.get_font("heading", "Microsoft YaHei"),
        color=ThemeUtils.get_color("text_primary", WHITE),
        font_size=ThemeUtils.get_font_size("h1"),
    ).to_edge(UP, buff=0.8)

    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=title_text if scene.transition_enabled else None)

    unique_id = id or f"mindmap_{abs(hash(str(data_source))) % 10000}"

    # 初始化mindmaps字典（如果不存在）
    if not hasattr(scene, "mindmaps"):
        scene.mindmaps = {}

    # 尝试从已有mindmaps加载
    if unique_id in scene.mindmaps:
        mindmap = scene.mindmaps[id]
        mindmap.show()
        need_create_mindmap = False
    else:
        # 处理数据源
        if isinstance(data_source, str):
            if os.path.exists(data_source):
                # 从文件加载
                with open(data_source, encoding="utf-8") as f:
                    data = json.load(f)
            else:
                data = eval(data_source)
        else:
            # 直接使用字典数据
            data = data_source

        # 创建节点树
        root = create_node_from_json(data, current_level=0, max_level=max_depth)

        # 创建思维导图布局
        mindmap = MindMapLayout(scene, root, level_distance=1.5, sibling_distance=1, layout=layout_style)
        # 保存到mindmaps字典
        scene.mindmaps[unique_id] = mindmap
        need_create_mindmap = True

    # 应用布局并显示
    with scene.voiceover(text=narration):
        # Handle animation section for transition support
        if not scene.transition_enabled:
            # Normal animation - show title first
            scene.play(Write(title_text), run_time=1.0)
            scene.wait(0.5)
        else:
            scene.add(title_text)

        # Always animate the mindmap content with original effects
        if need_create_mindmap:
            mindmap.apply_layout(display_level=max_depth + 1, script=narration, create_mind=True)

        # 如果有聚焦序列，按顺序聚焦节点
        if focus_sequence:
            for node_text in focus_sequence:
                try:
                    mindmap.focus(text=node_text, run_time=1)
                    scene.wait()
                except ValueError as e:
                    logger.warning(f"无法聚焦节点 '{node_text}': {e}")

    # Create display group with title and mindmap content
    mindmap_group = mindmap.get_all_mobjects()  # Get all mindmap mobjects
    display_group = Group(title_text, *mindmap_group)
    display_group.arrange(DOWN, buff=0.5)

    # Update current_mobject saving - ensure title is first submobject
    scene.current_mobj = display_group
    scene.save_scene_state("mindmap", unique_id)

    logger.info(f"Mindmap '{unique_id}' created successfully")
