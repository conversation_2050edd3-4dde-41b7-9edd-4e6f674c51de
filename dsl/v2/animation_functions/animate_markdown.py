"""
effect: |
    将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。
    支持各种Markdown元素，包括标题、列表、代码块、表格等。

use_cases:
    - 展示格式化的文本内容，如教程说明、演示文稿
    - 在动画中展示结构化的信息，如列表和表格
    - 显示带有语法高亮的代码片段
    - 创建包含文本和图片的混合内容

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  content:
    type: str
    desc: Markdown格式的文本内容
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  narration:
    type: str
    desc: 在内容显示时播放的语音旁白文本
    required: true

dsl_examples:
  - type: animate_markdown
    params:
      content: |
        # 主标🚗题

        ⚠️ 这是一段普通文本，支持**粗体**和*斜体*。

        > 🚰 这是一个引用💦块。

        ## 子标题

        - 列表🐆 项1
        - 🐇 列表项2
        - $E = mc^2$
            - 这是质能公式
            - 由爱因斯坦提出

        $$
        a^2 = b^2 + c^2
        $$

        ```python
        def hello_world():
            print("Hello, world!")
        ```
      title: 这是一个Markdown示例
      narration: 这是一个Markdown示例，包含标题、文本和代码。
  - type: animate_markdown
    params:
      content: |
        ## 数据比较📊

        | 产品 | 价格 | 评分 |
        | ---- | ---- | ---- |
        | A产品 | ¥199 | 4.5分 |
        | B产品 | ¥299 | 4.8分 |
        | C产品 | ¥399 | 4.9分 |
      title: 产品价格比较
      narration: 这个表格比较了两款产品的价格和评分。

notes:
  - 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等
  - 根据内容会自动调整大小以适应场景
  - 必须包含title字段，言简意赅，概括内容要点，content中不要有和title重复的标题部分，重点在内容介绍
"""

import os
import re
import unicodedata
import uuid
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Any, Optional

import requests
from loguru import logger
from manim import *

from dsl.v2.themes.theme_manager import get_current_theme
from dsl.v2.themes.theme_utils import ThemeUtils, get_theme_value

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from utils.format import wrap_text
from utils.md_to_pango import MarkdownToSimplifiedConverter
from utils.title_utils import create_title

# 使用主题系统获取样式值
DEFAULT_TEXT_COLOR = WHITE

# Emoji related constants
EMOJI_DIR = "emoji_cache"


# ===== 新的统一内联元素处理系统 =====


class InlineElementProcessor(ABC):
    """内联元素处理器的抽象基类"""

    @abstractmethod
    def can_handle(self, text: str, start_pos: int) -> bool:
        """检查是否可以处理从指定位置开始的文本"""
        pass

    @abstractmethod
    def extract_element(self, text: str, start_pos: int) -> tuple[int, str]:
        """提取内联元素，返回(结束位置, 元素内容)"""
        pass

    @abstractmethod
    def create_mobject(
        self, element_content: str, base_height: float, font: str, color: str, font_size: float
    ) -> Optional[Mobject]:
        """创建对应的Manim对象"""
        pass


class EmojiProcessor(InlineElementProcessor):
    """Emoji处理器"""

    def can_handle(self, text: str, start_pos: int) -> bool:
        if start_pos >= len(text):
            return False
        return is_emoji(text[start_pos])

    def extract_element(self, text: str, start_pos: int) -> tuple[int, str]:
        """提取emoji，处理复合emoji"""
        i = start_pos
        current_emoji = text[i]
        j = i + 1

        # 尝试扩展emoji检测（复合emoji字符）
        while j < len(text) and (
            ord(text[j]) in [0xFE0F, 0xFE0E]  # 变体选择器
            or 0x1F3FB <= ord(text[j]) <= 0x1F3FF  # 肤色修饰符
        ):
            current_emoji += text[j]
            j += 1

        return j, current_emoji

    def create_mobject(
        self, element_content: str, base_height: float, font: str, color: str, font_size: float
    ) -> Optional[Mobject]:
        emoji_mob = create_emoji_mobject(element_content)
        if emoji_mob:
            emoji_mob.height = base_height * 1.0  # emoji_scale
            return emoji_mob
        return None


class InlineMathProcessor(InlineElementProcessor):
    """内联数学公式处理器"""

    def can_handle(self, text: str, start_pos: int) -> bool:
        if start_pos >= len(text):
            return False
        # 检查是否是我们的math span标签开始
        return text[start_pos:].startswith("<span type='math'>$")

    def extract_element(self, text: str, start_pos: int) -> tuple[int, str]:
        """提取内联数学公式"""
        # 查找完整的span标签
        span_start = text.find("<span type='math'>$", start_pos)
        if span_start == -1:
            return start_pos, ""

        # 查找对应的结束标签
        content_start = span_start + len("<span type='math'>$")
        span_end = text.find("$</span>", content_start)
        if span_end == -1:
            return start_pos, ""

        # 提取数学公式内容（不包括$符号）
        math_content = text[content_start:span_end]
        end_pos = span_end + len("$</span>")

        return end_pos, math_content

    def create_mobject(
        self, element_content: str, base_height: float, font: str, color: str, font_size: float
    ) -> Optional[Mobject]:
        """创建内联数学公式的Manim对象"""
        try:
            # 使用MathTex创建数学公式
            math_mob = MathTex(element_content, font_size=font_size * 0.8, color=DEFAULT_TEXT_COLOR)
            # 调整高度以匹配文本
            if math_mob.height > 0:
                math_mob.height = base_height * 0.9
            return math_mob
        except Exception as e:
            logger.error(f"创建内联数学公式失败: {e}")
            # 降级为普通文本
            return MarkupText(f"${element_content}$", font=font, font_size=font_size, color=DEFAULT_TEXT_COLOR)


class InlineElementManager:
    """内联元素管理器，统一处理所有内联元素"""

    def __init__(self):
        self.processors = [
            InlineMathProcessor(),  # 数学公式优先处理（因为它有特定的span格式）
            EmojiProcessor(),
        ]

    def parse_text_with_inline_elements(self, text: str) -> list[tuple[str, str]]:
        """
        解析包含内联元素的文本，智能处理span标签
        返回: [(element_type, content), ...]
        element_type可以是: 'text', 'emoji', 'math'
        """
        # 首先检查是否包含span标签，如果有，需要特殊处理
        if "<span" in text and "</span>" in text:
            return self._parse_with_span_awareness(text)
        else:
            return self._parse_simple(text)

    def _parse_simple(self, text: str) -> list[tuple[str, str]]:
        """简单解析，不考虑span标签"""
        result = []
        i = 0

        while i < len(text):
            # 检查是否有处理器可以处理当前位置
            handled = False

            for processor in self.processors:
                if processor.can_handle(text, i):
                    end_pos, element_content = processor.extract_element(text, i)
                    if end_pos > i:  # 成功提取
                        processor_type = type(processor).__name__.replace("Processor", "").lower()
                        result.append((processor_type, element_content))
                        i = end_pos
                        handled = True
                        break

            if not handled:
                # 收集普通文本直到下一个内联元素
                text_start = i
                while i < len(text):
                    found_inline = False
                    for processor in self.processors:
                        if processor.can_handle(text, i):
                            found_inline = True
                            break
                    if found_inline:
                        break
                    i += 1

                if i > text_start:
                    result.append(("text", text[text_start:i]))

        return result

    def _parse_with_span_awareness(self, text: str) -> list[tuple[str, str]]:
        """智能解析，保持span标签完整性，只处理span外部的内联元素"""
        # 使用原有的split_span_and_emojis逻辑，但只处理emoji
        return self._split_span_and_emojis_smart(text)

    def _split_span_and_emojis_smart(self, s: str) -> list[tuple[str, str]]:
        """改进的span和emoji拆分，返回统一格式，支持数学公式"""
        import re

        pattern = re.compile(r"(<span[^>]*>)(.*?)(</span>)", re.DOTALL)
        result = []
        last = 0

        for m in pattern.finditer(s):
            # 标签前的内容
            if m.start() > last:
                before_span = s[last : m.start()]
                result.extend(self._split_text_and_emojis_to_tuples(before_span))

            span_head, inner, span_tail = m.group(1), m.group(2), m.group(3)

            # 检查是否是数学公式span
            if "type='math'" in span_head and inner.startswith("$") and inner.endswith("$"):
                # 这是数学公式，提取内容并作为math类型处理
                math_content = inner[1:-1]  # 去掉$符号
                result.append(("inlinemath", math_content))
            else:
                # 普通span，处理内部的emoji
                inner_parts = self._split_text_and_emojis_to_tuples(inner)

                for element_type, seg in inner_parts:
                    if element_type == "emoji":
                        result.append(("emoji", seg))
                    else:
                        if seg.strip():
                            result.append(("text", f"{span_head}{seg}{span_tail}"))
            last = m.end()

        if last < len(s):
            after_span = s[last:]
            result.extend(self._split_text_and_emojis_to_tuples(after_span))

        return result

    def _split_text_and_emojis_to_tuples(self, text: str) -> list[tuple[str, str]]:
        """将文本分割为[(element_type, segment)]格式"""
        spans = find_emojis_in_text(text)
        if not spans:
            return [("text", text)] if text else []

        result = []
        last = 0
        for start, end, emoji_str in spans:
            if start > last:
                result.append(("text", text[last:start]))
            result.append(("emoji", emoji_str))
            last = end
        if last < len(text):
            result.append(("text", text[last:]))
        return result

    def create_mobject_from_elements(
        self, elements: list[tuple[str, str]], font: str, color: str, font_size: float
    ) -> Mobject:
        """从解析的元素列表创建Mobject"""
        ref_text = MarkupText("中", font=font, color=color, font_size=font_size)
        base_height = ref_text.height

        group = Group()

        for element_type, content in elements:
            if element_type == "text":
                if content.strip():
                    group.add(MarkupText(content, font=font, color=color, font_size=font_size))
            elif element_type == "inlinemath":
                # 直接处理内联数学公式
                try:
                    math_mob = MathTex(content, font_size=font_size * 0.8, color=DEFAULT_TEXT_COLOR)
                    if math_mob.height > 0:
                        math_mob.height = base_height * 0.9
                    group.add(math_mob)
                except Exception as e:
                    logger.error(f"创建内联数学公式失败: {e}")
                    group.add(MarkupText(f"${content}$", font=font, font_size=font_size, color=DEFAULT_TEXT_COLOR))
            else:
                # 查找对应的处理器
                for processor in self.processors:
                    processor_type = type(processor).__name__.replace("Processor", "").lower()
                    if processor_type == element_type:
                        mob = processor.create_mobject(content, base_height, font, color, font_size)
                        if mob:
                            group.add(mob)
                        break

        if len(group) > 0:
            group.arrange(RIGHT, buff=0.05)
            return group
        else:
            return MarkupText("", font=font, color=color, font_size=font_size)

    def create_mobject_from_elements_glow(self, elements: list[tuple[str, str]], font_size: float) -> Mobject:
        """从解析的元素列表创建Mobject，使用发光字体"""
        ref_text = create_glow_text_simple("中", font_size)
        base_height = ref_text.height

        group = Group()

        for element_type, content in elements:
            if element_type == "text":
                if content.strip():
                    group.add(create_glow_text_simple(content, font_size))
            elif element_type == "inlinemath":
                # 直接处理内联数学公式
                try:
                    math_mob = MathTex(content, font_size=font_size * 0.8, color=DEFAULT_TEXT_COLOR)
                    if math_mob.height > 0:
                        math_mob.height = base_height * 0.9
                    group.add(math_mob)
                except Exception as e:
                    logger.error(f"创建内联数学公式失败: {e}")
                    group.add(create_glow_text_simple(f"${content}$", font_size))
            else:
                # 查找对应的处理器
                for processor in self.processors:
                    processor_type = type(processor).__name__.replace("Processor", "").lower()
                    if processor_type == element_type:
                        # 为emoji等其他元素保持原有逻辑
                        glow_config = ThemeUtils.get_glow_font_config()
                        mob = processor.create_mobject(
                            content, base_height, glow_config["font"], glow_config["color"], font_size
                        )
                        if mob:
                            group.add(mob)
                        break

        if len(group) > 0:
            group.arrange(RIGHT, buff=0.05)
            return group
        else:
            return create_glow_text_simple("", font_size)


# 全局内联元素管理器实例
inline_manager = InlineElementManager()


def is_emoji(character: str) -> bool:
    """
    检查一个字符是否是emoji

    Args:
        character: 要检查的字符

    Returns:
        是否为emoji字符
    """
    if not character:
        return False

    # 检查单字符emoji
    if len(character) == 1:
        try:
            # 使用unicodedata获取字符类别
            category = unicodedata.category(character)
            # 检查是否是Emoji类别 (So-Symbol Other)
            if category == "So":
                return True

            # 检查是否在emoji Unicode范围内
            cp = ord(character)
            # 基本Emoji区域
            if 0x1F000 <= cp <= 0x1FFFF:
                return True
            # 补充符号区域中的emoji
            if 0x2600 <= cp <= 0x27BF:
                return True
        except Exception:
            return False

    # 多字符表情的检测（如带变体选择器的表情）
    if len(character) > 1:
        # 检查是否包含变体选择器 (VS15, VS16)
        for c in character:
            cp = ord(c)
            if cp in [0xFE0F, 0xFE0E]:  # 变体选择器
                return True

    return False


def find_emojis_in_text(text: str) -> list[tuple[int, int, str]]:
    """
    在文本中查找所有emoji及其位置

    Args:
        text: 要搜索的文本

    Returns:
        包含emoji位置和内容的列表: [(start_index, end_index, emoji_str), ...]
    """
    emoji_positions = []
    i = 0
    while i < len(text):
        char = text[i]
        # 处理可能的复合emoji（例如带修饰符的emoji）
        j = i + 1
        current_emoji = char

        # 尝试扩展emoji检测（复合emoji字符）
        while j < len(text) and (
            ord(text[j]) in [0xFE0F, 0xFE0E]  # 变体选择器
            or 0x1F3FB <= ord(text[j]) <= 0x1F3FF
        ):  # 肤色修饰符
            current_emoji += text[j]
            j += 1

        if is_emoji(char) or is_emoji(current_emoji):
            emoji_positions.append((i, j, current_emoji))
            i = j  # 跳过整个emoji
        else:
            i += 1

    return emoji_positions


def download_emoji_image(emoji: str) -> Optional[str]:
    """
    下载高清emoji图片（PNG格式）

    Args:
        emoji: 要下载的emoji字符

    Returns:
        保存的文件路径，如果下载失败则返回None
    """
    if not os.path.exists(EMOJI_DIR):
        os.makedirs(EMOJI_DIR)

    # 生成emoji编码
    if len(emoji) == 0:
        return None

    emoji_code = hex(ord(emoji[0]))[2:].lower()
    emoji_name = f"emoji_{emoji_code}"
    filepath = os.path.join(EMOJI_DIR, f"{emoji_name}.svg")

    if os.path.exists(filepath):
        return filepath

    try:
        # 使用72x72高清版本（这是Twitter Twemoji的标准高质量版本）
        url = f"https://raw.githubusercontent.com/twitter/twemoji/master/assets/svg/{emoji_code}.svg"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            with open(filepath, "wb") as f:
                f.write(response.content)
            return filepath
    except Exception as e:
        logger.error(f"下载emoji失败: {e}")

    return None


def create_emoji_mobject(emoji: str) -> Optional[SVGMobject]:
    """
    创建高清emoji的Mobject（使用72x72高质量PNG）

    Args:
        emoji: 要创建的emoji字符

    Returns:
        emoji图片对象，如果创建失败则返回None
    """
    try:
        image_path = download_emoji_image(emoji)

        if image_path and os.path.exists(image_path):
            emoji_mob = SVGMobject(image_path)
            return emoji_mob
    except Exception as e:
        logger.error(f"无法加载emoji图片: {e}")

    return None


# 使用主题系统获取样式值
DEFAULT_TEXT_COLOR = ThemeUtils.get_color("text_primary", WHITE)
BLOCKQUOTE_COLOR = ThemeUtils.get_color("blockquote", GREY_C)
BLOCKQUOTE_PREFIX = "> "
DEFAULT_FONT_SIZE = ThemeUtils.get_font_size("body")
CODE_FONT_SIZE = ThemeUtils.get_font_size("code")
LINE_SPACING = get_theme_value("typography.line_spacing", 1.0)  # 行距
ITEM_SPACING = ThemeUtils.get_spacing("list_item_spacing", 0.5)  # VGroup项目间距
DEFAULT_FONT = ThemeUtils.get_font("primary", "Microsoft YaHei")
DEFAULT_TEXT_WRAP_WIDTH = 60
DEFAULT_ANIMATION = "fadeIn"  # 整体动画效果
DEFAULT_ITEM_ANIMATION = "fadeIn"  # 当使用LaggedStart/Succession时的单项动画
DEFAULT_LAG_RATIO = get_theme_value("animation.lag_ratio", 2.0)


def get_standard_text_height(font, color):
    # 用一个标准字生成基准高度
    ref = MarkupText("测试", font=font, color=color)
    return ref.height


def split_text_and_emojis(text: str):
    """将文本分割为[(is_emoji, segment)]"""
    spans = find_emojis_in_text(text)
    if not spans:
        return [(False, text)]
    result = []
    last = 0
    for start, end, emoji_str in spans:
        if start > last:
            result.append((False, text[last:start]))
        result.append((True, emoji_str))
        last = end
    if last < len(text):
        result.append((False, text[last:]))
    return result


def split_span_and_emojis(s):
    # 匹配 <span ...>...</span>，递归对标签内部文本做emoji拆分
    pattern = re.compile(r"(<span[^>]*>)(.*?)(</span>)", re.DOTALL)
    result = []
    last = 0
    for m in pattern.finditer(s):
        # 标签前的内容
        if m.start() > last:
            result.extend(split_text_and_emojis(s[last : m.start()]))
        span_head, inner, span_tail = m.group(1), m.group(2), m.group(3)
        inner_parts = split_text_and_emojis(inner)
        for is_emoji, seg in inner_parts:
            if is_emoji:
                result.append((True, seg))
            else:
                if seg.strip():
                    result.append((False, f"{span_head}{seg}{span_tail}"))
        last = m.end()
    if last < len(s):
        result.extend(split_text_and_emojis(s[last:]))
    return result


def create_text_with_inline_elements_new(
    content: str, font: str, color: str, font_size: float, weight: str = NORMAL
) -> Mobject:
    """
    新的统一内联元素处理函数，支持emoji和数学公式
    仅用于没有span标签的简单情况
    """
    logger.info(f"Processing content with inline elements: {content[:100]}...")

    # 检查是否包含span标签，如果有，回退到原有逻辑
    if "<span" in content and "</span>" in content and "type='math'" not in content:
        logger.info("Content contains span tags, using legacy emoji processing")
        return create_text_with_emoji_v2_legacy(content, font, color, font_size, weight)

    # 使用新的内联元素管理器解析内容
    elements = inline_manager.parse_text_with_inline_elements(content)
    logger.info(f"Parsed elements: {elements}")

    # 创建Mobject
    return inline_manager.create_mobject_from_elements(elements, font, color, font_size)


def create_text_with_emoji_v2_legacy(
    content: str, font: str, color: str, font_size: float, weight: str = NORMAL, emoji_scale: float = 1.0
) -> Mobject:
    """
    原有的emoji处理逻辑，保持span标签完整性
    """
    ref_text = MarkupText("中", font=font, color=color, font_size=font_size, weight=weight)
    base_height = ref_text.height
    logger.debug(f"content: {content}, font_size: {font_size}, weight: {weight}, base_height: {base_height}")
    parts = split_span_and_emojis(content)
    logger.debug(f"parts: {parts}")
    group = Group()
    for is_emoji, seg in parts:
        if is_emoji:
            emoji_mob = create_emoji_mobject(seg)
            if emoji_mob:
                emoji_mob.height = base_height * emoji_scale
                group.add(emoji_mob)
        else:
            if seg.strip():
                group.add(MarkupText(seg, font=font, color=color, font_size=font_size))
    if len(group) > 0:
        group.arrange(RIGHT, buff=0.05)
    return group if len(group) > 0 else MarkupText(content, font=font, color=color, font_size=font_size)


# 保持向后兼容性的别名
def create_text_with_emoji_v2(
    content: str, font: str, color: str, font_size: float, weight: str = NORMAL, emoji_scale: float = 1.0
) -> Mobject:
    """
    向后兼容的函数，智能选择处理方式
    """
    # 检查是否包含数学公式的span标签
    if "<span type='math'>$" in content:
        return create_text_with_inline_elements_new(content, font, color, font_size, weight)
    else:
        return create_text_with_emoji_v2_legacy(content, font, color, font_size, weight, emoji_scale)


# === 新增：发光字体相关函数 ===


def create_glow_text_simple(
    content: str, font_size: float, custom_color: str = None, line_spacing: float = None, **kwargs
) -> VGroup:
    """
    创建简单的发光文本，使用复制文本并添加发光效果的方式

    Args:
        content: 文本内容
        font_size: 字体大小，使用原有参数
        custom_color: 自定义发光颜色（已弃用，现在统一使用主题配置的颜色）
        line_spacing: 行间距，默认使用主题配置的行间距
        **kwargs: 其他Text参数

    Returns:
        包含发光效果的VGroup对象
    """
    # 获取发光字体配置
    glow_config = ThemeUtils.get_glow_font_config()

    # 字体颜色固定为白色
    text_color = glow_config["color"]  # 应该是白色

    # 发光颜色统一使用主题配置
    stroke_color = glow_config["stroke_color"]

    # 自适应发光宽度：字体大小的1/5
    adaptive_stroke_width = font_size / 5.0

    # 如果没有指定行间距，使用主题配置的行间距
    if line_spacing is None:
        from dsl.v2.themes.theme_manager import get_current_theme

        theme = get_current_theme()
        line_spacing = theme.typography.line_spacing

    # 创建主文本
    main_text = Text(
        content,
        font=glow_config["font"],
        font_size=font_size,
        color=text_color,
        weight=glow_config["weight"],
        line_spacing=line_spacing,
        **kwargs,
    )

    # 创建发光效果（复制主文本）
    glow_text = main_text.copy()
    glow_text.set_color(stroke_color)
    glow_text.set_stroke(color=stroke_color, width=adaptive_stroke_width, opacity=glow_config["stroke_opacity"])

    # 组合发光效果和主文本
    text_combined = VGroup(glow_text, main_text)

    return text_combined


def create_text_with_emoji_and_glow(content: str, font_size: float, emoji_scale: float = 1.0) -> Mobject:
    """
    创建包含emoji和发光字体的文本，专门用于markdown

    Args:
        content: 文本内容
        font_size: 字体大小，使用原有参数
        emoji_scale: emoji缩放比例
        custom_color: 自定义文本颜色（已弃用，现在统一使用主题配置的颜色）
        **kwargs: 其他参数

    Returns:
        包含emoji和发光文本的Mobject
    """
    # 检查是否包含数学公式的span标签
    if "<span type='math'>$" in content:
        return create_text_with_inline_elements_new_glow(content, font_size)
    else:
        return create_text_with_emoji_v2_legacy_glow(content, font_size, emoji_scale)


def create_text_with_inline_elements_new_glow(content: str, font_size: float) -> Mobject:
    """
    新的统一内联元素处理函数，支持emoji和数学公式，使用发光字体
    """
    logger.info(f"Processing content with inline elements (glow): {content[:100]}...")

    # 检查是否包含span标签，如果有，回退到原有逻辑
    if "<span" in content and "</span>" in content and "type='math'" not in content:
        logger.info("Content contains span tags, using legacy emoji processing with glow")
        return create_text_with_emoji_v2_legacy_glow(content, font_size, 1.0)

    # 使用新的内联元素管理器解析内容
    elements = inline_manager.parse_text_with_inline_elements(content)
    logger.info(f"Parsed elements: {elements}")

    # 创建Mobject，使用发光字体
    return inline_manager.create_mobject_from_elements_glow(elements, font_size)


def create_text_with_emoji_v2_legacy_glow(content: str, font_size: float, emoji_scale: float = 1.0) -> Mobject:
    """
    原有的emoji处理逻辑，保持span标签完整性，使用发光字体
    """
    glow_config = ThemeUtils.get_glow_font_config()

    ref_text = create_glow_text_simple("中", font_size)
    base_height = ref_text.height

    logger.debug(f"content: {content}, font_size: {font_size}, base_height: {base_height}")
    parts = split_span_and_emojis(content)
    logger.debug(f"parts: {parts}")

    group = Group()
    for is_emoji, seg in parts:
        if is_emoji:
            emoji_mob = create_emoji_mobject(seg)
            if emoji_mob:
                emoji_mob.height = base_height * emoji_scale
                group.add(emoji_mob)
        else:
            if seg.strip():
                # 检查是否是span标签内容，如果是则使用MarkupText，否则使用发光文本
                if "<span" in seg and "</span>" in seg:
                    # 对于span标签，保持MarkupText，但文字仍为白色
                    text_color = glow_config["color"]  # 白色
                    group.add(MarkupText(seg, font=glow_config["font"], color=text_color, font_size=font_size))
                else:
                    group.add(create_glow_text_simple(seg, font_size))

    if len(group) > 0:
        group.arrange(RIGHT, buff=0.05)
        return group
    else:
        return create_glow_text_simple(content, font_size)


# --- Helper Function for Mobject Creation ---
def _create_markdown_mobjects(parsed_markdown: list[dict[str, Any]], title: str = None) -> Group:
    """
    Creates all Manim mobjects from parsed markdown elements,
    arranges them, and wraps them in a background rectangle.
    """
    all_content_mobjects = Group()
    if title:
        all_content_mobjects.add(
            Text(title, font=DEFAULT_FONT, color=DEFAULT_TEXT_COLOR, font_size=ThemeUtils.get_font_size("h1"))
        )

    logger.debug(parsed_markdown)

    for element in parsed_markdown:
        element_type = element.get("type")
        content = element.get("content")
        attrs = element.get("attrs", {})
        mobject = None

        if element_type == "text":
            content = "\n".join(wrap_text(content, max_length=DEFAULT_TEXT_WRAP_WIDTH))
            font = ThemeUtils.get_font("primary", DEFAULT_FONT)
            font_size = ThemeUtils.get_font_size("body")
            color = ThemeUtils.get_color("text_primary", DEFAULT_TEXT_COLOR)

            if attrs.get("raw"):
                # 对于原始文本使用MarkupText直接创建，不处理emoji
                mobject = MarkupText(content, font=font, color=color)
            else:
                # 使用通用函数处理文本中的emoji
                mobject = create_text_with_emoji_and_glow(content, font_size, 1.0)

        elif element_type == "heading":
            wrapped_content = "\n".join(wrap_text(content, max_length=DEFAULT_TEXT_WRAP_WIDTH))
            level = attrs.get("level", 1)
            # Get font size from theme and determine text_type based on level
            if level == 1:
                font_size = ThemeUtils.get_font_size("h1")
                text_color = ThemeUtils.get_color("text_primary", DEFAULT_TEXT_COLOR)
            elif level == 2:
                font_size = ThemeUtils.get_font_size("h2")
                text_color = ThemeUtils.get_color("text_secondary", BLUE)
            elif level == 3:
                font_size = ThemeUtils.get_font_size("h3")
                text_color = ThemeUtils.get_color("text_tertiary", GREEN)
            else:
                font_size = ThemeUtils.get_font_size("h4")
                text_color = ThemeUtils.get_color("text_quaternary", YELLOW)

            font = ThemeUtils.get_font("heading", "Microsoft YaHei")
            logger.debug(f"content: {content}, level: {level}, font_size: {font_size}, text_color: {text_color}")
            heading_mob = create_text_with_emoji_and_glow(wrapped_content, font_size, 1.0)
            mobject = heading_mob

        elif element_type == "horizontal_rule":
            hr_opts = {"color": GREY_C, "stroke_width": 3}
            mobject = Line(LEFT, RIGHT, **hr_opts)
            # HR needs to be scaled later based on container width

        elif element_type == "blockquote":
            formatted_content = "\n".join([BLOCKQUOTE_PREFIX + line for line in content.rstrip().split("\n")])
            wrapped_content = "\n".join(wrap_text(formatted_content, max_length=DEFAULT_TEXT_WRAP_WIDTH))
            font_size = ThemeUtils.get_font_size("body")
            mobject = create_text_with_emoji_and_glow(wrapped_content, font_size, 1.0)

        elif element_type == "list":  # Handles both ordered and unordered from parser
            list_items_str = content  # Assuming content is a list of strings for items
            if isinstance(content, str):  # Fallback if content is a single string
                list_items_str = content.strip().split("\n")
            elif isinstance(content, list) and content and isinstance(content[0], dict):
                # Handle structured list data from new markdown parser
                list_items_str = []
                for item_data in content:
                    if isinstance(item_data, dict):
                        level = item_data.get("level", 1)
                        text = item_data.get("text", "")
                        marker = item_data.get("marker", "•")
                        item_indent_str = "  " * (level - 1)
                        list_items_str.append(f"{item_indent_str}{marker} {text}")
                    else:
                        # Fallback for unexpected data type
                        list_items_str.append(str(item_data))
            color = ThemeUtils.get_color("text_primary", DEFAULT_TEXT_COLOR)
            font = ThemeUtils.get_font("primary", DEFAULT_FONT)
            font_size = ThemeUtils.get_font_size("body")

            list_group = Group()

            max_item_width = 0
            for item_text in list_items_str:
                # Replace leading spaces with non-breaking spaces
                leading_spaces = len(item_text) - len(item_text.lstrip(" "))
                non_breaking_spaces = "\u00A0" * leading_spaces
                processed_item_text = non_breaking_spaces + item_text.lstrip(" ")

                list_item_mobject = create_text_with_emoji_and_glow(processed_item_text, font_size, 1.0)
                list_group.add(list_item_mobject)
                max_item_width = max(max_item_width, list_item_mobject.width)

            mobject = Group()
            if len(list_group) > 0:
                # 为列表项创建背景，使用主题系统样式
                max_item_width = max(item_mobject.width for item_mobject in list_group.submobjects)

                # 获取列表项样式
                corner_radius = ThemeUtils.get_component_style("list", "corner_radius", 0.2)
                bg_color = ThemeUtils.get_color("background", GRAY_E)
                bg_opacity = ThemeUtils.get_component_style("list", "background_opacity", 0.7)
                stroke_color = ThemeUtils.get_color("text_primary", DEFAULT_TEXT_COLOR)
                list_padding = ThemeUtils.get_component_style("list", "padding", 0.2)

                for list_item_mobject in list_group.submobjects:
                    item_group = Group()
                    item_group.add(list_item_mobject)

                    item_bg_rect = RoundedRectangle(
                        width=max_item_width + 2,
                        height=list_item_mobject.height + list_padding,
                        color=bg_color,
                        fill_opacity=bg_opacity,
                        stroke_width=2,
                        stroke_color=stroke_color,
                        corner_radius=corner_radius,
                    )
                    item_bg_rect.move_to(list_item_mobject.get_center()).align_to(list_item_mobject, LEFT)
                    list_item_mobject.shift(RIGHT * 0.2)  # 稍微缩进文本
                    item_group.add_to_back(item_bg_rect)
                    mobject.add(item_group)

                mobject.arrange_in_grid()

        elif element_type == "code_block":
            lang = attrs.get("lang", "python")
            code_content = content

            # 使用主题系统获取代码块样式
            code_font = ThemeUtils.get_font("code", "JetBrains Mono")

            code_creation_opts = {
                "language": lang,
                "formatter_style": "monokai",
                "paragraph_config": {"font": code_font},
            }
            try:
                mobject = Code(code_string=code_content.strip("\n"), **code_creation_opts)
            except Exception as e:
                logger.error(f"Error creating Code mobject for lang '{lang}': {e}")
                mobject = MarkupText(f"[Code block error: {lang}]", font=ThemeUtils.get_font("primary", DEFAULT_FONT))

        elif element_type == "image":
            image_path = attrs.get("src", content)  # src from attrs preferred
            alt_text = attrs.get("alt", "Image")
            try:
                mobject = ImageMobject(image_path)
            except Exception as e:
                logger.error(f"无法加载图片 '{image_path}': {e}")
                mobject = MarkupText(f"[图片加载失败: {alt_text or image_path}]", font=DEFAULT_FONT)

        elif element_type == "block_math":
            # 处理块级数学公式
            math_content = content
            try:
                # 使用MathTex创建块级数学公式
                math_font_size = ThemeUtils.get_font_size("h3")  # 使用较大的字体
                mobject = MathTex(math_content, font_size=math_font_size, color=DEFAULT_TEXT_COLOR)
            except Exception as e:
                logger.error(f"创建块级数学公式失败: {e}")
                # 降级为普通文本显示
                mobject = MarkupText(
                    f"$${math_content}$$", font=ThemeUtils.get_font("code", "JetBrains Mono"), color=DEFAULT_TEXT_COLOR
                )

        elif element_type == "table":
            # Assuming content is List[List[str]] for table data
            # And attrs might contain header info: attrs.get("header", [])
            table_data = content
            if table_data and isinstance(table_data, list) and all(isinstance(row, list) for row in table_data):
                str_table_data = [[str(cell) for cell in row] for row in table_data]
                # 使用主题系统获取表格样式
                table_line_width = ThemeUtils.get_component_style("table", "table_line_width", 1.5)
                table_cell_padding = ThemeUtils.get_component_style("table", "table_cell_padding", 0.4)
                table_row_padding = ThemeUtils.get_component_style("table", "table_row_padding", 0.2)
                table_font = ThemeUtils.get_font("table", "LXGW WenKai Mono")
                table_color = ThemeUtils.get_color("text_primary", BLACK)

                table_opts = {
                    "include_outer_lines": True,
                    "line_config": {"stroke_width": table_line_width, "stroke_color": table_color},
                    "h_buff": table_cell_padding,
                    "v_buff": table_row_padding,
                    "element_to_mobject": MarkupText,
                    "element_to_mobject_config": {"font": table_font, "color": table_color},
                }

                # Check for headers from attrs
                header_row_data = attrs.get("header")
                if header_row_data and isinstance(header_row_data, list):
                    table_opts["col_labels"] = [
                        MarkupText(
                            str(h), font=table_font, color=table_color, font_size=ThemeUtils.get_font_size("body")
                        ).set_style(BOLD)
                        for h in header_row_data
                    ]

                mobject = Table(str_table_data, **table_opts)
            else:
                logger.error(f"表格数据格式不正确: {table_data}")
                mobject = MarkupText("[表格数据错误]")

        else:  # Fallback for unknown or unhandled types
            logger.warning(f"未知的 Markdown 元素类型: '{element_type}' 内容: '{str(content)[:50]}...'")
            mobject = MarkupText(f"[未知类型: {element_type}")

        if mobject:
            if isinstance(mobject, Line):  # Special handling for HR width
                # Will be scaled relative to container later
                pass
            logger.info(f"Markdown元素创建完成: {element_type} {mobject}")
            all_content_mobjects.add(mobject)

    if len(all_content_mobjects) == 0:
        logger.info("没有可显示的 Markdown 内容 (mobject list empty after parsing).")
        return Group()  # Return empty VGroup, placeholder handled in main function

    all_content_mobjects.arrange(DOWN, buff=ITEM_SPACING, aligned_edge=LEFT)

    return all_content_mobjects


# --- Helper Function for List Focus Animation ---
def _animate_list_with_focus_effect(
    scene: "FeynmanScene",
    list_mobj: Group,
    anim_times: dict,
    lag_ratio: float,
    rate_func,
):
    """
    为列表项实现焦点高亮效果动画

    Args:
        scene: Feynman场景对象
        list_mobj: 列表的Group对象，包含所有列表项
        anim_times: 动画时间配置字典
        lag_ratio: 延迟比例
        rate_func: 速率函数
    """
    if not list_mobj.submobjects:
        return

    # 获取列表容器的原始宽度和中心位置
    original_list_center = list_mobj.get_center()
    list_width = list_mobj.width

    # 存储每个列表项的原始位置和大小
    original_states = []
    for item in list_mobj:
        original_states.append(
            {
                "position": item.get_center().copy(),
                "scale": 1.0,  # 假设初始缩放为1
            }
        )

    item_duration = ThemeUtils.get_component_style("list", "item_duration", 2.0)
    item_anim_time = anim_times["list_item_fade"]
    # 逐个处理每个列表项
    for i, (item, original_state) in enumerate(zip(list_mobj, original_states)):
        # 第一阶段：焦点效果
        # 1. 计算缩放因子，使项目宽度匹配列表容器宽度
        if item.width > 0:
            scale_factor = min(list_width / item.width, 2.0)  # 限制最大缩放为2倍
        else:
            scale_factor = 1.5  # 默认缩放

        # 2. 计算目标位置（列表容器中心）
        target_position = original_list_center

        # 3. 创建焦点动画：缩放、移动、背景模糊
        logger.info(f"Animating list item: {item}, scale_factor: {scale_factor}, target_position: {target_position}")
        item.scale(scale_factor).move_to(target_position)

        # 播放焦点动画
        scene.play(FadeIn(item), run_time=item_anim_time * 0.5)
        scene.play(Circumscribe(item, color=RED))

        # 4. 保持焦点状态
        scene.wait(item_duration)

        # 第二阶段：恢复效果
        # 5. 恢复项目到原始位置和大小

        # 播放恢复动画
        scene.play(
            item.animate.scale(1 / scale_factor).move_to(original_state["position"]),
            run_time=item_anim_time * 0.5,
            rate_func=rate_func,
        )

        # 在项目之间添加短暂延迟（基于lag_ratio）
        if i < len(list_mobj) - 1:  # 不在最后一个项目后添加延迟
            scene.wait(max(0.01, lag_ratio * 0.5))


# --- Helper Function for Animation ---
def _animate_markdown_mobjects(
    scene: "FeynmanScene",
    display_group: Group,  # This is the group from _create_markdown_mobjects
    parsed_markdown: list[dict[str, Any]],
):
    """
    Animates the display_group (background and its content).
    """
    if not display_group or not display_group.submobjects:
        logger.info("动画中止：display_group 为空。")
        return

    lag_ratio = DEFAULT_LAG_RATIO

    anim_times = {
        "fade_shift": 0.5,
        "create": 0.7,
        "write": 1.0,  # Default for Write if not by letter
        "letter_base": 0.03,
        "letter_min": 0.3,
        "list_item_fade": 1.0,
    }
    rate_func_name = get_theme_value("animation.default_rate_func", "smooth")
    rate_func = smooth  # 默认使用smooth
    if rate_func_name == "ease_in_out_sine":
        rate_func = there_and_back
    elif rate_func_name == "ease_out":
        rate_func = rate_functions.ease_out_sine
    elif rate_func_name == "ease_in":
        rate_func = rate_functions.ease_in_sine

    for i, (mobj, item_type) in enumerate(zip(display_group, [x["type"] for x in parsed_markdown])):
        logger.info(f"Animating mobject: {mobj}, type: {item_type}")
        if scene.transition_enabled and i == 0:
            # 第一个 mobj (title) 已经由转场函数添加了
            logger.info("Transition enabled, skipping fade in for the first mobject")
            scene.add(mobj)
            continue
        if item_type == "text" and isinstance(mobj, MarkupText):
            current_run_time = min(3.0, max(anim_times["letter_min"], len(mobj.text) * anim_times["letter_base"]))
            scene.play(AddTextLetterByLetter(mobj, run_time=current_run_time))
        elif item_type == "list" and isinstance(mobj, (Group, VGroup)) and mobj.submobjects:  # Likely a list
            # 使用主题系统设置列表项动画 - 实现焦点高亮效果
            _animate_list_with_focus_effect(scene, mobj, anim_times, lag_ratio, rate_func)
        else:  # Generic mobjects (Code, Image, Table, Line)
            scene.play(FadeIn(mobj, shift=RIGHT, rate_func=rate_func))


# --- Main Function ---
def animate_markdown(scene: "FeynmanScene", content: str, title: str, narration: str, id: str = None):
    logger.info(f"Using theme: {get_current_theme().name}, background color: {config.background_color}")
    mobject_id = id or f"markdown_{str(uuid.uuid4())[:8]}"

    # 创建标题对象
    initial_title, title_text = create_title(title, scene=scene)

    converter = MarkdownToSimplifiedConverter()
    parsed_markdown = converter.convert(content)
    logger.info(parsed_markdown)

    if not parsed_markdown:
        logger.info(f"Markdown content was empty: {content}")
        return

    display_group = _create_markdown_mobjects(parsed_markdown, None)  # 不在内容中创建标题

    if not display_group or not display_group.submobjects:  # Check if _create_markdown_mobjects returned empty
        logger.info("Markdown parsed, but no mobjects were created for display.")
        return

    target_rect = scene.full_screen_rect
    display_group.move_to(target_rect.get_center())

    scale_factor = min(
        target_rect.width * 0.9 / display_group.width, target_rect.height * 0.9 / display_group.height, 1.0
    )
    logger.debug(
        f"scale_factor: {scale_factor}, display_group.width: {display_group.width}, display_group.height: {display_group.height}, target_rect.width: {target_rect.width}, target_rect.height: {target_rect.height}"
    )
    display_group.scale(scale_factor).align_to(target_rect, ORIGIN)

    # 使用主题系统准备动画参数
    theme = get_current_theme()  # 获取当前主题

    # 日志记录使用的主题
    logger.info(f"Using theme: {theme.name if theme else 'default'} for markdown animation")

    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)

    # 直接添加标题，动画已在 create_title 中处理
    scene.add(title_text)

    with scene.voiceover(text=narration) as tracker:  # noqa
        _animate_markdown_mobjects(
            scene=scene,
            display_group=display_group,
            parsed_markdown=parsed_markdown,
        )

    # 根据转场模式选择使用的标题
    final_display_group = Group(title_text, display_group)
    final_display_group.arrange(DOWN, buff=0.5)

    scene.current_mobj = final_display_group
    setattr(scene, mobject_id, final_display_group)  # HACK

    # 保存结束状态
    scene.save_scene_state("markdown", mobject_id)

    logger.info(f"Markdown content (id: {mobject_id}) display and animation complete in region 'full_screen'.")
