"""
effect: |
    在Manim场景中创建并展示表格，支持逐行高亮显示效果。表格包含表头和数据行，单元格之间有间隙，支持黄色高亮状态。

use_cases:
    - 展示统计数据表格
    - 显示对比数据分析
    - 逐步突出重要数据行
    - 展示结构化信息

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  headers:
    type: List[str]
    desc: 表格表头列表
    required: true
  data:
    type: List[List[str]]
    desc: 表格数据，每个子列表代表一行数据
    required: true
  title:
    type: str
    desc: 表格标题
    required: true
  highlight_rows:
    type: List[int]
    desc: 需要高亮的行索引列表（从0开始，不包括表头）
    default: []
  cell_spacing:
    type: float
    desc: 单元格之间的间距
    default: 0.2
  highlight_color:
    type: str
    desc: 高亮颜色，支持Manim颜色常量
    default: YELLOW
  narration:
    type: str
    desc: 在表格展示时播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_table
    params:
      headers: ["城市", "4月入职", "5月离职", "5月入职", "5月离职", "6月入职", "6月离职", "主要离职原因"]
      data:
        - ["北京", "23", "11", "15", "7", "17", "3", "家庭原因"]
        - ["上海", "43", "2", "12", "8", "19", "11", "薪资原因"]
        - ["深圳", "11", "3", "15", "2", "16", "5", "个人发展"]
        - ["杭州", "15", "1", "19", "3", "15", "0", "家庭原因"]
        - ["武汉", "17", "0", "9", "7", "7", "8", "晋升"]
        - ["广州", "9", "1", "3", "0", "2", "2", "个人问题"]
        - ["合计", "118", "18", "73", "27", "76", "29", "/"]
      title: 城市职业变化统计表
      highlight_rows: [1]
      narration: 让我们来看看这个城市职业变化的统计表格。
  - type: animate_table
    params:
      headers: ["产品", "Q1销量", "Q2销量", "增长率"]
      data:
        - ["产品A", "100", "120", "20%"]
        - ["产品B", "80", "95", "18.8%"]
        - ["产品C", "60", "90", "50%"]
      highlight_rows: [0, 2]
      narration: 这是产品销量的对比分析表格。

notes:

  - 表格内容不要超过8个字符
  - 表格会自动调整尺寸以适应屏幕
  - 高亮行会以黄色背景显示
  - 单元格之间的间距可以调整
  - 支持中文内容显示
  - 表格展示后会逐行进行高亮动画
"""

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from loguru import logger
from manim import *

from dsl.v2.animation_functions.animate_markdown import create_glow_text_simple
from dsl.v2.themes.theme_manager import get_current_theme
from utils.title_utils import create_title


def _get_theme_aware_colors():
    """
    根据当前主题获取适配的颜色配置
    
    Returns:
        dict: 包含各种单元格类型颜色的字典
    """
    current_theme = get_current_theme()
    theme_name = current_theme.name.lower()
    
    if theme_name == "dark":
        # 暗色主题配色 - 调浅一点
        return {
            "header": "#3a4a5c",      # 稍浅的深灰色表头
            "data": "#2a3541",        # 稍浅的更深的灰色数据行
            "highlight": "#5a6c7c",   # 稍浅的中灰色高亮
            "total": "#3a4a5c",       # 稍浅的深灰色合计行
            "shadow": "#00000040",    # 阴影
            "text_color": WHITE       # 白色文字
        }
    else:
        # 默认/浅色主题配色 - 调浅一点
        return {
            "header": "#6ba8f0",      # 稍浅的蓝色表头
            "data": "#f8f9fa",        # 稍浅的白色数据行
            "highlight": "#ffe55c",   # 稍浅的黄色高亮
            "total": "#f0f8ff",       # 稍浅的浅蓝色合计行
            "shadow": "#00000020",    # 阴影
            "text_color": BLACK       # 黑色文字（对于浅色背景）
        }


def _create_rounded_cell(
    content: str, width: float, height: float, cell_type: str = "data", cell_spacing: float = 0.2
) -> VGroup:
    """
    创建单个圆角表格单元格（支持主题适配）

    Args:
        content: 单元格内容
        width: 单元格宽度
        height: 单元格高度
        cell_type: 单元格类型 ("header", "data", "highlight", "total")
        cell_spacing: 单元格间距

    Returns:
        VGroup: 包含背景和文本的单元格组
    """
    # 减去间距的实际单元格尺寸
    actual_width = width - cell_spacing
    actual_height = height - cell_spacing

    # 获取主题相关颜色
    colors = _get_theme_aware_colors()
    
    # 根据类型设置颜色
    fill_color = colors.get(cell_type, colors["data"])
    text_color = colors["text_color"]
    
    # 对于高亮和表头，使用白色文字以保持对比度
    if cell_type in ["header", "highlight"]:
        text_color = WHITE

    # 创建圆角矩形背景
    cell_bg = RoundedRectangle(
        width=actual_width,
        height=actual_height,
        corner_radius=0.1,
        fill_color=fill_color,
        fill_opacity=1.0,
        stroke_width=0,
    )

    # 添加阴影效果（通过偏移的深色矩形模拟）
    shadow = RoundedRectangle(
        width=actual_width,
        height=actual_height,
        corner_radius=0.1,
        fill_color=colors["shadow"],
        fill_opacity=0.15,
        stroke_width=0,
    )
    shadow.shift(DOWN * 0.02 + RIGHT * 0.02)

    # 创建文本 - 统一字体大小
    font_size = 23  # 统一所有单元格的字体大小

    # 对于暗色主题，使用普通文本而不是发光文本
    current_theme = get_current_theme()
    if current_theme.name.lower() == "dark":
        cell_text = Text(content, font_size=font_size, color=text_color, font="PingFang SC")
    else:
        cell_text = create_glow_text_simple(content, font_size=font_size)
        if cell_type in ["header", "highlight"]:
            cell_text.set_color(text_color)

    # 如果文本太长，缩放以适应单元格
    if cell_text.width > actual_width * 0.85:
        cell_text.scale(actual_width * 0.85 / cell_text.width)
    if cell_text.height > actual_height * 0.7:
        cell_text.scale(actual_height * 0.7 / cell_text.height)

    # 创建单元格组（阴影在底层）
    cell_group = VGroup(shadow, cell_bg, cell_text)
    cell_text.move_to(cell_bg.get_center())

    return cell_group


def _create_table_with_spacing(
    headers: list[str],
    data: list[list[str]],
    title: Optional[str] = None,
    highlight_rows: list[int] = None,
    cell_spacing: float = 0.2,
) -> tuple[VGroup, list[VGroup], list[VGroup]]:
    """
    创建带间距的表格结构（不包含标题）

    Args:
        headers: 表头列表
        data: 数据行列表
        title: 表格标题（已忽略）
        highlight_rows: 高亮行索引列表
        cell_spacing: 单元格间距

    Returns:
        tuple: (完整表格组, 数据行组列表, 高亮行组列表)
    """
    if highlight_rows is None:
        highlight_rows = []

    # 设置单元格尺寸 - 第一列和后续列使用不同宽度
    base_cell_width = 1.8  # 第一列的基础宽度
    wide_cell_width = base_cell_width * 1.5  # 第二列往后的宽度（1.5倍）
    cell_height = 0.8

    # 存储所有行
    all_rows = []
    data_row_groups = []
    highlight_row_groups = []

    # 创建表头行
    header_cells = []
    for col_idx, header in enumerate(headers):
        # 第一列使用基础宽度，其他列使用1.5倍宽度
        width = base_cell_width if col_idx == 0 else wide_cell_width
        cell = _create_rounded_cell(
            content=header, width=width, height=cell_height, cell_type="header", cell_spacing=cell_spacing
        )
        header_cells.append(cell)

    header_row = VGroup(*header_cells)
    header_row.arrange(RIGHT, buff=cell_spacing)
    all_rows.append(header_row)

    # 创建数据行
    for row_idx, row_data in enumerate(data):
        row_cells = []

        # 判断行类型
        is_highlighted = row_idx in highlight_rows
        is_total_row = len(row_data) > 0 and str(row_data[0]).lower() in ["合计", "总计", "total", "sum"]

        if is_total_row:
            cell_type = "total"
        elif is_highlighted:
            cell_type = "highlight"
        else:
            cell_type = "data"

        for col_idx, cell_data in enumerate(row_data):
            # 第一列使用基础宽度，其他列使用1.5倍宽度
            width = base_cell_width if col_idx == 0 else wide_cell_width
            cell = _create_rounded_cell(
                content=str(cell_data),
                width=width,
                height=cell_height,
                cell_type=cell_type,
                cell_spacing=cell_spacing,
            )
            row_cells.append(cell)

        data_row = VGroup(*row_cells)
        data_row.arrange(RIGHT, buff=cell_spacing)
        all_rows.append(data_row)
        data_row_groups.append(data_row)

        # 如果是高亮行，记录下来
        if is_highlighted:
            highlight_row_groups.append(data_row)

    # 排列所有行
    table_group = VGroup(*all_rows)
    table_group.arrange(DOWN, buff=cell_spacing)

    # 不添加标题，直接使用表格
    final_group = table_group

    # 缩放以适应屏幕 - 调整为指定的3/4高度和4/5宽度
    max_width = 11.38   # 4/5 * 14.22 (screen width)
    max_height = 6      # 3/4 * 8 (screen height)
    
    if final_group.width > max_width:
        final_group.scale(max_width / final_group.width)
    if final_group.height > max_height:
        final_group.scale(max_height / final_group.height)

    # 居中显示
    final_group.move_to(ORIGIN)

    return final_group, data_row_groups, highlight_row_groups


def _modify_row_colors(row: VGroup, cell_type: str) -> VGroup:
    """
    修改行中所有单元格的颜色以匹配指定的单元格类型（支持主题适配）

    Args:
        row: 要修改的行VGroup
        cell_type: 单元格类型 ("header", "data", "highlight", "total")

    Returns:
        VGroup: 修改后的行
    """
    # 获取主题相关颜色
    colors = _get_theme_aware_colors()
    fill_color = colors.get(cell_type, colors["data"])
    text_color = colors["text_color"]
    
    # 对于高亮和表头，使用白色文字以保持对比度
    if cell_type in ["header", "highlight"]:
        text_color = WHITE

    # 修改每个单元格的颜色
    for cell_group in row:
        if hasattr(cell_group, "submobjects") and len(cell_group.submobjects) >= 2:
            # cell_group 结构: [shadow, cell_bg, cell_text]
            if len(cell_group.submobjects) >= 3:
                cell_bg = cell_group.submobjects[1]  # 背景矩形
                cell_text = cell_group.submobjects[2]  # 文本

                # 修改背景颜色
                cell_bg.set_fill(fill_color, opacity=1.0)
                
                # 修改文本颜色
                current_theme = get_current_theme()
                if current_theme.name.lower() == "dark":
                    # 暗色主题下直接设置文本颜色
                    cell_text.set_color(text_color)
                else:
                    # 浅色主题下保持glow效果
                    if hasattr(cell_text, "submobjects") and len(cell_text.submobjects) >= 2:
                        # 如果是glow text (VGroup with glow_text and main_text)
                        main_text = cell_text.submobjects[1] if len(cell_text.submobjects) > 1 else cell_text.submobjects[0]
                        main_text.set_color(text_color if cell_type in ["data", "total"] else WHITE)
                    else:
                        # 如果是普通text
                        cell_text.set_color(text_color if cell_type in ["data", "total"] else WHITE)

    return row


def animate_table(
    scene: "FeynmanScene",
    title: str,
    headers: list[str],
    data: list[list[str]],
    highlight_rows: list[int] = None,
    cell_spacing: float = 0.2,
    highlight_color: str = "YELLOW",
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    """
    在Manim场景中创建并展示表格，支持逐行高亮显示效果，包含标题

    Args:
        scene: Manim场景实例
        headers: 表格表头列表
        data: 表格数据，每个子列表代表一行数据
        title: 表格标题
        highlight_rows: 需要高亮的行索引列表（从0开始，不包括表头）
        cell_spacing: 单元格之间的间距
        highlight_color: 高亮颜色
        narration: 语音旁白文本
        id: 创建的Manim Mobject的唯一标识符
    """
    try:
        # 1. 初始化和基本设置
        # 创建唯一的引用 ID
        reference_id = id if id else f"table_{abs(hash(str(headers) + str(data))) % 10000}"
        
        logger.info(f"开始创建表格动画: {title}")

        # 参数验证
        if not headers or not data:
            raise ValueError("表头和数据不能为空")

        # 验证数据行列数一致性
        expected_cols = len(headers)
        for i, row in enumerate(data):
            if len(row) != expected_cols:
                logger.warning(f"数据行 {i} 的列数 ({len(row)}) 与表头列数 ({expected_cols}) 不匹配")

        if highlight_rows is None:
            highlight_rows = []

        # 2. 创建标题对象
        initial_title, title_text = create_title(title, scene=scene)

        # 3. Handle clear_current_mobject for transition support
        scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)

        # 4. 创建不高亮的表格
        normal_table, data_rows, _ = _create_table_with_spacing(
            headers=headers,
            data=data,
            title=None,  # 不在表格中包含标题，因为已经单独创建
            highlight_rows=[],  # 先不高亮任何行
            cell_spacing=cell_spacing,
        )

        # 5. 播放动画
        # 使用语音旁白和动画
        with scene.voiceover(narration) as tracker:
            # 直接添加标题，动画已在 create_title 中处理
            scene.add(title_text)
            scene.play(FadeIn(normal_table), run_time=1.5)
            scene.wait(0.5)

        # 6. Create display group with title and table
        display_group = Group(title_text, normal_table)
        display_group.arrange(DOWN, buff=0.5)

        # 7. Update current_mobject saving - ensure title is first submobject
        scene.current_mobj = display_group
        scene.save_scene_state(content_type="table", mobject_id=reference_id)

        # 如果提供了ID，保存到对象字典
        if id:
            scene.mobj_dict[id] = display_group
            logger.info(f"表格已保存到对象字典，ID: {id}")

        logger.info(f"表格动画创建完成: id='{reference_id}'")

    except Exception as e:
        logger.error(f"创建表格动画时发生错误: {str(e)}")

        # 错误恢复 - 显示错误信息
        current_theme = get_current_theme()
        if current_theme.name.lower() == "dark":
            error_text = Text(f"表格创建失败\n{str(e)[:50]}...", font_size=24, color=WHITE, font="PingFang SC")
        else:
            error_text = create_glow_text_simple(f"表格创建失败\n{str(e)[:50]}...", font_size=24)
        error_text.move_to(ORIGIN)

        with scene.voiceover(narration or "抱歉，表格创建时出现了问题。") as tracker:  # noqa: F841
            scene.play(FadeIn(error_text))

        scene.current_mobj = error_text

        if id:
            scene.mobj_dict[id] = error_text
