"""
effect: |
    在Manim场景中播放视频文件，支持可选的文本叠加和语音旁白。

use_cases:
    - 在教程或演示中展示屏幕录制或外部视频片段
    - 播放动画片段作为更复杂场景的一部分
    - 叠加解释性文本或字幕到视频内容上
    - 配合旁白同步视频演示

params:
  scene:
    type: FeynmanScene
    desc: Man<PERSON>场景实例（由系统自动传入）
  video_path:
    type: str
    desc: 要播放的视频文件的本地路径
    required: true
  overlay_text:
    type: str
    desc: 可选的叠加文本，显示在视频之上。多行文本用"\n"分隔
    default: None
  overlay_animation_delay:
    type: float
    desc: 每行文本动画之间的延迟（秒）
    default: 1.0
  narration:
    type: str
    desc: 在视频播放时同步播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_video
    params:
      video_path: assets/demo.mp4
      narration: 这是一个演示视频。
  - type: animate_video
    params:
      video_path: assets/demo.mp4
      overlay_text: "重要提示！\n请注意这个关键点"
      overlay_animation_delay: 0.5
      narration: 请注意视频中的这个重点部分。

notes:
  - 视频文件必须存在且路径正确，否则函数会记录错误并返回
  - 视频会自动缩放以适应场景
  - 叠加文本会依次显示，每行之间有指定的延迟
"""

import os
import shlex
from typing import TYPE_CHECKING, Optional

import ffmpeg  # type: ignore
from loguru import logger
from manim import *

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from dsl.v2.animation_functions.animate_markdown import create_glow_text_simple
from manim_funcs.video_mobject import VideoMobject


def _create_sequential_text_lines_group(
    overlay_text: str,
    text_font_size: int = 48,
    text_color: ManimColor = YELLOW,
) -> Group:
    """
    Creates a group of text lines, each with a surrounding rectangle,
    for sequential display over a video.
    """
    lines = overlay_text.strip().split("\n")
    line_mobjects_with_background = []
    max_text_width = 0

    # First pass: create text and rects, find max_width
    created_text_rect_pairs: list[tuple[Text, SurroundingRectangle]] = []

    for i, line_content in enumerate(lines):
        text_line = create_glow_text_simple(
            line_content,
            font_size=text_font_size,
        )
        # Transparent background for text, actual background is the rectangle
        rect = SurroundingRectangle(text_line, buff=0.2, fill_opacity=0.5, fill_color=BLACK, stroke_width=0)
        max_text_width = max(max_text_width, text_line.width)
        created_text_rect_pairs.append((text_line, rect))

    # Second pass: adjust rect widths and group
    for i, (text_line, rect) in enumerate(created_text_rect_pairs):
        # Stretch rectangle to max_text_width (plus buff)
        rect.stretch_to_fit_width(max_text_width + 2 * rect.buff)  # Use rect.buff (which is 0.2)
        line_group = VGroup(rect, text_line)  # Rectangle behind text
        line_mobjects_with_background.append(line_group)

    text_lines_group = Group(*line_mobjects_with_background)
    text_lines_group.arrange(DOWN, buff=MED_LARGE_BUFF, aligned_edge=LEFT)  # Increased buff
    return text_lines_group


def animate_video(
    scene: "FeynmanScene",
    video_path: str,
    overlay_text: Optional[str] = None,
    overlay_animation_delay: float = 1,
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    logger.info(f"Animating video: '{video_path}', Overlay: '{bool(overlay_text)}'")

    if not os.path.exists(video_path):
        logger.error(f"Video file not found: {video_path}")
        return

    target_rect = scene.full_screen_rect
    speed = 1.0

    # 处理overlay_text，如果是列表则转换为字符串用于hash计算
    overlay_text_str = ""
    if overlay_text:
        if isinstance(overlay_text, list):
            overlay_text_str = "\n".join(overlay_text)
        else:
            overlay_text_str = str(overlay_text)

    unique_id = id or f"video_display_{abs(hash(video_path + overlay_text_str)) % 10000}"
    safe_video_path = shlex.quote(video_path)  # For ffmpeg probe

    video_mobject = VideoMobject(filename=safe_video_path, speed=speed, loop=False)  # Use safe_video_path, loop=False
    video_mobject.set_z_index(0)  # Ensure video is behind text if overlaid

    # Get video duration
    video_duration = 5.0  # Default
    try:
        probe = ffmpeg.probe(safe_video_path)
        video_info_stream = next((s for s in probe.get("streams", []) if s.get("codec_type") == "video"), None)
        if video_info_stream and "duration" in video_info_stream:
            video_duration = float(video_info_stream["duration"]) / speed
            logger.info(f"Video duration for '{unique_id}': {video_duration}s (adjusted for speed {speed}x)")
        else:
            logger.warning(f"Could not find video duration for '{unique_id}'. Using default {video_duration}s.")
    except Exception as e:
        logger.warning(f"Could not get video duration for '{unique_id}': {e}. Using default {video_duration}s.")

    start_time_for_wait_calc = scene.renderer.time  # Record time before animations for wait calculation

    # --- Prepare Mobjects ---
    # final_mobject_to_animate is used for simultaneous text or no text scenarios for animation.
    # object_to_scale_and_position is used for scaling and positioning logic.
    final_mobject_to_animate: Mobject = video_mobject
    object_to_scale_and_position: Mobject = video_mobject
    text_group: Optional[Group] = None

    if overlay_text:
        text_group = _create_sequential_text_lines_group(overlay_text_str)
        text_group.set_z_index(1)  # Ensure text is above video
        text_group.move_to(video_mobject.get_center())  # Position text group on video
        scale_factor = min(video_mobject.height / text_group.height, video_mobject.width / text_group.width, 1.0)
        text_group.scale(scale_factor)
        # Group video and sequential text for combined scaling and positioning
        object_to_scale_and_position = Group(video_mobject, text_group)

    # --- Position and Scale --- (New logic based on visitor)
    target_rect = scene.full_screen_rect
    if target_rect:
        if object_to_scale_and_position.width == 0 or object_to_scale_and_position.height == 0:
            logger.warning(f"Mobject '{unique_id}' has zero width or height before scaling. Positioning at center.")
            object_to_scale_and_position.move_to(target_rect.get_center())
        else:
            # Move to center first
            object_to_scale_and_position.move_to(target_rect.get_center())

            # Calculate scale factor based on video_mobject dimensions to fit 90% of region
            # This ensures that if text makes the group larger, video is still the reference for fitting.
            base_width = video_mobject.width
            base_height = video_mobject.height

            target_fit_width = target_rect.width * 0.9
            target_fit_height = target_rect.height * 0.9

            if base_width > 0 and base_height > 0:
                scale_factor_w = target_fit_width / base_width
                scale_factor_h = target_fit_height / base_height
                scale_factor = min(scale_factor_w, scale_factor_h, 1.0)  # Cap at 1.0 (don't enlarge)
                logger.info(f"Scale factor for '{unique_id}': {scale_factor}")

                if scale_factor < 1.0:  # Only scale down
                    object_to_scale_and_position.scale(scale_factor)
                    # Re-center after scaling
                    object_to_scale_and_position.move_to(target_rect.get_center())
            else:
                logger.warning(f"Video mobject for '{unique_id}' has zero width or height. Skipping scaling.")

    else:  # Should not happen
        logger.warning("Target region 'full_screen' not found. Positioning at ORIGIN.")
        object_to_scale_and_position.move_to(ORIGIN)

    # --- Animation ---
    animation_played = False  # Flag to track if any animation command was issued

    def play_animations():
        nonlocal animation_played
        # Handle clear_current_mobject for transition support
        scene.clear_current_mobj(new_mobj=object_to_scale_and_position if scene.transition_enabled else None)

        # Always animate the video content
        scene.play(FadeIn(video_mobject))
        scene.wait(2)

        if text_group:
            scene.play(
                LaggedStart(
                    *[FadeIn(line, shift=RIGHT * 0.2) for line in text_group],
                    lag_ratio=1.0,  # Changed from 0.5 to 1.0
                    run_time=max(1.0, len(text_group) * overlay_animation_delay),
                )
            )
        animation_played = True

    with scene.voiceover(narration) as tracker:  # noqa: F841
        play_animations()

    # --- Wait for video duration --- (New logic based on visitor)
    # This wait happens regardless of narration, after main animations.
    time_elapsed_during_animation = scene.renderer.time - start_time_for_wait_calc

    # If no animation was explicitly played (e.g. only add operations without a play call for them,
    # or if play_animations didn't run any scene.play),
    # time_elapsed_during_animation might be very small.
    # The video duration itself is the primary concern for waiting.
    remaining_wait_time = max(0.1, video_duration - time_elapsed_during_animation)

    # If an 'add' operation was the only thing, and no 'play' call,
    # the time_elapsed_during_animation might not reflect the 'animation' time.
    # However, the video starts playing upon 'add' or 'FadeIn'.
    # The crucial part is that video_duration is the total time we want the video to be visible/playing.
    if animation_played:
        scene.wait(remaining_wait_time)
    else:
        # If only 'add' happened and no 'play', the scene time hasn't advanced via play commands.
        # So, just wait for the full video_duration.
        scene.wait(video_duration)

    # --- Register for clearing ---
    if text_group:
        # Use the explicitly constructed group for registration
        content_to_register = object_to_scale_and_position
    else:
        content_to_register = final_mobject_to_animate

    # Update current_mobject saving
    scene.current_mobj = content_to_register
    scene.save_scene_state("video", unique_id)
    logger.info(f"Video '{unique_id}' animation complete.")
