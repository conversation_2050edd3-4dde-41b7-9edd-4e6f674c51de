"""
effect: |
  在Manim场景中直接播放一个架构图动画。该架构图是使用ExcalidrawToolkit根据文本描述生成的视频。
  通常用于全屏展示复杂的系统架构或流程图。

use_cases:
  - 可视化软件架构
  - 展示系统组件及其交互
  - 解释数据流或业务流程
  - 需要通过Excalidraw风格图表进行说明的场景

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  content_description:
    type: str
    desc: 用于生成Excalidraw视频的文本描述，ExcalidrawToolkit将使用此描述来创建图表内容
    required: true
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  narration:
    type: str
    desc: 播放动画时同步播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_architecture_diagram
    params:
      content_description: |
        一个微服务架构图，包含前端应用、API网关和多个后端服务。
        前端应用连接到API网关，网关将请求路由到用户服务、产品服务和支付服务。
        这些服务分别连接到各自的数据库。
      title: 微服务架构图
      narration: 这个架构图展示了我们的微服务系统结构，以及各组件之间的数据流。
  - type: animate_architecture_diagram
    params:
      content_description: |
        一个数据处理流程图，展示从数据收集到分析的完整过程。
        包含数据源、数据收集器、数据存储、处理引擎和可视化工具等组件。
        显示数据如何从原始数据转换为可行性见解。
      title: 数据处理流程图
      id: data_pipeline
      narration: 这个数据处理流程展示了从原始数据到最终分析结果的完整路径。

notes:
  - ExcalidrawToolkit会根据提供的文本描述自动生成架构图视频
  - 内容描述越详细，生成的架构图越准确
  - 生成的视频会自动缩放以适应场景大小
  - 此功能需要与外部ExcalidrawToolkit组件配合使用
"""

import os
from typing import TYPE_CHECKING, Optional

import ffmpeg  # type: ignore
from loguru import logger
from manim import *

# Conditionally import FeynmanScene for type checking only
if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from dsl.v2.themes.theme_utils import ThemeUtils
from manim_funcs.video_mobject import VideoMobject
from tools.excalidraw_toolkit import ExcalidrawToolkit


def animate_architecture_diagram(
    scene: "FeynmanScene",
    content_description: str,
    title: str,
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    logger.info(f"Animating architecture diagram with description: '{content_description[:50]}...'")

    # 1. Generate a unique ID if not provided
    unique_id = id or f"arch_diagram_{abs(hash(content_description)) % 10000}"

    # 2. Create title object
    title_text = Text(
        title,
        font=ThemeUtils.get_font("heading", "Microsoft YaHei"),
        color=ThemeUtils.get_color("text_primary", WHITE),
        font_size=ThemeUtils.get_font_size("h1"),
    ).to_edge(UP, buff=0.8)

    # 3. Generate the video using ExcalidrawToolkit
    video_path: Optional[str] = None
    try:
        toolkit = ExcalidrawToolkit()
        video_path = toolkit.generate_excalidraw_video(content_description)
        if not video_path or not os.path.exists(video_path):
            logger.error(
                f"ExcalidrawToolkit failed to generate video or video path is invalid: {video_path} for ID: {unique_id}"
            )
            error_msg = Text(f"Error: Could not generate diagram '{unique_id}'", color=RED, font_size=24).to_edge(UP)
            scene.add(error_msg)
            scene.wait(2)
            scene.remove(error_msg)
            return
    except Exception as e:
        logger.error(f"Error generating Excalidraw video for ID '{unique_id}': {e}", exc_info=True)
        error_msg = Text(
            f"Error generating diagram '{unique_id}': {type(e).__name__}", color=RED, font_size=24
        ).to_edge(UP)
        scene.add(error_msg)
        scene.wait(2)
        scene.remove(error_msg)
        return

    logger.info(f"Generated architecture diagram video: {video_path} for ID: {unique_id}")

    # 4. Create VideoMobject
    video_mobject: Optional[VideoMobject] = None
    try:
        video_mobject = VideoMobject(filename=video_path, speed=1.0, loop=False)
    except Exception as e:
        logger.error(f"Failed to create VideoMobject for {video_path} (ID: {unique_id}): {e}", exc_info=True)
        error_msg = Text(f"Error: Could not load video '{unique_id}'", color=RED, font_size=24).to_edge(UP)
        scene.add(error_msg)
        scene.wait(2)
        scene.remove(error_msg)
        if video_path and os.path.exists(video_path):  # Clean up generated video if loading failed
            try:
                os.remove(video_path)
                logger.info(f"Cleaned up failed video file: {video_path}")
            except OSError as oe:
                logger.warning(f"Could not remove failed video file {video_path}: {oe}")
        return

    # 5. Position and scale the video, arrange with title
    available_width = scene.camera.frame_width * 0.9
    available_height = scene.camera.frame_height * 0.9 - title_text.height - 0.5  # Reserve space for title

    current_width = video_mobject.width
    current_height = video_mobject.height

    if current_width == 0 or current_height == 0:
        logger.warning(f"VideoMobject for {unique_id} has zero dimension. Cannot scale properly.")
    else:
        scale_factor = 1.0
        if current_width > available_width:
            scale_factor = available_width / current_width
        if current_height * scale_factor > available_height:  # Check height after width scaling consideration
            scale_factor = available_height / current_height

        if scale_factor != 1.0:
            video_mobject.scale(scale_factor)

    # Create display group with title and video content
    display_group = Group(title_text, video_mobject)
    display_group.arrange(DOWN, buff=0.5)
    display_group.move_to(ORIGIN)

    # 6. Get video duration
    video_duration = 5.0  # Default duration
    try:
        probe = ffmpeg.probe(video_path)
        video_info_stream = next((s for s in probe.get("streams", []) if s.get("codec_type") == "video"), None)
        if video_info_stream and "duration" in video_info_stream:
            video_duration = float(video_info_stream["duration"])
            logger.info(f"Video duration for '{unique_id}': {video_duration}s")
        else:
            logger.warning(
                f"Could not find video duration in probe for '{unique_id}'. Using default {video_duration}s."
            )
    except Exception as e:
        logger.warning(f"Could not get video duration for '{unique_id}': {e}. Using default {video_duration}s.")

    # 7. Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=title_text if scene.transition_enabled else None)

    # 8. Animate with or without narration
    logger.info(f"Playing architecture diagram '{unique_id}' with narration.")
    with scene.voiceover(text=narration) as tracker:  # noqa
        # Handle animation section for transition support
        if not scene.transition_enabled:
            # Normal animation - show title first
            scene.play(Write(title_text), run_time=1.0)
            scene.wait(0.5)
        else:
            scene.add(title_text)

        # Always animate the content with original effects
        scene.play(FadeIn(video_mobject))

    # 9. Update current_mobject saving - ensure title is first submobject
    scene.current_mobj = display_group
    scene.save_scene_state("architecture_diagram", unique_id)

    logger.info(f"Finished animating architecture diagram '{unique_id}'.")
