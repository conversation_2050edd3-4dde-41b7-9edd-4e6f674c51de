"""
FeynmanScene class for Manim animations with region management.
"""

from pathlib import Path
from typing import Optional

from loguru import logger
from manim import *
from manim_voiceover import VoiceoverScene

from dsl.v2.core.transition_effects import TransitionManager
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.common import Config
from utils.edgetts_service import EdgeTTSService
from utils.feynman_scene import add_wrapped_subcaption
from utils.transition_helper import load_scene_state, save_scene_state

VoiceoverScene.add_wrapped_subcaption = add_wrapped_subcaption

config_obj = Config()
manim_config = config_obj.config.get("manim", {})

# 设置像素分辨率
config.pixel_width = manim_config.get("width", 1920)
config.pixel_height = manim_config.get("height", 1080)


class FeynmanScene(VoiceoverScene):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = logger  # 使 logger 在场景实例中可用
        self.set_speech_service(EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"), create_subcaption=True)
        self.current_mobj: Mobject | None = None
        self.full_screen_rect = Rectangle(height=config.frame_height, width=config.frame_width)

        # 设置frame尺寸：保持frame_width不变，根据像素比例调整frame_height
        pixel_aspect_ratio = config.pixel_width / config.pixel_height

        # 保持默认的frame_width不变
        # config.frame_width 保持默认值 (通常是14.222)

        # 根据像素比例调整frame_height
        # 如果像素是竖屏(9:16)，那么frame也应该是竖屏比例
        config.frame_height = config.frame_width / pixel_aspect_ratio

        self.frame_width = config.frame_width
        self.frame_height = config.frame_height

        print(f"Pixel resolution: {config.pixel_width}x{config.pixel_height}")
        print(f"Frame size: {self.frame_width:.1f} x {self.frame_height:.1f}")
        print(
            f"Pixel aspect ratio: {pixel_aspect_ratio:.4f}, Frame aspect ratio: {self.frame_width/self.frame_height:.4f}"
        )

        transition_config = config_obj.config.get("transition", {})

        # 背景相关配置
        background_config = config_obj.config.get("background", {})
        self.background_type = background_config.get("type", "hyperbolic_network")  # 默认使用双曲网络背景
        self.background_config = background_config  # 保存完整的背景配置

        # 转场相关属性
        self.transition_enabled = transition_config.get("enable", False)  # 是否启用转场效果
        self.default_transition_type = transition_config.get("type", None)  # 默认转场类型，None表示随机选择
        self.transition_run_time = transition_config.get("run_time", 1.0)  # 转场动画时长

        # 场景状态序列化相关
        self.scene_states_dir = Path(transition_config.get("state_dir", "output/scene_states"))
        self.scene_states_dir.mkdir(parents=True, exist_ok=True)
        self.current_scene_id = None

    def clear_current_mobj(
        self,
        transition_type: Optional[str] = None,
        run_time: Optional[float] = None,
        new_mobj: Optional[Mobject] = None,
    ):
        """
        清除当前场景中的对象，使用精美的转场效果

        Args:
            transition_type: 转场效果类型，None表示随机选择
            run_time: 转场动画时长，None使用默认值
        """
        if self.current_mobj:
            if not self.transition_enabled:
                # 如果禁用转场，使用原来的简单淡出
                logger.warning("Transition is disabled, using simple fade out")
                self.play(FadeOut(self.current_mobj))
            else:
                # 使用转场效果
                actual_transition_type = transition_type or self.default_transition_type
                logger.info(
                    f"actual_transition_type: {actual_transition_type}, default: {self.default_transition_type}, input: {transition_type}"
                )
                actual_run_time = run_time or self.transition_run_time

                TransitionManager.apply_transition(
                    scene=self,
                    old_mobj=self.current_mobj,
                    new_mobj=new_mobj,
                    transition_type=actual_transition_type,
                    run_time=actual_run_time,
                )

            self.remove(self.current_mobj)
            self.current_mobj = None

    def save_scene_state(self, content_type: str, mobject_id: str):
        if not hasattr(self, "first_mobj"):
            scene_name = f"{self.__class__.__name__}_first"
            self.first_mobj = self.current_mobj
        else:
            scene_name = f"{self.__class__.__name__}_last"
        save_scene_state(
            scene_name=scene_name,
            current_mobj=self.current_mobj,
            save_dir=str(self.scene_states_dir),
            content_type=content_type,
            mobject_id=mobject_id,
        )

    def load_scene_state(self, scene_id: str, is_old_mobject: bool = True) -> Optional[Mobject]:
        if is_old_mobject:
            scene_name = f"{scene_id}_last"
            if not (self.scene_states_dir / (scene_name + ".pkl")).exists():
                # 可能这个分镜只有一个action，所以没有_last，只有_first
                scene_name = f"{scene_id}_first"
                if not (self.scene_states_dir / (scene_name + ".pkl")).exists():
                    # 兼容直接以mobject_id为名字存储的文件
                    scene_name = scene_id
        else:
            scene_name = f"{scene_id}_first"
        return load_scene_state(save_dir=str(self.scene_states_dir), scene_id=scene_name, is_old_mobject=is_old_mobject)

    def create_gray_background(self):
        """创建简单的纯灰色背景"""
        # 创建纯色背景
        background = Rectangle(
            width=self.frame_width,
            height=self.frame_height,
            fill_opacity=1,
            stroke_width=0,
            fill_color=GRAY_E,  # 调暗背景色，从GRAY_D改为GRAY_E
        )

        return background

    def create_hyperbolic_network_background(self):
        # 获取主题颜色
        primary_color = ThemeUtils.get_color("primary")
        background_color = ThemeUtils.get_color("background")

        # Main background - using config.frame_width and config.frame_height
        gradient = Rectangle(
            width=self.frame_width, height=self.frame_height, fill_opacity=1, stroke_width=0
        ).set_color_by_gradient([primary_color, background_color, primary_color])

        # Create hyperbolic network with regular pattern
        network = VGroup()

        # Parameters for the hyperbolic grid
        num_radial_lines = 16
        num_circles = 8
        max_radius = 10

        # Create radial lines
        for i in range(num_radial_lines):
            angle = i * TAU / num_radial_lines
            line = Line(
                ORIGIN,
                max_radius * np.array([np.cos(angle), np.sin(angle), 0]),
                stroke_width=1.2,
                stroke_opacity=0.3,
                stroke_color=primary_color,
            )
            network.add(line)

        # Create concentric circles
        for i in range(1, num_circles):
            radius = i * max_radius / num_circles
            circle = Circle(radius=radius, stroke_width=1.2, stroke_opacity=0.3, stroke_color=primary_color)
            network.add(circle)

        # Create hyperbolic curves connecting points
        for i in range(num_radial_lines):
            for j in range(i + 2, num_radial_lines, 3):  # Connect to every third line
                angle1 = i * TAU / num_radial_lines
                angle2 = j * TAU / num_radial_lines

                # Get points on different circles for a more interesting pattern
                radius1 = (i % 3 + 2) * max_radius / num_circles
                radius2 = (j % 3 + 2) * max_radius / num_circles

                start = radius1 * np.array([np.cos(angle1), np.sin(angle1), 0])
                end = radius2 * np.array([np.cos(angle2), np.sin(angle2), 0])

                # Create a curved path between points
                control = (
                    np.array([(start[0] + end[0]) * 0.5, (start[1] + end[1]) * 0.5, 0]) * 0.5
                )  # Pull control point toward center for hyperbolic effect

                curve = CubicBezier(
                    start,
                    start * 0.6 + control * 0.4,
                    end * 0.6 + control * 0.4,
                    end,
                    stroke_width=0.8,
                    stroke_opacity=0.2,
                    stroke_color=primary_color,
                )
                network.add(curve)

        # Scale the network to fit the screen
        network.scale(0.9)

        # Add a central node
        central_node = Circle(
            radius=0.15, fill_opacity=0.5, stroke_width=1.5, stroke_color=primary_color, fill_color=primary_color
        )

        # Add some smaller nodes at intersection points
        nodes = VGroup()
        for i in range(1, num_circles, 2):
            for j in range(0, num_radial_lines, 4):
                angle = j * TAU / num_radial_lines
                radius = i * max_radius / num_circles
                position = radius * np.array([np.cos(angle), np.sin(angle), 0])

                node = Circle(
                    radius=0.08, fill_opacity=0.4, stroke_width=1, stroke_color=primary_color, fill_color=primary_color
                ).move_to(position)
                nodes.add(node)

        network.add(central_node, nodes)

        # Create a clear space in the center for content
        # Use a solid white background for center to ensure text is clear
        center_mask = Circle(
            radius=5.5,
            fill_opacity=1.0,  # Fully opaque
            stroke_width=0,
            fill_color=background_color,
        )

        return gradient, network, center_mask

    def create_scrolling_text(self):
        """创建滚动文字组"""
        text_group = VGroup()  # 恢复使用VGroup，因为只有文字对象

        # 文字内容和样式
        text_content = "AI费曼"

        # 调整字体大小和间距，让屏幕显示更多文字
        font_size = 144  # 从216缩小到144
        text_spacing_x = 8   # 水平间距从12缩小到8
        text_spacing_y = 6   # 垂直间距从9缩小到6

        print(f"Frame: {self.frame_width} x {self.frame_height}")
        print(f"Text: font_size={font_size}, spacing=({text_spacing_x}, {text_spacing_y})")

        # 计算需要的行数和列数（确保覆盖整个屏幕加上更大的缓冲区）
        rows = int(self.frame_height / text_spacing_y) + 8  # 增加更多缓冲行
        cols = int(self.frame_width / text_spacing_x) + 8  # 增加更多缓冲列

        # 创建多行多列的文字
        for row in range(rows):
            for col in range(cols):

                text = Text(
                    text_content,
                    font_size=font_size,  # 使用调整后的字体大小
                    color=GRAY_D,  # 调暗文字颜色，从GRAY_B改为GRAY_D
                    opacity=0.15,  # 设置透明度，形成背景水印效果
                    weight=BOLD,  # 加粗字体
                    font="PingFang SC",  # 使用苹果系统的中文字体
                )

                # 计算位置（从左下角开始，向右上角倾斜排列，调整初始位置确保文字完整显示）
                # 考虑到文字大小和旋转角度，调整起始位置
                start_x = -self.frame_width / 2 - text_spacing_x * 1.5  # 减少左边距，让文字更早进入屏幕
                start_y = -self.frame_height / 2 - text_spacing_y * 1.5  # 减少下边距，让文字更早进入屏幕

                x_pos = start_x + col * text_spacing_x
                y_pos = start_y + row * text_spacing_y

                text.move_to(np.array([x_pos, y_pos, 0]))

                # 添加旋转角度，使文字倾斜15度（从左下到右上）
                text.rotate(PI / 12)  # 旋转15度 (PI/12 = 15度)

                text_group.add(text)

        # 保存间距信息，供滚动函数使用
        text_group.text_spacing_x = text_spacing_x
        text_group.text_spacing_y = text_spacing_y

        return text_group

    def _add_gray_background(self):
        """添加纯灰色背景环境"""
        background = self.create_gray_background()
        self.add(background)

        # 添加滚动文字效果，确保在最上层
        scrolling_text = self.create_scrolling_text()
        self.add(scrolling_text)

        # 添加滚动动画（从左下向右上滚动）
        def scroll_text(mob, dt):
            # 向右上角滚动
            mob.shift(dt * np.array([0.3, 0.2, 0]))  # 减慢速度，使滚动更平滑

            # 平滑的循环重置逻辑
            center = mob.get_center()

            # 使用动态计算的间距
            spacing_x = mob.text_spacing_x
            spacing_y = mob.text_spacing_y

            # 当文字移出屏幕右边界时，平滑重置到左边
            if center[0] > self.frame_width / 2 + spacing_x:
                mob.shift(np.array([-(self.frame_width + spacing_x * 2), 0, 0]))

            # 当文字移出屏幕上边界时，平滑重置到下边
            if center[1] > self.frame_height / 2 + spacing_y:
                mob.shift(np.array([0, -(self.frame_height + spacing_y * 2), 0]))

        scrolling_text.add_updater(scroll_text)

    def _add_hyperbolic_network_background(self):
        """添加双曲网络背景环境"""
        gradient, network, center_mask = self.create_hyperbolic_network_background()

        # Add the background to the scene
        self.add(gradient, center_mask)

        # Add the network and start rotation animation in the background
        self.add(network)

        # Create rotation updater function
        def rotate_network(mob, dt):
            mob.rotate(dt * 0.1)  # 固定旋转速度

        # Add the continuous rotation updater
        network.add_updater(rotate_network)
        self.add(network)

    def add_background(self):
        """
        根据配置添加不同类型的背景
        支持的背景类型：
        - 'gray': 纯灰色背景
        - 'hyperbolic_network': 双曲网络背景（默认）
        """
        self.logger.info(f"Adding background type: {self.background_type}")

        if self.background_type == "gray":
            self._add_gray_background()

        elif self.background_type == "hyperbolic_network":
            self._add_hyperbolic_network_background()

        else:
            # 默认背景类型或未知类型时，使用双曲网络背景
            self.logger.warning(f"Unknown background type: {self.background_type}, using hyperbolic_network as default")
            self._add_hyperbolic_network_background()
