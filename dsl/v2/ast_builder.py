# dsl/v2/ast_builder.py
from typing import Any

from loguru import logger
from pydantic import ValidationError

from .ast_nodes import (  # 导入 Pydantic 模型
    SceneNode,
)
from .parser import parse_dsl_file  # 导入我们的 parser


class ASTBuilder:
    """
    将解析后的 DSL 数据（字典）构建成 AST（Pydantic 模型）。
    主要利用 Pydantic 的数据绑定和验证能力。
    """

    def build(self, dsl_data: dict[str, Any]) -> SceneNode:
        """
        将字典数据构建成 SceneNode AST。

        Args:
            dsl_data: 从 parser 获取的 DSL 数据字典。

        Returns:
            构建好的 SceneNode AST 对象。

        Raises:
            ValidationError: 如果 DSL 数据不符合 Pydantic 模型定义。
        """
        logger.info("开始构建 AST...")
        try:
            # Pydantic 会自动处理大部分验证和类型转换
            scene_node = SceneNode(**dsl_data)
            logger.success("AST 节点基本结构构建和验证成功 (基于 Pydantic 模型)。")

            logger.success("AST 构建完成并通过所有验证。")
            return scene_node

        except ValidationError as e:
            logger.error(f"AST 构建失败：DSL 数据验证错误。\n{e}")
            raise  # 直接重新抛出 Pydantic 的验证错误
        except Exception as e:
            logger.error(f"AST 构建时发生未知错误: {e}")
            raise RuntimeError(f"AST 构建时发生未知错误: {e}") from e


# Helper function for end-to-end usage (used by tests and potentially main script)
def build_ast_from_file(file_path: str) -> SceneNode:
    """
    从 DSL 文件完整构建 AST (解析 + 构建)。

    Args:
        file_path: DSL JSON 文件的路径。

    Returns:
        构建好的 SceneNode AST 对象。

    Raises:
        各种解析或构建时可能发生的异常。
    """
    dsl_data = parse_dsl_file(file_path)
    builder = ASTBuilder()
    ast = builder.build(dsl_data)
    return ast
