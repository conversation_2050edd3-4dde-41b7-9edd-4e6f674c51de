"""
SideBySideComparison Visitor 模块

实现分屏对比布局的 visitor 函数，用于在 <PERSON><PERSON> 中创建左右分屏对比效果。
支持多种内容类型和过渡动画效果。
"""

import textwrap
from typing import TYPE_CHECKING, Any, Optional

from loguru import logger

from utils.md_to_pango import MarkdownToPangoConverter

if TYPE_CHECKING:
    from ..ast_nodes import SideBySideComparisonNode
    from ..code_generator import CodeGenerator


def visit_side_by_side_comparison(generator: "CodeGenerator", node: "SideBySideComparisonNode", **kwargs: Any) -> None:
    """
    生成 SideBySideComparisonNode 对应的 Manim 代码，创建左右分屏对比布局。

    参数:
        generator: 代码生成器实例
        node: SideBySideComparisonNode 实例
        **kwargs: 额外的参数
    """
    # This action is considered fullscreen and covers previous content.
    # target_region_id is ignored.
    logger.info("正在访问 SideBySideComparisonNode")

    # 1. Clear all existing regions before displaying the comparison
    generator._clear_all_regions()

    # 1. 创建唯一的变量名前缀
    prefix = f"comparison_{abs(hash(node.left_content + node.right_content)) % 10000}"

    # 2. 左侧内容创建
    generator._add_line("# 创建左侧内容")
    _create_content(generator, prefix, "left", node.left_content, node.left_type, node.left_title)

    # 3. 右侧内容创建
    generator._add_line("# 创建右侧内容")
    _create_content(generator, prefix, "right", node.right_content, node.right_type, node.right_title)

    node.vs_symbol = False

    # 4. 创建VS符号（如果需要）
    if node.vs_symbol:
        generator._add_line("# 创建VS符号")
        generator._add_line(f"{prefix}_vs = Text('VS', font_size=32, color=WHITE, font='Maple Mono NF CN')")

    # 5. 组合所有元素
    generator._add_line("# 组合所有元素")
    generator._add_line(f"{prefix}_group = Group(")
    generator._add_line(f"    {prefix}_left_content_group,")
    if node.vs_symbol:
        generator._add_line(f"    {prefix}_vs,")
    generator._add_line(f"    {prefix}_right_content_group")
    generator._add_line(")")
    generator._add_line(f"{prefix}_group.arrange(RIGHT, buff=1)")

    # 添加适配屏幕边界的代码
    generator._add_line("# 确保内容适合屏幕大小")
    generator._add_line("screen_width = config.frame_width * 0.95  # 留出5%的边距")
    generator._add_line(f"if {prefix}_group.width > screen_width:")
    generator._add_line(f"    scale_factor = screen_width / {prefix}_group.width")
    generator._add_line(f"    {prefix}_group.scale(scale_factor)")

    # 5.5 将 Group 赋给 self 属性并更新区域状态
    self_group_var = f"self.{prefix}_group"
    dsl_id = f"{prefix}_comparison"  # 使用 prefix 作为基础 ID
    generator._add_line("# 将对比 Group 赋值给 self 属性")
    generator._add_line(f"{self_group_var} = {prefix}_group")
    generator._add_line("# 更新 'full_screen' 区域内容状态")
    generator._update_region_content("full_screen", self_group_var, dsl_id)

    # --- 根据transition类型选择动画方式，并可能带有语音 ---
    narration_text = node.narration
    if narration_text:
        logger.info(f"为对比 {prefix} 添加语音旁白")
        generator._add_line(f"with self.voiceover({repr(narration_text)}) as tracker: # noqa: F841")
        generator._indent_block()
        generator._add_line("# 添加到场景并播放动画 (带语音)")
        if node.transition == "fadeIn":
            generator._add_line(f"self.play(FadeIn({self_group_var}))")
        elif node.transition == "slideUp":
            generator._add_line("# 先将组放置在屏幕下方")
            generator._add_line(f"{self_group_var}.shift(DOWN * 3)")
            generator._add_line(f"self.add({self_group_var})")
            generator._add_line("# 从下方滑入屏幕中央")
            generator._add_line(f"self.play({self_group_var}.animate.shift(UP * 3), run_time=1.0)")
        else:  # none
            generator._add_line(f"self.add({self_group_var})")
            # No explicit wait needed when using voiceover for 'none'
        generator._dedent_block()
    else:
        logger.info(f"对比 {prefix} 无语音旁白")
        generator._add_line("# 添加到场景并播放动画 (无语音)")
        if node.transition == "fadeIn":
            generator._add_line(f"self.play(FadeIn({self_group_var}))")
        elif node.transition == "slideUp":
            generator._add_line("# 先将组放置在屏幕下方")
            generator._add_line(f"{self_group_var}.shift(DOWN * 3)")
            generator._add_line(f"self.add({self_group_var})")
            generator._add_line("# 从下方滑入屏幕中央")
            generator._add_line(f"self.play({self_group_var}.animate.shift(UP * 3), run_time=1.0)")
        else:  # none
            generator._add_line(f"self.add({self_group_var})")
            # Add a small wait for 'none' animation without voiceover if needed
            # generator._add_line("self.wait(0.5)")

    logger.info("SideBySideComparisonNode 访问完成")


def _create_content(
    generator: "CodeGenerator", prefix: str, side: str, content: str, content_type: str, title: Optional[str]
) -> None:
    """
    辅助函数，根据内容类型创建对应的 Manim 对象。

    参数:
        generator: 代码生成器实例
        prefix: 变量名前缀
        side: 'left' 或 'right'
        content: 内容字符串或文件路径
        content_type: 内容类型
        title: 标题（可选）
    """
    # 根据内容类型创建对象
    if content_type == "text":
        if "\n" in content:
            wrapped_content = content
        else:
            wrapped_content = "\n".join(textwrap.wrap(content, width=20))
        generator._add_line(
            f'{prefix}_{side}_content = Text("""{wrapped_content}""", font_size=24, weight=BOLD, font="LXGW WenKai Mono")'
        )
        generator._add_line("# 确保文本宽度不超过屏幕的二分之一")
        generator._add_line(
            f"{prefix}_{side}_content.width = min({prefix}_{side}_content.width, config.frame_width * 0.45)"
        )
    elif content_type == "code":
        # 为代码添加宽度限制
        generator._add_line(f'{prefix}_{side}_content = Code(code_string="""{content}""", language="python")')
        generator._add_line("# 缩小代码并限制宽度")
        # generator._add_line(f"{prefix}_{side}_content.scale(0.7)")  # 缩小整体尺寸
        generator._add_line(f"if {prefix}_{side}_content.width > config.frame_width * 0.45:")
        generator._add_line(f"    {prefix}_{side}_content.width = config.frame_width * 0.45")
    elif content_type == "json":
        # 为JSON添加宽度限制
        generator._add_line(f'{prefix}_{side}_content = Code(code_string="""{content}""", language=\'json\')')
        generator._add_line("# 缩小JSON并限制宽度")
        # generator._add_line(f"{prefix}_{side}_content.scale(0.7)")  # 缩小整体尺寸
        generator._add_line(f"if {prefix}_{side}_content.width > config.frame_width * 0.45:")
        generator._add_line(f"    {prefix}_{side}_content.width = config.frame_width * 0.45")
    elif content_type == "image":
        generator._add_line(f'{prefix}_{side}_content = ImageMobject("{content}")')
        generator._add_line("# 确保图像宽度不超过屏幕的二分之一")
        generator._add_line(
            f"{prefix}_{side}_content.width = min({prefix}_{side}_content.width, config.frame_width * 0.45)"
        )
    elif content_type == "markdown":
        converter = MarkdownToPangoConverter()
        content = converter.convert(content)
        generator._add_line(
            f'{prefix}_{side}_content = MarkupText("""{content}""", font="LXGW WenKai Mono", weight=BOLD, font_size=24)'
        )
        generator._add_line("# 缩小Markdown并限制宽度")
        # generator._add_line(f"{prefix}_{side}_content.scale(0.7)")  # 缩小整体尺寸
        generator._add_line(f"if {prefix}_{side}_content.width > config.frame_width * 0.45:")
        generator._add_line(f"    {prefix}_{side}_content.width = config.frame_width * 0.45")
    else:
        generator._add_line(f"# {content_type} not supported")

    # 创建标题（如果提供）
    if title:
        generator._add_line(
            f"{prefix}_{side}_title = Text(\"{title}\", font_size=36, color=BLUE, font='LXGW WenKai Mono')"
        )
        # 创建内容组
        generator._add_line(f"{prefix}_{side}_content_group = Group({prefix}_{side}_title, {prefix}_{side}_content)")
        generator._add_line(f"{prefix}_{side}_content_group.arrange(DOWN, buff=0.5)")
        # # 确保标题与内容对齐
        # generator._add_line(f"# 确保标题与内容对齐")
        # generator._add_line(f"if {prefix}_{side}_title.width < {prefix}_{side}_content.width:")
        # generator._add_line(f"    {prefix}_{side}_title.width = {prefix}_{side}_content.width")
    else:
        generator._add_line(f"{prefix}_{side}_content_group = {prefix}_{side}_content")
