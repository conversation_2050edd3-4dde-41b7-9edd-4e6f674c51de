# dsl/v2/visitors/highlight_sequence.py

from typing import TYPE_CHECKING, Any

from loguru import logger

if TYPE_CHECKING:
    from ..ast_nodes import HighlightSequenceNode
    from ..code_generator import CodeGenerator


def parse_line_ranges(line_ranges_str: str) -> list[tuple[int, int]]:
    """
    解析行范围字符串，如 "1-3,5,7-10"，返回[(1,3), (5,5), (7,10)]

    参数:
        line_ranges_str: 行范围字符串

    返回:
        解析后的行范围列表，每个范围是一个元组(start, end)
    """
    if not line_ranges_str:
        return []

    ranges = []
    for part in line_ranges_str.split(","):
        part = part.strip()
        if "-" in part:
            start, end = part.split("-")
            ranges.append((int(start.strip()), int(end.strip())))
        else:
            line_num = int(part.strip())
            ranges.append((line_num, line_num))
    return ranges


def _generate_general_highlight(
    generator: "CodeGenerator", reference_var: str, highlight_type: str, color: str, duration: float
) -> None:
    """生成整体高亮的代码"""
    if highlight_type == "flash":
        generator._add_line(f"self.play(Flash({reference_var}, color='{color}'), run_time={duration})")
    elif highlight_type == "box":
        generator._add_line(f"highlight_box = SurroundingRectangle({reference_var}, color='{color}')")
        generator._add_line(f"self.play(Create(highlight_box), run_time={duration/2})")
        generator._add_line(f"self.wait({duration/2})")
        generator._add_line("self.play(FadeOut(highlight_box))")
    elif highlight_type == "underline":
        generator._add_line(f"underline = Underline({reference_var}, color='{color}')")
        generator._add_line(f"self.play(Create(underline), run_time={duration/2})")
        generator._add_line(f"self.wait({duration/2})")
        generator._add_line("self.play(FadeOut(underline))")
    elif highlight_type == "color":
        generator._add_line(f"original_color = {reference_var}.get_color()")
        generator._add_line(f"self.play({reference_var}.animate.set_color('{color}'), run_time={duration/2})")
        generator._add_line(f"self.wait({duration/2})")
        generator._add_line(f"self.play({reference_var}.animate.set_color(original_color))")


def _generate_line_range_highlight(
    generator: "CodeGenerator", start: int, end: int, reference_var: str, color: str
) -> None:
    """为指定的行范围生成高亮代码"""
    generator._add_line(f"# 高亮行范围: {start}-{end}")
    generator._add_line("top_y, bottom_y = None, None")
    generator._add_line("for line_idx in range(len(code_lines)):")
    generator._indent_block()
    generator._add_line(f"if {start} <= line_idx + 1 <= {end} and line_idx < len(code_lines):")
    generator._indent_block()
    generator._add_line("for submob in code_lines[line_idx].submobjects:")
    generator._indent_block()
    generator._add_line("if submob.height == 0: continue")
    generator._add_line("top_y = max(submob.get_top()[1], top_y) if top_y is not None else submob.get_top()[1]")
    generator._add_line(
        "bottom_y = min(submob.get_bottom()[1], bottom_y) if bottom_y is not None else submob.get_bottom()[1]"
    )
    generator._dedent_block()
    generator._dedent_block()
    generator._dedent_block()

    generator._add_line("if top_y is not None and bottom_y is not None:")
    generator._indent_block()
    generator._add_line("height, mid_y = top_y - bottom_y, (top_y + bottom_y) / 2")
    generator._add_line(f"rect = Rectangle(height=height, width={reference_var}.width, color={color})")
    generator._add_line(
        f"rect.move_to([0, mid_y, 0]).stretch_to_fit_width({reference_var}.width).align_to({reference_var}, LEFT)"
    )
    generator._add_line(f"rect.set_y(mid_y).set_fill({color}, opacity=0.3).set_stroke(width=0)")
    generator._add_line("rectangles.append(rect)")
    generator._add_line("highlight_anims.append(FadeIn(rect))")
    generator._dedent_block()


def _generate_code_line_highlight(
    generator: "CodeGenerator", reference_var: str, line_ranges: list[tuple[int, int]], color: str, duration: float
) -> None:
    """生成代码行高亮的代码"""
    generator._add_line("# 创建高亮动画")
    generator._add_line("highlight_anims = []")
    generator._add_line("rectangles = []")

    # 对每个行范围创建高亮效果
    for start, end in line_ranges:
        _generate_line_range_highlight(generator, start, end, reference_var, color)

    # 播放高亮动画
    generator._add_line("if highlight_anims:")
    generator._indent_block()
    generator._add_line("self.play(*highlight_anims, run_time=0.5)")
    generator._add_line(f"self.wait({duration})")
    generator._add_line("self.play(*[FadeOut(rect) for rect in rectangles], run_time=0.5)")
    generator._dedent_block()


def visit_highlight_sequence(generator: "CodeGenerator", node: "HighlightSequenceNode", **kwargs: Any) -> None:
    """
    生成 HighlightSequenceNode 对应的 Manim 代码。

    按顺序高亮一系列预先定义的元素，支持对代码元素的行高亮。

    参数:
        generator: 代码生成器实例
        node: HighlightSequenceNode 实例
        **kwargs: 额外的参数，由 CodeGenerator 传入
    """
    logger.info(f"正在访问 HighlightSequenceNode: elements={node.elements}, highlight_type={node.highlight_type}")

    # 准备并可能带有语音的高亮序列
    narration_text = node.narration

    if narration_text:
        logger.info("为高亮序列添加语音旁白")
        generator._add_line(f"with self.voiceover({repr(narration_text)}) as tracker: # noqa: F841")
        generator._indent_block()
        generator._add_line("# 按顺序高亮指定的元素 (带语音)")

        # 解析行范围（如果有）
        line_ranges = []
        if node.lines:
            line_ranges = parse_line_ranges(node.lines)
            logger.debug(f"解析行范围: {node.lines} -> {line_ranges}")

        for element_id in node.elements:
            reference_var = f"self.{element_id}_content"
            generator._add_line(f"# 高亮元素: {element_id}")
            generator._add_line(f"if hasattr(self, '{element_id}_content'):")
            generator._indent_block()
            if not line_ranges:
                _generate_general_highlight(
                    generator, reference_var, node.highlight_type, node.color, node.duration_per_item
                )
            else:
                generator._add_line("# 检查是否是代码对象")
                generator._add_line(f"if isinstance({reference_var}, Code):")
                generator._indent_block()
                generator._add_line("# 获取代码行对象")
                generator._add_line(f"code_lines = {reference_var}.code_lines")
                generator._add_line("if len(code_lines) > 0:")
                generator._indent_block()
                _generate_code_line_highlight(generator, reference_var, line_ranges, node.color, node.duration_per_item)
                generator._dedent_block()  # 结束len(code_lines)检查
                generator._dedent_block()  # 结束Code判断
                generator._add_line("else:")
                generator._indent_block()
                generator._add_line("# 不是代码对象，使用整体高亮")
                generator._add_line(
                    f"highlight_box = SurroundingRectangle({reference_var}, color={node.color}, buff=0.1)"
                )
                generator._add_line(f"self.play(Create(highlight_box), run_time={node.duration_per_item/2})")
                generator._add_line(f"self.wait({node.duration_per_item/2})")
                generator._add_line("self.play(FadeOut(highlight_box))")
                generator._dedent_block()
            generator._dedent_block()  # 结束 hasattr 检查
            generator._add_line("else:")
            generator._indent_block()
            generator._add_line(f"logger.warning(f\"找不到ID为 '{element_id}' 的元素，无法高亮\")")
            generator._dedent_block()
        generator._dedent_block()  # End of with voiceover block
    else:
        logger.info("高亮序列无语音旁白")
        generator._add_line("# 按顺序高亮指定的元素 (无语音)")
        line_ranges = []
        if node.lines:
            line_ranges = parse_line_ranges(node.lines)
            logger.debug(f"解析行范围: {node.lines} -> {line_ranges}")

        for element_id in node.elements:
            reference_var = f"self.{element_id}_content"
            generator._add_line(f"# 高亮元素: {element_id}")
            generator._add_line(f"if hasattr(self, '{element_id}_content'):")
            generator._indent_block()
            if not line_ranges:
                _generate_general_highlight(
                    generator, reference_var, node.highlight_type, node.color, node.duration_per_item
                )
            else:
                generator._add_line("# 检查是否是代码对象")
                generator._add_line(f"if isinstance({reference_var}, Code):")
                generator._indent_block()
                generator._add_line("# 获取代码行对象")
                generator._add_line(f"code_lines = {reference_var}.code_lines")
                generator._add_line("if len(code_lines) > 0:")
                generator._indent_block()
                _generate_code_line_highlight(generator, reference_var, line_ranges, node.color, node.duration_per_item)
                generator._dedent_block()  # 结束len(code_lines)检查
                generator._dedent_block()  # 结束Code判断
                generator._add_line("else:")
                generator._indent_block()
                generator._add_line("# 不是代码对象，使用整体高亮")
                generator._add_line(
                    f"highlight_box = SurroundingRectangle({reference_var}, color={node.color}, buff=0.1)"
                )
                generator._add_line(f"self.play(Create(highlight_box), run_time={node.duration_per_item/2})")
                generator._add_line(f"self.wait({node.duration_per_item/2})")
                generator._add_line("self.play(FadeOut(highlight_box))")
                generator._dedent_block()
            generator._dedent_block()  # 结束 hasattr 检查
            generator._add_line("else:")
            generator._indent_block()
            generator._add_line(f"logger.warning(f\"找不到ID为 '{element_id}' 的元素，无法高亮\")")
            generator._dedent_block()

            # 在元素之间添加短暂暂停 (无语音时保留)
            if element_id != node.elements[-1]:
                generator._add_line("self.wait(0.5)  # 元素间暂停")

    logger.info("HighlightSequenceNode 访问完成")
