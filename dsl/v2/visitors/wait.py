from typing import TYPE_CHECKING

from loguru import logger

# Type hint for <PERSON>Generator to avoid circular import
if TYPE_CHECKING:
    from ..ast_nodes import WaitNode
    from ..code_generator import CodeGenerator


def visit_wait(generator: "CodeGenerator", node: "WaitNode", **kwargs):
    """Generates the self.wait() code."""  # English docstring
    logger.debug(f"Visiting WaitNode: duration={node.duration}")
    generator._add_line(f"# self.wait({node.duration}) (disabled)")
