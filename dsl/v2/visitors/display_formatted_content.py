# dsl/v2/visitors/display_formatted_content.py

import os
import textwrap
from typing import TYPE_CHECKING, Any

from loguru import logger

from utils.md_to_pango import MarkdownToPangoConverter

if TYPE_CHECKING:
    from ..ast_nodes import DisplayFormattedContentNode
    from ..code_generator import CodeGenerator


def visit_display_formatted_content(
    generator: "CodeGenerator", node: "DisplayFormattedContentNode", **kwargs: Any
) -> None:
    """
    Generates Manim code for DisplayFormattedContentNode.

    Handles various content types like text, code, JSON, and Markdown (converted to Pango).

    Args:
        generator: The CodeGenerator instance.
        node: The DisplayFormattedContentNode instance.
        **kwargs: Additional arguments passed from the CodeGenerator.
    """
    target_region_id = getattr(node, "target_region_id", None) or "main"
    logger.info(
        f"Visiting DisplayFormattedContentNode: type={node.content_type}, anim={node.animation}, region='{target_region_id}'"
    )

    # Create a unique variable name prefix
    content_type = node.content_type
    # Include id in hash for uniqueness
    content_hash = abs(hash(node.content + str(node.id))) % 10000
    prefix = f"content_{content_hash}"

    # Create a reference variable name, preferring node.id if available
    if node.id:
        reference_id = node.id
    else:
        # Auto-generate an ID based on type and hash if none provided
        reference_id = f"{content_type}_{content_hash}"

    reference_var = f"self.{reference_id}_content"

    if node.content_type == "image":
        logger.info(
            f"Processing image content: {node.content} (ID: {reference_id}), Requested Animation: {node.animation}"
        )
        target_region_id = "full_screen"
    annotation = node.annotation

    # 1. Clear the target region
    generator._clear_region(target_region_id)

    # Generate code to create the appropriate Manim object
    generator._add_line(f"# Create formatted content: {content_type} type (ID: {reference_id})")

    # Prepare content based on type and create the object
    content_str = node.content
    if content_type == "markdown":
        converter = MarkdownToPangoConverter()
        logger.debug(f"Original Markdown for {reference_id}:\\n{content_str}")
        pango_content = converter.convert(content_str)
        logger.debug(f"Converted Pango Markup for {reference_id}:\\n{pango_content}")
        # Use f-string formatting for multi-line Pango content passed to MarkupText
        generator._add_line(f"{prefix}_obj = MarkupText(")
        # Use triple quotes within the f-string for the multi-line content
        generator._add_line(f'    f"""{pango_content}""",')  # Pass converted Pango markup
        generator._add_line("    font='LXGW WenKai Mono',")
        generator._add_line("    font_size=32,")
        generator._add_line("    weight=BOLD,")
        generator._add_line("    stroke_width=2,")
        generator._add_line("    stroke_color='#333333',")
        generator._add_line("    line_spacing=1.5,")
        generator._add_line(")")
    elif content_type == "text":
        # Use MarkupText with coordinated text color, no background box
        if "\n" in content_str:
            wrapped_content = content_str
        else:
            wrapped_content = "\n".join(textwrap.wrap(content_str, width=40))
        generator._add_line(f"{prefix}_obj = Text(")
        generator._add_line(f'    """{wrapped_content}""",')
        generator._add_line("    font='LXGW WenKai Mono',")
        generator._add_line("    font_size=32,")
        generator._add_line("    weight=BOLD,")
        generator._add_line("    line_spacing=1.5,")
        generator._add_line(")")
    elif content_type in ["code", "json"]:
        language = node.language or ("json" if content_type == "json" else "python")
        style = node.style or "monokai"  # Use a common default style like 'monokai'
        generator._add_line(f"{prefix}_obj = Code(")
        generator._add_line(f'code_string="""{content_str}""",')
        generator._add_line(f"language='{language}',")
        generator._add_line("background='window',")
        generator._add_line('paragraph_config={"font": "LXGW WenKai Mono"},')
        generator._add_line(f"formatter_style='{style}',")
        generator._add_line(")")
    elif content_type == "image":
        image_path = node.content
        # Log the *provided* animation value, even if we override it later
        logger.info(
            f"Processing image content: {image_path} (ID: {reference_id}), Requested Animation: {node.animation}"
        )

        if not os.path.exists(image_path):
            logger.warning(f"Image file not found: {image_path}. Skipping node {reference_id}.")
            generator._add_line(f"# WARNING: Image file not found: {image_path}")
            generator._add_line(f"{reference_var} = None # Placeholder for missing image")
            generator._update_region_content(target_region_id, None, reference_id)  # Indicate absence
            return  # Stop processing this node

        generator._add_line(f"# Create image object: (ID: {reference_id})")
        # Ensure path string is properly escaped for the generated code
        generator._add_line(f"{prefix}_obj = ImageMobject({repr(image_path)})")
    else:  # Handle unknown content type EARLY before positioning/scaling
        logger.warning(f"Unsupported content type '{node.content_type}' for node {reference_id}. Skipping.")
        generator._add_line(f"# WARNING: Unsupported content type '{node.content_type}'")
        generator._add_line(f"{reference_var} = None # Placeholder for unsupported type")
        generator._update_region_content(target_region_id, None, reference_id)  # Indicate absence/error
        return  # Stop processing this node

    # Determine the effective animation type based on defaults and validation
    is_image = content_type == "image"
    user_animation = node.animation  # Animation specified by the user (can be None)
    effective_animation_type = None

    # Define default animations
    default_anim = {
        "image": "zoomAndPan",  # New default for images
        "text": "write",
        "markdown": "write",
        "code": "fadeIn",
        "json": "fadeIn",
    }
    # Define valid animations per type (including the new logical type for images)
    valid_anims = {
        "image": ["fadeIn", "grow", "zoomAndPan", "none"],
        "text": ["write", "fadeIn", "none"],
        "markdown": ["write", "fadeIn", "none"],
        "code": ["fadeIn", "write", "none"],
        "json": ["fadeIn", "write", "none"],
    }

    if user_animation is None:
        effective_animation_type = default_anim.get(content_type, "fadeIn")  # Use default
        logger.debug(f"No animation specified for {reference_id}, using default '{effective_animation_type}'")
    else:
        allowed_anims = valid_anims.get(content_type, [])
        if user_animation in allowed_anims:
            effective_animation_type = user_animation  # Use valid user choice
        else:
            effective_animation_type = default_anim.get(content_type, "fadeIn")  # Fallback to default
            logger.warning(
                f"Invalid or unsupported animation '{user_animation}' for {content_type} node {reference_id}. "
                f"Falling back to default '{effective_animation_type}'."
            )

    # Special check: zoomAndPan requires a valid target region
    target_rect = generator._get_region_rect(target_region_id)
    if content_type == "image":
        effective_animation_type = "zoomAndPan"
    if effective_animation_type == "zoomAndPan" and not target_rect:
        logger.warning(
            f"Cannot perform 'zoomAndPan' for {reference_id} because target region '{target_region_id}' was not found. Falling back to 'fadeIn'."
        )
        effective_animation_type = "fadeIn"

    logger.info(f"Effective animation for {reference_id}: '{effective_animation_type}'")

    # --- Positioning, Scaling, and Pre-calculation for Animations ---
    positioning_applied = False
    pan_animation_vars = {}  # Reset pan vars dict

    if target_rect:
        target_rect_center_code = (
            f"[{target_rect.get_center()[0]:.3f}, {target_rect.get_center()[1]:.3f}, {target_rect.get_center()[2]:.3f}]"
        )
        target_width_code = f"{target_rect.width:.3f}"
        target_height_code = f"{target_rect.height:.3f}"

        # --- Handling for Image zoomAndPan --- (Only if it's the effective type)
        if is_image and effective_animation_type == "zoomAndPan":
            logger.debug(f"Applying zoomAndPan positioning for {reference_id}")
            generator._add_line("# -- Positioning & Scaling for zoomAndPan --")
            # Get original dimensions *before* scaling
            generator._add_line(f"{prefix}_orig_width = {prefix}_obj.width")
            generator._add_line(f"{prefix}_orig_height = {prefix}_obj.height")
            # Check aspect ratio (use original dimensions)
            generator._add_line(f"if {prefix}_orig_width > {prefix}_orig_height:")  # Wide image -> Pan Left
            generator._indent_block()
            generator._add_line(f"logger.debug('{reference_id}: Wide image detected, preparing for left pan.')")
            generator._add_line(f"{prefix}_target_height = {target_height_code} * 0.95")
            generator._add_line(
                f"{prefix}_scale_factor = {prefix}_target_height / {prefix}_orig_height if {prefix}_orig_height > 0 else 1.0"
            )
            generator._add_line(f"{prefix}_obj.scale({prefix}_scale_factor)")
            generator._add_line(f"{prefix}_scaled_width = {prefix}_obj.width")
            # 计算图片左边缘与目标区域右边缘对齐的位置
            generator._add_line(
                f"{prefix}_right_edge_pos = np.array({target_rect_center_code}) + RIGHT * ({target_width_code} / 2)"
            )
            # 计算图片中心点到左边缘的偏移量
            generator._add_line(f"{prefix}_to_left_edge = RIGHT * ({prefix}_scaled_width / 2)")
            # 计算初始位置（图片左边缘与目标区域右边缘对齐）
            generator._add_line(f"{prefix}_initial_pos = {prefix}_right_edge_pos + {prefix}_to_left_edge")
            # 计算移动距离，使图片右边缘与目标区域右边缘对齐
            generator._add_line(f"{prefix}_shift_dist = {prefix}_scaled_width")
            # 设置向左移动
            generator._add_line(f"{prefix}_pan_direction = LEFT")
            # 移动方向已在上面设置
            generator._dedent_block()
            generator._add_line("else:")  # Long/Square image -> Pan Up
            generator._indent_block()
            generator._add_line(f"logger.debug('{reference_id}: Long/Square image detected, preparing for up pan.')")
            generator._add_line(f"{prefix}_target_width = {target_width_code} * 0.95")
            generator._add_line(
                f"{prefix}_scale_factor = {prefix}_target_width / {prefix}_orig_width if {prefix}_orig_width > 0 else 1.0"
            )
            generator._add_line(f"{prefix}_obj.scale({prefix}_scale_factor)")
            generator._add_line(f"{prefix}_scaled_height = {prefix}_obj.height")
            # 初始位置：图片顶边与目标区域底边对齐（图片在画面下方）
            generator._add_line(
                f"{prefix}_initial_pos = np.array({target_rect_center_code}) + DOWN * ({target_height_code} / 2 + {prefix}_scaled_height / 2)"
            )
            # 移动距离：图片整体向上移动，直到底边与区域底边对齐
            generator._add_line(f"{prefix}_shift_dist = {prefix}_scaled_height")
            # 设置向上移动
            generator._add_line(f"{prefix}_pan_direction = UP")
            generator._dedent_block()

            # Store var names for animation block
            pan_animation_vars["initial_pos_var"] = f"{prefix}_initial_pos"
            pan_animation_vars["shift_distance_var"] = f"{prefix}_shift_dist"
            pan_animation_vars["pan_direction_var"] = f"{prefix}_pan_direction"  # Store direction
            positioning_applied = True

        # --- Standard Positioning & Scaling (if not zoomAndPan) ---
        if not positioning_applied:
            logger.debug(f"Applying standard positioning for {reference_id}")
            generator._add_line("# -- Standard Positioning & Scaling --")
            generator._add_line(f"# Position in region '{target_region_id}' center")
            generator._add_line(f"{prefix}_obj.move_to({target_rect_center_code})")  # Use pre-calculated center
            generator._add_line("# Scale to fit region (with padding)")
            generator._add_line(f"target_width = {target_width_code} * 0.9")  # Padding
            generator._add_line(f"target_height = {target_height_code} * 0.9")
            generator._add_line(f"current_width = {prefix}_obj.width")
            generator._add_line(f"current_height = {prefix}_obj.height")
            generator._add_line("if current_width > 0 and current_height > 0:")
            generator._add_line("    scale_w = target_width / current_width")
            generator._add_line("    scale_h = target_height / current_height")
            generator._add_line("    scale_factor = min(scale_w, scale_h, 1.0)")
            generator._add_line("    if scale_factor < 1.0:")
            generator._add_line(f"        {prefix}_obj.scale(scale_factor)")
            positioning_applied = True

    else:  # Fallback if target_rect not found
        if not positioning_applied:  # Only apply if no other positioning happened
            logger.warning(f"Region '{target_region_id}' not found for {reference_id}. Positioning at ORIGIN.")
            generator._add_line(f"# Region '{target_region_id}' not found, positioning at ORIGIN")
            generator._add_line(f"{prefix}_obj.move_to(ORIGIN)")
            positioning_applied = True  # Still mark as positioned

    # Store the object reference
    generator._add_line("# Store object reference")
    generator._add_line(f"{reference_var} = {prefix}_obj")
    generator._add_line(f"text_{reference_id} = None")
    # Add numpy import if needed for pan calculations
    if pan_animation_vars:
        generator._add_import("numpy", as_name="np")

    # --- Animation and Narration ---
    narration_text = node.narration
    anim_target = reference_var

    # Retrieve pre-calculated vars if needed for zoomAndPan
    initial_pos_var = pan_animation_vars.get("initial_pos_var")
    shift_distance_var = pan_animation_vars.get("shift_distance_var")
    pan_direction_var = pan_animation_vars.get("pan_direction_var")  # Get direction

    # Generate animation code
    if narration_text:
        generator._add_line(f"with self.voiceover(text={repr(narration_text)}) as tracker: # noqa: F841")
        generator._indent_block()
        generator._add_line(
            f"# Add {content_type} '{reference_id}' using animation '{effective_animation_type}' (with voiceover)"
        )
        # Define run times based on tracker.duration
        pan_run_time = "min(tracker.duration, 5.0)"
        fade_run_time = "min(tracker.duration, 1.0)"  # Shorter fade for pan
        grow_run_time = "min(tracker.duration, 1.5)"
        write_run_time = "min(tracker.duration, 4.0)"  # Text write

        # --- zoomAndPan Animation --- (Use the stored direction)
        if is_image and effective_animation_type == "zoomAndPan":
            # Initial placement and fade-in
            generator._add_line(f"{anim_target}.move_to({initial_pos_var}) # Set initial position")
            generator._add_line(f"self.play(FadeIn({anim_target}), run_time={fade_run_time})")
            # Conditional panning
            generator._add_line(f"if {shift_distance_var} > 0.01:")  # Add tolerance
            # Use the calculated direction variable
            generator._indent_block()
            generator._add_line(
                f"self.play({anim_target}.animate.shift({pan_direction_var} * {shift_distance_var}), run_time={pan_run_time})"
            )
            generator._dedent_block()
            generator._add_line(f"if {repr(annotation)}:")
            generator._indent_block()
            # Post-pan: scale & move image into left half and show annotation
            generator._add_line(f"target_half_width = {target_width_code} / 2")
            generator._add_line(f"scale_w2 = min(1.0, target_half_width / {prefix}_obj.width)")
            generator._add_line(f"scale_h2 = min(1.0, {target_height_code} * 0.9 / {prefix}_obj.height)")
            generator._add_line("scale_factor2 = min(scale_w2, scale_h2)")
            generator._add_line(
                f"left_half_center = np.array({target_rect_center_code}) + LEFT * ({target_width_code} / 4)"
            )
            generator._add_line(
                f"self.play({prefix}_obj.animate.scale(scale_factor2).move_to(left_half_center), run_time=1)"
            )
            generator._add_line(
                f"text_{reference_id} = Text({repr(annotation)}, font='LXGW WenKai Mono').scale_to_fit_width({target_width_code} / 4)"
            )
            generator._add_line(
                f"text_{reference_id}.move_to(np.array({target_rect_center_code}) + RIGHT * ({target_width_code} / 4))"
            )
            generator._add_line(f"self.play(AddTextLetterByLetter(text_{reference_id}))")
            generator._dedent_block()
            generator._add_line("else:")
            generator._indent_block()
            # Wait out remaining voiceover duration if no pan needed
            generator._add_line(f"self.wait(max(0.1, tracker.duration - {fade_run_time}))")
            generator._dedent_block()

        elif effective_animation_type == "write" and content_type in ["text", "markdown"]:
            generator._add_line(f"self.play(AddTextLetterByLetter({anim_target}), run_time={write_run_time})")
        elif effective_animation_type == "fadeIn":
            generator._add_line(f"self.play(FadeIn({anim_target}), run_time={fade_run_time})")
        elif effective_animation_type == "grow" and is_image:
            generator._add_line(f"self.play(GrowFromCenter({anim_target}), run_time={grow_run_time})")
        else:  # 'none' or fallback
            generator._add_line(f"self.play(FadeIn({anim_target}))")
            generator._add_line("self.wait(tracker.duration)")
        generator._dedent_block()
    else:  # No voiceover
        generator._add_line(
            f"# Add {content_type} '{reference_id}' using animation '{effective_animation_type}' (no voiceover)"
        )
        # Define default run times
        pan_run_time = "3.0"
        fade_run_time = "0.75"  # Shorter fade for pan
        grow_run_time = "1.0"
        write_run_time = "2.0"  # Text write

        # --- zoomAndPan Animation --- (Use the stored direction)
        if is_image and effective_animation_type == "zoomAndPan":
            generator._add_line(f"{anim_target}.move_to({initial_pos_var})")
            generator._add_line(f"self.play(FadeIn({anim_target}), run_time={fade_run_time})")
            generator._add_line(f"if {shift_distance_var} > 0.01:")
            generator._add_line(
                f"    self.play({anim_target}.animate.shift({pan_direction_var} * {shift_distance_var}), run_time={pan_run_time})"
            )
            generator._add_line("else:")
            generator._add_line(f"    self.wait({pan_run_time}) # Wait fixed duration")
            # Post-pan: scale & move image into left half and show annotation
            generator._add_line(f"    target_half_width = {target_width_code} / 2")
            generator._add_line(f"    scale_w2 = min(1.0, target_half_width / {prefix}_obj.width)")
            generator._add_line(f"    scale_h2 = min(1.0, {target_height_code} / {prefix}_obj.height)")
            generator._add_line("    scale_factor2 = min(scale_w2, scale_h2)")
            generator._add_line(f"    {prefix}_obj.scale(scale_factor2)")
            generator._add_line(
                f"    left_half_center = np.array({target_rect_center_code}) + LEFT * ({target_width_code} / 4)"
            )
            generator._add_line(f"    {prefix}_obj.move_to(left_half_center)")
            generator._add_line(f"    text_{reference_id} = Text({repr(annotation)})")
            generator._add_line(
                f"    text_{reference_id}.move_to(np.array({target_rect_center_code}) + RIGHT * ({target_width_code} / 4))"
            )
            generator._add_line(f"    self.play(Write(text_{reference_id}), run_time={write_run_time})")
        elif effective_animation_type == "write" and content_type in ["text", "markdown"]:
            generator._add_line(f"self.play(AddTextLetterByLetter({anim_target}), run_time={write_run_time})")
        elif effective_animation_type == "fadeIn":
            generator._add_line(f"self.play(FadeIn({anim_target}), run_time={fade_run_time})")
        elif effective_animation_type == "grow" and is_image:
            generator._add_line(f"self.play(GrowFromCenter({anim_target}), run_time={grow_run_time})")
        else:  # 'none' or fallback
            generator._add_line(f"self.add({anim_target})")
            generator._add_line("self.wait(0.5)")

    generator._add_line(f"{prefix}_all_obj = Group({prefix}_obj)")
    generator._add_line(f"if text_{reference_id}:")
    generator._add_line(f"    {prefix}_all_obj.add(text_{reference_id})")
    generator._add_line(f"{reference_var} = {prefix}_all_obj")

    # Update CodeGenerator's region content state
    generator._update_region_content(target_region_id, reference_var, reference_id)

    logger.info(f"DisplayFormattedContentNode visit completed for {reference_id}")
