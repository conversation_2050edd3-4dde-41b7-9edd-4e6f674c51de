# dsl/v2/visitors/animate_chart.py

from typing import TYPE_CHECKING, Any, Union

# Import necessary classes and constants
from loguru import logger

if TYPE_CHECKING:
    from ..ast_nodes import AnimateChartNode
    from ..code_generator import CodeGenerator


# 添加辅助函数
def _get_colors(generator: "CodeGenerator", num_datasets: int) -> str:
    """为多组数据生成不同的颜色列表"""
    colors = ["BLUE", "RED", "GREEN", "YELLOW", "PURPLE", "ORANGE", "TEAL"]
    # 如果数据组数超过默认颜色列表长度，则循环使用
    if num_datasets <= len(colors):
        return f"[{', '.join(colors[:num_datasets])}]"
    else:
        # 循环使用颜色
        extended_colors = []
        for i in range(num_datasets):
            extended_colors.append(colors[i % len(colors)])
        return f"[{', '.join(extended_colors)}]"


def _generate_legend(generator: "CodeGenerator", prefix: str, dataset_names: list[str], colors: str) -> None:
    """生成图例代码"""
    generator._add_line("# 生成图例")
    generator._add_line(f"{prefix}_legend_items = []")
    generator._add_line(f"{prefix}_colors = {colors}")
    generator._add_line(f"for i, name in enumerate({prefix}_dataset_names):")
    generator._add_line("    # 创建颜色块")
    generator._add_line(
        f"    color_rect = Rectangle(height=0.2, width=0.4, fill_opacity=1, fill_color={prefix}_colors[i], stroke_width=1)"
    )
    generator._add_line("    # 创建文本标签")
    generator._add_line("    label = Text(name, font_size=18, font='Maple Mono NF CN')")
    generator._add_line("    # 组合矩形和标签")
    generator._add_line("    label.next_to(color_rect, RIGHT, buff=0.1)")
    generator._add_line("    legend_item = VGroup(color_rect, label)")
    generator._add_line("    # 添加到图例组")
    generator._add_line(f"    {prefix}_legend_items.append(legend_item)")

    # 排列图例项
    generator._add_line("# 排列图例项")
    generator._add_line(f"{prefix}_legend = VGroup()")
    generator._add_line(f"for i, item in enumerate({prefix}_legend_items):")
    generator._add_line("    if i == 0:")
    generator._add_line(f"        {prefix}_legend.add(item)")
    generator._add_line("    else:")
    generator._add_line("        # 将新项放在前一项的右侧")
    generator._add_line(f"        item.next_to({prefix}_legend, RIGHT, buff=0.3)")
    generator._add_line(f"        {prefix}_legend.add(item)")

    # 放置图例 - 修改为标题下方
    generator._add_line("# 放置图例到标题下方")
    # 图例在有标题时会由visit_animate_chart中的代码处理位置


def _normalize_data(data: Union[dict, list[dict]]) -> tuple[list[dict], list[str]]:
    """将数据标准化为列表形式，并返回数据集名称"""
    if isinstance(data, dict):
        # 如果是单个dict，将其包装为列表
        normalized_data = [data]
        dataset_names = ["数据集1"]  # 默认数据集名称
    else:
        normalized_data = data
        # 使用索引作为默认数据集名称
        dataset_names = [f"数据集{i+1}" for i in range(len(data))]

    return normalized_data, dataset_names


def visit_animate_chart(generator: "CodeGenerator", node: "AnimateChartNode", **kwargs: Any) -> None:
    """
    生成 AnimateChartNode 对应的 Manim 代码。

    创建并动画展示指定类型的图表（折线图、柱状图或雷达图）。

    参数:
        generator: 代码生成器实例
        node: AnimateChartNode 实例，包含图表的所有参数
        **kwargs: 额外的参数，由 CodeGenerator 传入
    """
    # This action is considered fullscreen and covers previous content.
    # target_region_id is ignored.
    logger.info(f"正在访问 AnimateChartNode: chart_type={node.chart_type}, animation_style={node.animation_style}")

    # 1. Clear all existing regions before displaying the chart
    generator._clear_all_regions()

    # 创建一个唯一的变量名前缀，避免命名冲突
    prefix = f"chart_{abs(hash(str(node.data))) % 10000}"

    # 正常添加标题的代码
    if node.title:
        generator._add_line("# 创建图表标题")
        generator._add_line(
            f'{prefix}_title_obj = Text("{node.title}", font_size=32, color=BLUE, font="Maple Mono NF CN")'
        )
        generator._add_line(f"{prefix}_title_obj.to_edge(UP)")

    # 标准化数据结构
    normalized_data, dataset_names = _normalize_data(node.data)

    # 获取数据集名称，如果提供了dataset_names选项则使用它
    if node.options and "dataset_names" in node.options:
        generator._add_line(f"{prefix}_dataset_names = {node.options['dataset_names']}")
    else:
        generator._add_line(f"{prefix}_dataset_names = {dataset_names}")

    # 生成颜色数组
    colors = _get_colors(generator, len(normalized_data))

    # 根据图表类型生成不同的创建代码
    if node.chart_type == "bar":
        _generate_bar_chart_code(generator, node, prefix, normalized_data, colors)
    elif node.chart_type == "line":
        _generate_line_chart_code(generator, node, prefix, normalized_data, colors)
    elif node.chart_type == "radar":
        _generate_radar_chart_code(generator, node, prefix, normalized_data, colors)
    else:
        logger.error(f"不支持的图表类型: {node.chart_type}")
        generator._add_line(f"# 不支持的图表类型: {node.chart_type}")
        return

    # 生成图例
    _generate_legend(generator, prefix, f"{prefix}_dataset_names", colors)

    # 将所有元素分组到一个 VGroup 中
    generator._add_line("# 创建图表组合")
    if node.title:
        # 修改图例位置，使其在标题下方，图表上方
        generator._add_line("# 设置图例位置在标题下方")
        generator._add_line(f"{prefix}_legend.next_to({prefix}_title_obj, DOWN, buff=0.3)")
        generator._add_line(f"{prefix}_chart.next_to({prefix}_legend, DOWN, buff=0.3)")
        generator._add_line(f"{prefix}_group = VGroup({prefix}_chart, {prefix}_legend, {prefix}_title_obj)")
    else:
        generator._add_line("# 设置图例位置在图表上方")
        generator._add_line(f"{prefix}_legend.to_edge(UP, buff=0.3)")
        generator._add_line(f"{prefix}_chart.next_to({prefix}_legend, DOWN, buff=0.3)")
        generator._add_line(f"{prefix}_group = VGroup({prefix}_chart, {prefix}_legend)")

    # 确保图表大小适合屏幕
    generator._add_line("# 确保图表适合屏幕大小")
    generator._add_line("screen_width = config.frame_width * 0.75")
    generator._add_line("screen_height = config.frame_height * 0.75")
    generator._add_line(f"if {prefix}_group.width > screen_width:")
    generator._add_line(f"    scale_factor = screen_width / {prefix}_group.width")
    generator._add_line(f"    {prefix}_group.scale(scale_factor)")
    generator._add_line(f"if {prefix}_group.height > screen_height:")
    generator._add_line(f"    scale_factor = screen_height / {prefix}_group.height")
    generator._add_line(f"    {prefix}_group.scale(scale_factor)")

    # 将 VGroup 赋给 self 属性并更新区域状态
    self_group_var = f"self.{prefix}_chart_group"
    dsl_id = f"{prefix}_chart"
    generator._add_line("# 将图表 Group 赋值给 self 属性")
    generator._add_line(f"{self_group_var} = {prefix}_group")
    generator._update_region_content("full_screen", self_group_var, dsl_id)

    # --- 根据动画方式展示图表，并可能带有语音 ---
    generator._add_line("# 添加动画展示")
    narration_text = node.narration

    if narration_text:
        logger.info(f"为图表 {prefix} 添加语音旁白")
        generator._add_line(f"with self.voiceover({repr(narration_text)}) as tracker: # noqa: F841")
        generator._indent_block()
        if node.animation_style == "grow":
            if node.title:
                generator._add_line(f"self.play(FadeIn({prefix}_title_obj), run_time=1.0)")
            generator._add_line(f"self.play(FadeIn({prefix}_legend), run_time=0.8)")
            generator._add_line("# 渐进式构建图表 (带语音)")
            generator._add_line(f"self.play(Create({prefix}_chart), run_time=2.0)")
        elif node.animation_style == "fadeIn":
            generator._add_line("# 淡入整个图表 (带语音)")
            # 使用 self 属性引用整个 group
            generator._add_line(f"self.play(FadeIn({self_group_var}), run_time=1.5)")
        elif node.animation_style == "draw":
            if node.title:
                generator._add_line(f"self.play(Write({prefix}_title_obj), run_time=1.0)")
            generator._add_line(f"self.play(FadeIn({prefix}_legend), run_time=0.8)")
            generator._add_line("# 绘制图表 (带语音)")
            generator._add_line(f"self.play(Create({prefix}_chart, run_time=2.0))")
        elif node.animation_style == "update":
            if node.chart_type == "bar":
                if node.title:
                    generator._add_line(f"self.play(FadeIn({prefix}_title_obj), run_time=1.0)")
                generator._add_line(f"self.play(FadeIn({prefix}_legend), run_time=0.8)")
                generator._add_line("# 初始为空，然后逐个更新条形 (带语音)")
                generator._add_line(f"self.play(Create({prefix}_chart))")  # 假设 Create 内部处理了 update
            else:
                if node.title:
                    generator._add_line(f"self.play(FadeIn({prefix}_title_obj))")
                generator._add_line("# 动态更新仅支持条形图，使用 Create 代替 (带语音)")
                generator._add_line(f"self.play(Create({prefix}_chart), run_time=2.0)")
                generator._add_line(f"self.play(FadeIn({prefix}_legend), run_time=0.8)")
        generator._dedent_block()
    else:
        logger.info(f"图表 {prefix} 无语音旁白")
        if node.animation_style == "grow":
            if node.title:
                generator._add_line(f"self.play(FadeIn({prefix}_title_obj), run_time=1.0)")
            generator._add_line(f"self.play(FadeIn({prefix}_legend), run_time=0.8)")
            generator._add_line("# 渐进式构建图表 (无语音)")
            generator._add_line(f"self.play(Create({prefix}_chart), run_time=2.0)")
        elif node.animation_style == "fadeIn":
            generator._add_line("# 淡入整个图表 (无语音)")
            # 使用 self 属性引用整个 group
            generator._add_line(f"self.play(FadeIn({self_group_var}), run_time=1.5)")
        elif node.animation_style == "draw":
            if node.title:
                generator._add_line(f"self.play(Write({prefix}_title_obj), run_time=1.0)")
            generator._add_line(f"self.play(FadeIn({prefix}_legend), run_time=0.8)")
            generator._add_line("# 绘制图表 (无语音)")
            generator._add_line(f"self.play(Create({prefix}_chart, run_time=2.0))")
        elif node.animation_style == "update":
            if node.chart_type == "bar":
                if node.title:
                    generator._add_line(f"self.play(FadeIn({prefix}_title_obj), run_time=1.0)")
                generator._add_line(f"self.play(FadeIn({prefix}_legend), run_time=0.8)")
                generator._add_line("# 初始为空，然后逐个更新条形 (无语音)")
                generator._add_line(f"self.play(Create({prefix}_chart))")  # 假设 Create 内部处理了 update
            else:
                if node.title:
                    generator._add_line(f"self.play(FadeIn({prefix}_title_obj))")
                generator._add_line("# 动态更新仅支持条形图，使用 Create 代替 (无语音)")
                generator._add_line(f"self.play(Create({prefix}_chart), run_time=2.0)")
                generator._add_line(f"self.play(FadeIn({prefix}_legend), run_time=0.8)")

    logger.info(f"AnimateChartNode 访问完成，生成了图表代码: {prefix}_chart")


def _generate_bar_chart_code(
    generator: "CodeGenerator", node: "AnimateChartNode", prefix: str, normalized_data: list[dict], colors: str
) -> None:
    """生成条形图的代码，支持多组数据"""
    # 获取第一组数据的键作为所有图表的标签
    first_dataset_keys = list(normalized_data[0].keys())
    generator._add_line("# 准备条形图数据")
    generator._add_line(f"{prefix}_labels = {first_dataset_keys}")

    # 设置条形图参数
    generator._add_line("# 设置条形图参数")
    options = node.options or {}
    generator._add_line(f"{prefix}_colors = {colors}")

    # 设置数据集数量
    n_datasets = len(normalized_data)

    # 创建一个完整的条形图作为基础
    generator._add_line("# 创建基础条形图")

    # 合并所有数据找出最大值
    all_values = []
    for dataset in normalized_data:
        all_values.extend(list(dataset.values()))

    generator._add_line(f"{prefix}_all_values = {all_values}")
    generator._add_line(f"{prefix}_max_y = 1.2 * max({prefix}_all_values)")

    # 仅创建第一个条形图用于获取轴和布局
    dataset0_values = list(normalized_data[0].values())
    generator._add_line(f"{prefix}_data_0_values = {dataset0_values}")

    y_range = options.get("y_range", f"[0, {prefix}_max_y]")
    generator._add_line("# 创建主条形图用于轴和布局")
    generator._add_line(f"{prefix}_base_chart = BarChart(")
    generator._add_line(f"    values={prefix}_data_0_values,")
    generator._add_line("    bar_names=None,")  # 不使用内置的标签功能
    generator._add_line(f"    y_range={y_range},")
    generator._add_line(f"    bar_colors=[{prefix}_colors[0]],")

    # 使用较窄的条形宽度，为并排条形留出空间
    bar_width = options.get("bar_width", "0.3")
    generator._add_line(f"    bar_width={bar_width},")

    if "width" in options:
        generator._add_line(f"    x_length={options['width']},")
    else:
        generator._add_line("    x_length=10,")

    if "height" in options:
        generator._add_line(f"    y_length={options['height']},")
    else:
        generator._add_line("    y_length=5.5,")

    generator._add_line(")")

    # 创建额外的条形图但不显示它们的轴，只保留条形
    generator._add_line("# 创建所有数据集的条形")
    generator._add_line(f"{prefix}_all_bars = VGroup()")
    generator._add_line(f"{prefix}_all_bars.add(*{prefix}_base_chart.bars)")  # 先添加第一组条形

    # 处理第一组以外的数据集
    for i in range(1, n_datasets):
        dataset_values = list(normalized_data[i].values())
        dataset_prefix = f"{prefix}_data_{i}"

        # 添加数据值变量
        generator._add_line(f"{dataset_prefix}_values = {dataset_values}")

        # 创建条形图但不添加到场景中
        generator._add_line(f"# 创建第{i+1}组条形")
        generator._add_line(f"{dataset_prefix}_chart = BarChart(")
        generator._add_line(f"    values={dataset_prefix}_values,")
        generator._add_line("    bar_names=None,")
        generator._add_line(f"    y_range={y_range},")
        generator._add_line(f"    bar_colors=[{prefix}_colors[{i}]],")
        generator._add_line(f"    bar_width={bar_width},")

        if "width" in options:
            generator._add_line(f"    x_length={options['width']},")
        else:
            generator._add_line("    x_length=10,")

        if "height" in options:
            generator._add_line(f"    y_length={options['height']},")
        else:
            generator._add_line("    y_length=5.5,")

        generator._add_line(")")

        # 创建一个新的组只包含条形
        generator._add_line(f"{dataset_prefix}_bars = VGroup(*{dataset_prefix}_chart.bars)")
        generator._add_line(f"{prefix}_all_bars.add(*{dataset_prefix}_bars)")

    # 平移每组条形到适当位置
    generator._add_line("# 调整条形位置，错开各组数据")
    for i in range(n_datasets):
        dataset_prefix = f"{prefix}_data_{i}"
        # 计算水平偏移
        shift_value = i - (n_datasets - 1) / 2
        offset = str(shift_value * 0.7)  # 适当的偏移量

        if i == 0:
            # 第一组不需要特殊处理，因为已经在base_chart中
            continue
        else:
            # 其他组需要获取并调整每个条形
            generator._add_line(f"# 移动第{i+1}组条形")
            generator._add_line(f"for j, bar in enumerate({dataset_prefix}_chart.bars):")
            generator._add_line("    # 获取对应原始位置")
            generator._add_line(f"    base_bar = {prefix}_base_chart.bars[j]")
            generator._add_line("    # 计算底部位置，确保与x轴对齐")
            generator._add_line("    base_bottom = base_bar.get_bottom()")
            generator._add_line("    # 移动到底部对齐位置，保持y方向一致，只调整x方向")
            generator._add_line("    bar_height = bar.height")
            generator._add_line(f"    bar_center_x = base_bar.get_center()[0] + {offset}")
            generator._add_line("    bar_center_y = base_bottom[1] + bar_height/2")
            generator._add_line("    bar.move_to([bar_center_x, bar_center_y, 0])")

    # 创建组合图表
    generator._add_line("# 组合轴和条形到一个完整图表")
    generator._add_line(f"{prefix}_axes = {prefix}_base_chart.axes")  # 获取基础图表的轴
    generator._add_line(f"{prefix}_all_bars = VGroup(*{prefix}_base_chart.bars)")  # 重置，只包含基础图表的条形

    # 添加其他组的条形
    for i in range(1, n_datasets):
        dataset_prefix = f"{prefix}_data_{i}"
        generator._add_line(f"{prefix}_all_bars.add(*{dataset_prefix}_chart.bars)")

    # 手动添加X轴标签
    generator._add_line("# 手动添加X轴标签（使用Text而不是LaTeX）")
    generator._add_line(f"{prefix}_x_labels = VGroup()")
    generator._add_line(f"for i, label in enumerate({prefix}_labels):")

    # 使用基础条形图的条形位置来定位标签
    generator._add_line("    # 使用条形位置定位标签")
    generator._add_line(f"    bar_center = {prefix}_base_chart.bars[i].get_center()")
    generator._add_line(f"    x_axis_height = {prefix}_base_chart.x_axis.get_center()[1]")  # 获取x轴高度
    generator._add_line("    label_obj = Text(label, font_size=24, font='Maple Mono NF CN')")
    generator._add_line("    # 将标签放在柱子正下方的x轴处")
    generator._add_line("    label_obj.move_to([bar_center[0], x_axis_height, 0])")
    generator._add_line("    label_obj.shift(DOWN * 0.3)")  # 向下偏移一点
    generator._add_line(f"    {prefix}_x_labels.add(label_obj)")

    # 添加坐标轴单位标签
    generator._add_line("# 添加坐标轴单位标签")
    if "x_label" in options:
        generator._add_line("    # 添加X轴单位标签")
        generator._add_line(
            f"    {prefix}_x_axis_label = Text('{options['x_label']}', font_size=20, font='Maple Mono NF CN')"
        )
        generator._add_line(f"    {prefix}_x_axis_label.next_to({prefix}_base_chart.x_axis, DOWN, buff=0.8)")
        generator._add_line(f"    {prefix}_x_labels.add({prefix}_x_axis_label)")

    if "y_label" in options:
        generator._add_line("    # 添加Y轴单位标签")
        generator._add_line(
            f"    {prefix}_y_axis_label = Text('{options['y_label']}', font_size=20, font='Maple Mono NF CN')"
        )
        generator._add_line(f"    {prefix}_y_axis_label.next_to({prefix}_base_chart.y_axis, LEFT, buff=0.8)")
        generator._add_line(f"    {prefix}_y_axis_label.rotate(90 * DEGREES)")
        generator._add_line(f"    {prefix}_axes.add({prefix}_y_axis_label)")

    # 组合完整图表
    generator._add_line(f"{prefix}_chart = VGroup({prefix}_axes, {prefix}_all_bars, {prefix}_x_labels)")

    # 根据标题设置图表位置
    if node.title:
        generator._add_line("# 设置图表相对于标题的位置")
        generator._add_line(f"{prefix}_chart.next_to({prefix}_title_obj, DOWN, buff=0.5)")
    else:
        generator._add_line(f"{prefix}_chart.move_to(ORIGIN)")


def _generate_line_chart_code(
    generator: "CodeGenerator", node: "AnimateChartNode", prefix: str, normalized_data: list[dict], colors: str
) -> None:
    """生成线图代码，支持多组数据"""

    # 获取第一组数据的键作为所有图表的X轴标签
    first_dataset_keys = list(normalized_data[0].keys())

    # 创建变量简写函数
    generator._add_var = lambda name, value: generator._add_line(f"{name} = {value}")
    generator._add_var(f"{prefix}_labels", first_dataset_keys)
    generator._add_var(f"{prefix}_colors", colors)

    # 创建共用坐标轴，需要计算所有数据集的最大值
    generator._add_line("# 计算所有数据集的Y轴最大值")
    generator._add_line(f"{prefix}_all_values = []")
    for i, dataset in enumerate(normalized_data):
        dataset_values = list(dataset.values())
        generator._add_var(f"{prefix}_data_{i}_values", dataset_values)
        generator._add_line(f"{prefix}_all_values.extend({prefix}_data_{i}_values)")

    # 创建X值数组（对所有数据集相同）
    x_values = list(range(len(first_dataset_keys)))
    generator._add_var(f"{prefix}_x_vals", x_values)

    # 设置坐标范围
    options = node.options or {}
    x_range = options.get("x_range", [0, len(x_values) - 1, 1])
    generator._add_line("# 计算Y轴范围，考虑所有数据集")
    generator._add_line(f"{prefix}_max_y = max({prefix}_all_values) * 1.2")

    # 修改y轴刻度间隔，使其不那么密集
    generator._add_line(f"{prefix}_min_y = 0")
    generator._add_line(f"{prefix}_y_step = max(1, int({prefix}_max_y / 5))  # 限制最多显示5个主刻度")
    y_range = options.get("y_range", f"[{prefix}_min_y, {prefix}_max_y, {prefix}_y_step]")

    # 创建坐标轴
    generator._add_line("# 创建坐标轴")
    generator._add_line(f"{prefix}_axes = Axes(")
    generator._add_line(f"    x_range={x_range},")
    generator._add_line(f"    y_range={y_range},")
    generator._add_line("    axis_config={'color': WHITE},")
    generator._add_line(")")

    # 添加y轴刻度标签
    generator._add_line("# 添加Y轴刻度标签")
    generator._add_line(f"{prefix}_y_labels = []")
    generator._add_line(f"for y_val in range({prefix}_min_y, int({prefix}_max_y) + 1, {prefix}_y_step):")
    generator._add_line(f"    label_pos = {prefix}_axes.c2p(0, y_val)")
    generator._add_line("    y_label = Text(str(y_val), font_size=14, color=WHITE, font='Maple Mono NF CN')")
    generator._add_line("    y_label.next_to(label_pos, LEFT, buff=0.1)")
    generator._add_line(f"    {prefix}_y_labels.append(y_label)")
    generator._add_line(f"    {prefix}_axes.add(y_label)")

    # 为每组数据绘制线图
    generator._add_line("# 为每组数据绘制线图")
    generator._add_line(f"{prefix}_line_graphs = []")

    # 创建不同的点样式 - 移除不支持的'shape'属性
    generator._add_line("# 定义不同的点样式")
    generator._add_line(f"{prefix}_dot_styles = [")
    generator._add_line(f"    {{'fill_color': {prefix}_colors[0], 'stroke_width': 1}},")
    generator._add_line(f"    {{'fill_color': {prefix}_colors[1 % len({prefix}_colors)], 'stroke_width': 1}},")
    generator._add_line(f"    {{'fill_color': {prefix}_colors[2 % len({prefix}_colors)], 'stroke_width': 1}},")
    generator._add_line(f"    {{'fill_color': {prefix}_colors[3 % len({prefix}_colors)], 'stroke_width': 1}},")
    generator._add_line("]")

    for i, dataset in enumerate(normalized_data):
        dataset_prefix = f"{prefix}_data_{i}"

        # 绘制线图
        generator._add_line(f"# 绘制第{i+1}组数据的线图")
        generator._add_line(f"{dataset_prefix}_line_graph = {prefix}_axes.plot_line_graph(")
        generator._add_line(f"    x_values={prefix}_x_vals,")
        generator._add_line(f"    y_values={dataset_prefix}_values,")
        generator._add_line(f"    line_color={prefix}_colors[{i} % len({prefix}_colors)],")
        generator._add_line("    vertex_dot_radius=0.12,")
        generator._add_line(f"    vertex_dot_style={prefix}_dot_styles[{i} % len({prefix}_dot_styles)],")
        generator._add_line("    stroke_width=3,")
        generator._add_line(")")

        generator._add_line(f"{prefix}_line_graphs.append({dataset_prefix}_line_graph)")

    # 添加X轴标签
    generator._add_line("# 添加X轴标签")
    generator._add_line(f"{prefix}_label_objs = []")
    generator._add_line(f"for i, label in enumerate({prefix}_labels):")
    generator._add_line("    label_obj = Text(label, font_size=24, font='Maple Mono NF CN')")
    generator._add_line(f"    label_pos = {prefix}_axes.c2p(i, 0)")
    generator._add_line("    label_obj.next_to(label_pos, DOWN, buff=0.3)")
    generator._add_line(f"    {prefix}_label_objs.append(label_obj)")
    generator._add_line(f"    {prefix}_axes.add(label_obj)")

    # 添加坐标轴单位标签
    generator._add_line("# 添加坐标轴单位标签")
    if "x_label" in options:
        generator._add_line("# 添加X轴单位标签")
        generator._add_line(
            f"{prefix}_x_axis_label = Text('{options['x_label']}', font_size=20, font='Maple Mono NF CN')"
        )
        generator._add_line(f"{prefix}_x_axis_label.next_to({prefix}_axes, DOWN, buff=0.8)")
        generator._add_line(f"{prefix}_axes.add({prefix}_x_axis_label)")

    if "y_label" in options:
        generator._add_line("# 添加Y轴单位标签")
        generator._add_line(
            f"{prefix}_y_axis_label = Text('{options['y_label']}', font_size=20, font='Maple Mono NF CN')"
        )
        generator._add_line(f"{prefix}_y_axis_label.next_to({prefix}_axes, LEFT, buff=0.8)")
        generator._add_line(f"{prefix}_y_axis_label.rotate(90 * DEGREES)")
        generator._add_line(f"{prefix}_axes.add({prefix}_y_axis_label)")

    # 组合图表，包括坐标轴和所有线图
    generator._add_line("# 组合图表，包括坐标轴和所有线图")
    generator._add_line(f"{prefix}_chart = VGroup({prefix}_axes, *{prefix}_line_graphs)")

    # 设置位置
    if node.title:
        generator._add_line("# 设置图表相对于标题的位置")
        generator._add_line(f"{prefix}_chart.next_to({prefix}_title_obj, DOWN, buff=0.5)")
    else:
        generator._add_line(f"{prefix}_chart.move_to(ORIGIN)")


def _generate_radar_chart_code(
    generator: "CodeGenerator", node: "AnimateChartNode", prefix: str, normalized_data: list[dict], colors: str
) -> None:
    """生成雷达图代码，支持多组数据"""

    # 获取第一组数据的键作为所有图表的轴标签
    first_dataset_keys = list(normalized_data[0].keys())

    # 创建变量
    generator._add_var = lambda name, value: generator._add_line(f"{name} = {value}")
    generator._add_var(f"{prefix}_labels", first_dataset_keys)
    generator._add_var(f"{prefix}_colors", colors)

    # 计算所有数据集中的最大值，用于标准化
    generator._add_line("# 计算所有数据集的最大值")
    generator._add_line(f"{prefix}_all_values = []")
    for i, dataset in enumerate(normalized_data):
        dataset_values = list(dataset.values())
        generator._add_var(f"{prefix}_data_{i}_values", dataset_values)
        generator._add_line(f"{prefix}_all_values.extend({prefix}_data_{i}_values)")
    generator._add_line(f"{prefix}_max_value = max({prefix}_all_values)")

    # 设置雷达图参数 - 减小初始半径以便后续整体缩放
    generator._add_line("# 设置雷达图参数")
    generator._add_line(f"{prefix}_n = len({prefix}_labels)")
    generator._add_line(f"{prefix}_radius = 2.5")  # 减小初始半径
    generator._add_line(f"{prefix}_center = ORIGIN")

    # 创建雷达图背景和刻度
    generator._add_line("# 创建雷达图背景和刻度")
    generator._add_line(f"{prefix}_angles = [i * 2 * np.pi / {prefix}_n for i in range({prefix}_n)]")
    generator._add_line(
        f"{prefix}_points = [{prefix}_center + {prefix}_radius * np.array([np.cos(angle), np.sin(angle), 0]) for angle in {prefix}_angles]"
    )
    generator._add_line(f"{prefix}_bg_polygon = Polygon(*{prefix}_points, color=WHITE, stroke_width=1)")

    # 添加同心圆刻度
    generator._add_line("# 添加同心圆刻度")
    generator._add_line(f"{prefix}_circles = []")
    generator._add_line("for i in range(1, 5):  # 添加4个同心圆")
    generator._add_line("    ratio = i / 4")
    generator._add_line(
        f"    circle_points = [{prefix}_center + {prefix}_radius * ratio * np.array([np.cos(angle), np.sin(angle), 0]) for angle in np.linspace(0, 2 * np.pi, 50)]"
    )
    generator._add_line(
        "    circle = Polygon(*circle_points, color=WHITE, stroke_width=0.5, stroke_opacity=0.5, fill_opacity=0)"
    )
    generator._add_line(f"    {prefix}_circles.append(circle)")

    # 创建轴线
    generator._add_line("# 创建轴线")
    generator._add_line(f"{prefix}_axes = []")
    generator._add_line(f"for point in {prefix}_points:")
    generator._add_line(f"    {prefix}_axes.append(Line({prefix}_center, point, color=WHITE, stroke_width=1))")

    # 为每组数据创建多边形
    generator._add_line("# 为每组数据创建多边形")
    generator._add_line(f"{prefix}_data_polygons = []")
    generator._add_line(f"{prefix}_data_dots = []")

    # 添加轴标签 - 修改标签位置计算逻辑，增加距离并添加背景框以提高可读性
    generator._add_line("# 添加轴标签（使用Text对象而不是LaTeX，支持中文）")
    generator._add_line(f"{prefix}_label_objs = []")
    generator._add_line(f"{prefix}_label_backgrounds = []")
    generator._add_line(f"for i, label in enumerate({prefix}_labels):")
    generator._add_line("    # 计算标签位置（在轴端点外侧）")
    generator._add_line(f"    angle = {prefix}_angles[i]")
    generator._add_line("    # 使用角度正确放置标签，并增加距离避免重叠")
    generator._add_line("    direction = np.array([np.cos(angle), np.sin(angle), 0])")
    generator._add_line("    # 根据角度和标签长度动态调整距离")
    generator._add_line("    # 为底部标签特别增加距离")
    generator._add_line("    is_bottom_label = np.pi * 0.4 < angle < np.pi * 1.6")  # 识别底部区域的标签")
    generator._add_line("    distance_factor = 0.8 if is_bottom_label else (0.7 if len(label) > 3 else 0.6)")
    generator._add_line(f"    label_pos = {prefix}_center + ({prefix}_radius + distance_factor) * direction")
    generator._add_line("    # 使用Text创建标签，支持中文")
    generator._add_line("    label_obj = Text(label, font_size=20, font='Maple Mono NF CN')")
    generator._add_line("    label_obj.move_to(label_pos)")
    generator._add_line("    # 添加半透明背景框，增加可读性")
    generator._add_line("    padding = 0.1")
    generator._add_line(
        "    background = Rectangle(height=label_obj.height + padding*2, width=label_obj.width + padding*2, fill_color=BLACK, fill_opacity=0.6, stroke_opacity=0)"
    )
    generator._add_line("    background.move_to(label_obj.get_center())")
    generator._add_line(f"    {prefix}_label_backgrounds.append(background)")
    generator._add_line(f"    {prefix}_label_objs.append(label_obj)")

    generator._add_line(f"{prefix}_value_labels = []")
    generator._add_line(f"{prefix}_value_backgrounds = []")

    for i, dataset in enumerate(normalized_data):
        dataset_prefix = f"{prefix}_data_{i}"

        # 创建数据多边形
        generator._add_line(f"# 创建第{i+1}组数据的多边形")
        generator._add_line(
            f"{dataset_prefix}_scaled_values = [val / {prefix}_max_value for val in {dataset_prefix}_values]"
        )
        generator._add_line(
            f"{dataset_prefix}_data_points = [{prefix}_center + {prefix}_radius * {dataset_prefix}_scaled_values[i] * np.array([np.cos({prefix}_angles[i]), np.sin({prefix}_angles[i]), 0]) for i in range({prefix}_n)]"
        )
        generator._add_line(f"{dataset_prefix}_polygon = Polygon(")
        generator._add_line(f"    *{dataset_prefix}_data_points,")
        generator._add_line(f"    color={prefix}_colors[{i} % len({prefix}_colors)],")
        generator._add_line("    fill_opacity=0.3,")
        generator._add_line("    stroke_width=2,")
        generator._add_line(")")
        generator._add_line(f"{prefix}_data_polygons.append({dataset_prefix}_polygon)")

        # 添加数据点
        generator._add_line(f"# 添加第{i+1}组数据的点")
        generator._add_line(f"{dataset_prefix}_dots = []")
        generator._add_line(f"for point in {dataset_prefix}_data_points:")
        generator._add_line(f"    dot = Dot(point, color={prefix}_colors[{i} % len({prefix}_colors)], radius=0.1)")
        generator._add_line(f"    {dataset_prefix}_dots.append(dot)")
        generator._add_line(f"{prefix}_data_dots.extend({dataset_prefix}_dots)")

    # 修改值标签放置逻辑，优化布局避免重叠
    generator._add_line("# 添加数值标签")
    generator._add_line("# 为每个数据集添加值标签")
    n_datasets = len(normalized_data)  # 获取数据集数量
    generator._add_line(f"# 处理{n_datasets}个数据集的值标签")
    for i in range(n_datasets):
        dataset_prefix = f"{prefix}_data_{i}"
        generator._add_line(f"# 添加数据集{i+1}的值标签")
        generator._add_line(f"for j, val in enumerate({dataset_prefix}_values):")
        generator._add_line("    # 使用Text创建值标签，支持中文")
        generator._add_line(
            f"    value_label = Text(str(val), font_size=16, color={prefix}_colors[{i} % len({prefix}_colors)], font='Maple Mono NF CN')"
        )
        generator._add_line("    # 计算标签位置（在数据点附近）")
        generator._add_line(f"    angle = {prefix}_angles[j]")
        generator._add_line(f"    scaled_value = {dataset_prefix}_scaled_values[j]")
        generator._add_line("    direction = np.array([np.cos(angle), np.sin(angle), 0])")
        generator._add_line(f"    data_point = {prefix}_center + {prefix}_radius * scaled_value * direction")
        generator._add_line("    # 根据数据点位置智能调整标签偏移")
        generator._add_line("    # 为避免与轴标签重叠，调整偏移方向")
        generator._add_line("    if scaled_value > 0.8:")  # 接近外圈
        generator._add_line("        # 向内偏移避免与轴标签重叠")
        generator._add_line(
            "        offset_direction = -0.3 * direction + 0.2 * np.array([-direction[1], direction[0], 0])"
        )  # 向内加侧向偏移
        generator._add_line("    elif scaled_value > 0.5:")  # 中等距离
        generator._add_line("        # 侧向偏移")
        generator._add_line(
            "        offset_direction = 0.1 * direction + 0.25 * np.array([-direction[1], direction[0], 0])"
        )
        generator._add_line("    else:")  # 靠近中心
        generator._add_line("        # 沿径向偏移")
        generator._add_line("        offset_direction = 0.3 * direction")
        generator._add_line("    value_label.move_to(data_point + offset_direction)")
        generator._add_line("    # 添加半透明背景框，增加可读性")
        generator._add_line("    padding = 0.05")
        generator._add_line(
            "    value_bg = Rectangle(height=value_label.height + padding*2, width=value_label.width + padding*2, fill_color=BLACK, fill_opacity=0.6, stroke_opacity=0)"
        )
        generator._add_line("    value_bg.move_to(value_label.get_center())")
        generator._add_line(f"    {prefix}_value_backgrounds.append(value_bg)")
        generator._add_line(f"    {prefix}_value_labels.append(value_label)")

    # 组合所有元素成一个 VGroup - 注意元素的添加顺序对视觉效果很重要
    generator._add_line("# 组合所有元素，按照视觉层次顺序添加")
    generator._add_line(f"{prefix}_radar_elements = VGroup(")
    generator._add_line(f"    {prefix}_bg_polygon,")  # 背景多边形最底层
    generator._add_line(f"    *{prefix}_circles,")  # 同心圆刻度
    generator._add_line(f"    *{prefix}_axes,")  # 轴线
    generator._add_line(f"    *{prefix}_data_polygons,")  # 数据多边形
    generator._add_line(f"    *{prefix}_data_dots,")  # 数据点
    generator._add_line(")")

    # 添加标签和背景，确保背景在标签下方
    generator._add_line("# 添加标签背景和标签")
    generator._add_line(f"if {prefix}_label_backgrounds:")
    generator._add_line(f"    {prefix}_radar_elements.add(*{prefix}_label_backgrounds)")
    generator._add_line(f"if {prefix}_label_objs:")
    generator._add_line(f"    {prefix}_radar_elements.add(*{prefix}_label_objs)")

    # 添加值标签背景和值标签
    generator._add_line("# 添加值标签背景和值标签")
    generator._add_line(f"if {prefix}_value_backgrounds:")
    generator._add_line(f"    {prefix}_radar_elements.add(*{prefix}_value_backgrounds)")
    generator._add_line(f"if {prefix}_value_labels:")
    generator._add_line(f"    {prefix}_radar_elements.add(*{prefix}_value_labels)")

    # 设置整个图表
    generator._add_line(f"{prefix}_chart = {prefix}_radar_elements")

    # 自动缩放调整以确保所有内容都在视图边界内
    generator._add_line("# 检查图表大小并自动缩放以确保所有内容都在视图边界内")
    generator._add_line(f"{prefix}_height = {prefix}_chart.height")
    generator._add_line(f"{prefix}_width = {prefix}_chart.width")

    # 计算安全边界（帧大小的80%）
    generator._add_line("# 计算安全边界区域")
    generator._add_line("safe_height = config.frame_height * 0.80")
    generator._add_line("safe_width = config.frame_width * 0.80")

    # 如果图表尺寸超出安全边界，按比例缩小
    generator._add_line("# 如果图表尺寸超出安全边界，按比例缩小")
    generator._add_line("scale_factor = 1.0")
    generator._add_line(f"if {prefix}_height > safe_height:")
    generator._add_line(f"    height_scale = safe_height / {prefix}_height")
    generator._add_line("    scale_factor = min(scale_factor, height_scale)")
    generator._add_line(f"if {prefix}_width > safe_width:")
    generator._add_line(f"    width_scale = safe_width / {prefix}_width")
    generator._add_line("    scale_factor = min(scale_factor, width_scale)")

    # 应用缩放
    generator._add_line("# 应用缩放")
    generator._add_line("if scale_factor < 1.0:")
    generator._add_line(f"    {prefix}_chart.scale(scale_factor)")

    # 设置图表位置
    if node.title:
        generator._add_line("# 设置图表相对于标题的位置")
        generator._add_line(f"{prefix}_chart.next_to({prefix}_title_obj, DOWN, buff=0.5)")
    else:
        generator._add_line(f"{prefix}_chart.move_to(ORIGIN)")
