from typing import TYPE_CHECKING, Any

from loguru import logger

if TYPE_CHECKING:
    from ..ast_nodes import TimelineNode
    from ..code_generator import CodeGenerator


def visit_timeline(generator: "CodeGenerator", node: "TimelineNode", **kwargs: Any) -> None:
    """
    生成 TimelineNode 对应的 Manim 代码。
    包含主线、事件节点、文本/图片、横向排列、自动缩放/平移、自动播放与强调动画。
    """
    # This action is considered fullscreen and covers previous content.
    # target_region_id is ignored.
    logger.info("正在访问 TimelineNode，生成时间轴动画代码")

    # 1. Clear all existing regions before displaying the timeline
    generator._clear_all_regions()

    prefix = f"timeline_{abs(hash(str(node.events))) % 10000}"
    n_events = len(node.events)
    spacing = 5.0  # 事件节点间距，进一步增大
    node_radius = 0.25  # 节点圆半径
    timeline_y = 0  # 主线纵坐标
    font = node.label_font or "Maple Mono NF CN"
    timeline_color = node.timeline_color or "#6666FF"
    event_style = node.event_style or {}

    generator._add_line("# 创建时间轴主线")
    generator._add_line(
        f"{prefix}_line = Line(LEFT*{spacing*(n_events-1)/2}, RIGHT*{spacing*(n_events-1)/2}, color='{timeline_color}', stroke_width=6)"
    )
    generator._add_line(f"self.add({prefix}_line)")

    generator._add_line("# 创建事件节点、文本、图片、注释")
    generator._add_line(f"{prefix}_nodes = []  # 节点圆/方形")
    generator._add_line(f"{prefix}_labels = []  # 时间点文本")
    generator._add_line(f"{prefix}_contents = []  # 事件内容（文本/图片）")
    generator._add_line(f"{prefix}_annotations = []  # 注释")

    for i, event in enumerate(node.events):
        x = (i - (n_events - 1) / 2) * spacing
        shape = event_style.get("shape", "circle")
        color = event_style.get("color", timeline_color)
        node_var = f"{prefix}_node_{i}"
        if shape == "square":
            generator._add_line(f"{node_var} = Square({node_radius*2}, color='{color}', fill_opacity=1.0)")
        else:
            generator._add_line(f"{node_var} = Circle(radius={node_radius}, color='{color}', fill_opacity=1.0)")
        generator._add_line(f"{node_var}.move_to([{x}, {timeline_y}, 0])")
        generator._add_line(f"{prefix}_nodes.append({node_var})")
        label_var = f"{prefix}_label_{i}"
        generator._add_line(f"{label_var} = Text(str({repr(event.time)}), font_size=24, font='{font}')")
        generator._add_line(f"{label_var}.next_to({node_var}, DOWN, buff=0.15)")
        generator._add_line(f"{prefix}_labels.append({label_var})")
        content_var = f"{prefix}_content_{i}"
        content_items = []
        if event.image:
            img_var = f"{content_var}_img"
            generator._add_line(f"{img_var} = ImageMobject('{event.image}')")
            generator._add_line(f"if {img_var}.height > 2.0:")
            generator._indent_block()
            generator._add_line(f"{img_var}.set(height=2.0)")
            generator._dedent_block()
            generator._add_line(f"{img_var}.next_to({node_var}, UP, buff=0.2)")
            content_items.append(img_var)
        if event.text:
            txt_var = f"{content_var}_txt"
            generator._add_line(f"{txt_var} = Text({repr(event.text)}, font_size=32, font='{font}')")
            if event.image:
                generator._add_line(f"{txt_var}.next_to({img_var}, DOWN, buff=0.15)")
            else:
                generator._add_line(f"{txt_var}.next_to({node_var}, UP, buff=0.2)")
            content_items.append(txt_var)
        if content_items:
            generator._add_line(f"{content_var} = Group({', '.join(content_items)})")
            generator._add_line(f"{content_var}.shift(UP * 0.5)")
        else:
            generator._add_line(f"{content_var} = None  # 无内容")
        generator._add_line(f"{prefix}_contents.append({content_var})")
        if event.annotation:
            ann_var = f"{prefix}_annotation_{i}"
            generator._add_line(
                f"{ann_var} = Text({repr(event.annotation)}, font_size=24, font='{font}', color=YELLOW)"
            )
            generator._add_line(f"{ann_var}.next_to({node_var}, DOWN, buff=0.7)")
            generator._add_line(f"{prefix}_annotations.append({ann_var})")
        else:
            generator._add_line(f"{prefix}_annotations.append(None)")

    generator._add_line("# 将所有节点、文本、内容、注释组合成 Group")
    generator._add_line(
        f"{prefix}_group = Group(*{prefix}_line, *{prefix}_nodes, *{prefix}_labels, *[c for c in {prefix}_contents if c], *[a for a in {prefix}_annotations if a])"
    )
    generator._add_line(f"{prefix}_group.move_to(ORIGIN)")

    # 将 Group 赋给 self 属性并更新区域状态
    self_group_var = f"self.{prefix}_timeline_group"
    dsl_id = f"{prefix}_timeline"
    generator._add_line("# 将时间轴 Group 赋值给 self 属性")
    generator._add_line(f"{self_group_var} = {prefix}_group")
    generator._add_line("# 更新 'main' 区域内容状态")
    generator._update_region_content("main", self_group_var, dsl_id)

    # --- 创建时间轴并播放动画，并可能带有语音 ---
    narration_text = node.narration

    if narration_text:
        logger.info(f"为时间轴 {prefix} 添加语音旁白")
        generator._add_line(f"with self.voiceover({repr(narration_text)}) as tracker: # noqa: F841")
        generator._indent_block()

        # -- 自动播放事件聚焦动画 (在 voiceover 内部) --
        generator._add_line("# 自动播放每个事件的出现与强调动画")
        generator._add_line(f"for i in range({n_events}):")
        generator._indent_block()
        generator._add_line("# 隐藏所有内容和注释")
        generator._add_line(f"for j in range({n_events}):")
        generator._indent_block()
        generator._add_line(f"if {prefix}_contents[j]:")
        generator._indent_block()
        generator._add_line(f"{prefix}_contents[j].set_opacity(0)")
        generator._dedent_block()
        generator._add_line(f"if {prefix}_annotations[j]:")
        generator._indent_block()
        generator._add_line(f"{prefix}_annotations[j].set_opacity(0)")
        generator._dedent_block()
        generator._dedent_block()
        generator._add_line("# 平移 group，使当前节点居中")
        generator._add_line(f"focus_x = {prefix}_nodes[i].get_center()[0]")
        generator._add_line("shift_x = -focus_x")
        generator._add_line(f"self.play({self_group_var}.animate.shift(RIGHT * shift_x), run_time=0.7)")
        generator._add_line("# 依次淡入节点、文本")
        generator._add_line(f"self.play(FadeIn({prefix}_nodes[i]), FadeIn({prefix}_labels[i]), run_time=0.4)")
        generator._add_line("# 显示当前内容和注释")
        generator._add_line(f"if {prefix}_contents[i]:")
        generator._indent_block()
        generator._add_line(f"{prefix}_contents[i].set_opacity(1)")
        generator._add_line(f"self.play(FadeIn({prefix}_contents[i]), run_time=0.5)")
        generator._dedent_block()
        generator._add_line(f"if {prefix}_annotations[i]:")
        generator._indent_block()
        generator._add_line(f"{prefix}_annotations[i].set_opacity(1)")
        generator._add_line(f"self.play(FadeIn({prefix}_annotations[i]), run_time=0.3)")
        generator._dedent_block()
        generator._add_line("# focus 强调动画")
        if node.focus_effect == "zoom":
            generator._add_line(f"self.play({prefix}_nodes[i].animate.scale(1.3), run_time=0.3)")
            generator._add_line(f"self.play({prefix}_nodes[i].animate.scale(1/1.3), run_time=0.3)")
        elif node.focus_effect == "flash":
            generator._add_line(f"self.play(Indicate({prefix}_nodes[i], color=YELLOW, scale_factor=1.2, run_time=0.5))")
        elif node.focus_effect == "color":
            generator._add_line(f"self.play({prefix}_nodes[i].animate.set_color(RED), run_time=0.3)")
            generator._add_line(f"self.play({prefix}_nodes[i].animate.set_color('{color}'), run_time=0.3)")
        # generator._add_line(f"self.wait({node.duration})") # wait is handled by voiceover context
        generator._dedent_block()  # End of for loop
        generator._dedent_block()  # End of with voiceover block
    else:
        logger.info(f"时间轴 {prefix} 无语音旁白")
        # -- 自动播放事件聚焦动画 (无语音) --
        generator._add_line("# 自动播放每个事件的出现与强调动画")
        generator._add_line(f"for i in range({n_events}):")
        generator._indent_block()
        generator._add_line("# 隐藏所有内容和注释")
        generator._add_line(f"for j in range({n_events}):")
        generator._indent_block()
        generator._add_line(f"if {prefix}_contents[j]:")
        generator._indent_block()
        generator._add_line(f"{prefix}_contents[j].set_opacity(0)")
        generator._dedent_block()
        generator._add_line(f"if {prefix}_annotations[j]:")
        generator._indent_block()
        generator._add_line(f"{prefix}_annotations[j].set_opacity(0)")
        generator._dedent_block()
        generator._dedent_block()
        generator._add_line("# 平移 group，使当前节点居中")
        generator._add_line(f"focus_x = {prefix}_nodes[i].get_center()[0]")
        generator._add_line("shift_x = -focus_x")
        generator._add_line(f"self.play({self_group_var}.animate.shift(RIGHT * shift_x), run_time=0.7)")
        generator._add_line("# 依次淡入节点、文本")
        generator._add_line(f"self.play(FadeIn({prefix}_nodes[i]), FadeIn({prefix}_labels[i]), run_time=0.4)")
        generator._add_line("# 显示当前内容和注释")
        generator._add_line(f"if {prefix}_contents[i]:")
        generator._indent_block()
        generator._add_line(f"{prefix}_contents[i].set_opacity(1)")
        generator._add_line(f"self.play(FadeIn({prefix}_contents[i]), run_time=0.5)")
        generator._dedent_block()
        generator._add_line(f"if {prefix}_annotations[i]:")
        generator._indent_block()
        generator._add_line(f"{prefix}_annotations[i].set_opacity(1)")
        generator._add_line(f"self.play(FadeIn({prefix}_annotations[i]), run_time=0.3)")
        generator._dedent_block()
        generator._add_line("# focus 强调动画")
        if node.focus_effect == "zoom":
            generator._add_line(f"self.play({prefix}_nodes[i].animate.scale(1.3), run_time=0.3)")
            generator._add_line(f"self.play({prefix}_nodes[i].animate.scale(1/1.3), run_time=0.3)")
        elif node.focus_effect == "flash":
            generator._add_line(f"self.play(Indicate({prefix}_nodes[i], color=YELLOW, scale_factor=1.2, run_time=0.5))")
        elif node.focus_effect == "color":
            generator._add_line(f"self.play({prefix}_nodes[i].animate.set_color(RED), run_time=0.3)")
            generator._add_line(f"self.play({prefix}_nodes[i].animate.set_color('{color}'), run_time=0.3)")
        generator._add_line(f"self.wait({node.duration})")
        generator._dedent_block()  # End of for loop

    logger.info("TimelineNode 访问完成")
