from typing import TYPE_CHECKING

from loguru import logger

# Type hint for <PERSON>G<PERSON><PERSON> to avoid circular import
if TYPE_CHECKING:
    from ..ast_nodes import SceneNode
    from ..code_generator import CodeGenerator


def visit_scene(generator: "CodeGenerator", node: "SceneNode", **kwargs):
    """Generates the overall structure of the scene."""
    logger.debug(f"Visiting SceneNode: {node.metadata.title}")
    # 1. Add header comments and imports
    generator._add_line("# Generated by DSL v2 Code Generator")
    if node.metadata.author:
        generator._add_line(f"# Author: {node.metadata.author}")
    generator._add_line("import sys")
    generator._add_line("import os")
    generator._add_line("root_dir = os.getcwd()")  # 使用当前工作目录作为root_dir
    generator._add_line("sys.path.append(root_dir)")  # 添加当前目录到Python路径
    generator._add_line("from manim import *")
    generator._add_line("from utils.edgetts_service import EdgeTTSService")
    generator._add_line("from utils.feynman_scene import FeynmanScene")
    generator._add_line()

    # 2. Define the scene class
    scene_class_name = node.metadata.title.replace(" ", "_").replace("-", "_")
    if not scene_class_name.isidentifier():
        scene_class_name = f"Scene_{scene_class_name}"
        logger.warning(
            f"Original title '{node.metadata.title}' generated an invalid class name, changed to: {scene_class_name}"
        )

    generator._add_line(f"class {scene_class_name}(FeynmanScene):")
    generator._indent_block()

    # Optional: Add background color config
    if node.metadata.background_color and node.metadata.background_color.upper() != "BLACK":
        color_str = f"{node.metadata.background_color.upper()}"
        generator._add_line(f'config.background_color = "{color_str}"')
        generator._add_line()

    # 4. Define the construct method
    generator._add_line("def construct(self):")
    generator._indent_block()
    generator._add_line(
        "self.set_speech_service(EdgeTTSService(global_speed=1.2, voice='zh-CN-YunxiNeural'), create_subcaption=True)"
    )
    generator._add_line()

    # 5. Iterate and generate action code
    logger.debug(f"Generating code for {len(node.actions)} actions...")
    if node.actions:
        for i, action in enumerate(node.actions):
            generator._add_line(f"# Action {i+1}: {type(action).__name__}")  # Use type name
            generator.visit(action)  # Visit each action node
            generator._add_line()  # Add blank line between actions
    else:
        generator._add_line("pass # No actions defined")

    # --- Add final wait ---
    generator._add_line("# --- Final wait to hold the last frame ---")
    generator._add_line("self.wait(1)")
    # -----------------------

    # 6. End construct and class definition
    generator._dedent_block()  # Exit construct
    generator._dedent_block()  # Exit class
    logger.debug("SceneNode visit completed.")
