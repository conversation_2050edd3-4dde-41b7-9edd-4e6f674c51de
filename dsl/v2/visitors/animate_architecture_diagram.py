import random
from typing import TYPE_CHECKING, Any

from loguru import logger

if TYPE_CHECKING:
    from ..ast_nodes import AnimateArchitectureDiagramNode
    from ..code_generator import CodeGenerator


def visit_animate_architecture_diagram(
    generator: "CodeGenerator", node: "AnimateArchitectureDiagramNode", **kwargs: Any
) -> None:
    """
    生成 AnimateArchitectureDiagramNode 对应的 Manim 代码。

    通过调用 ExcalidrawToolkit 生成架构图视频，然后将视频嵌入到 Manim 场景中。

    参数:
        generator: 代码生成器实例
        node: AnimateArchitectureDiagramNode 实例，包含架构图的内容描述
        **kwargs: 额外的参数，由 CodeGenerator 传入
    """
    # This action is considered fullscreen and covers previous content.
    # target_region_id is ignored.
    logger.info("正在访问 AnimateArchitectureDiagramNode")

    # 1. Clear all existing regions before displaying the diagram video
    generator._clear_all_regions()

    # 创建一个唯一的变量名前缀和分镜 ID
    prefix = f"arch_diagram_{abs(hash(node.content_description[:30])) % 10000}"
    step_id = random.randint(1, 9999)

    # 1. 添加必要的导入
    if not generator._has_import("ffmpeg"):
        generator._add_import("ffmpeg")
    if not generator._has_import("ExcalidrawToolkit"):
        generator._add_import("tools.excalidraw_toolkit", "ExcalidrawToolkit")
    if not generator._has_import("VideoMobject"):
        generator._add_import("manim_funcs.video_mobject", "VideoMobject")

    # 2. 调用 ExcalidrawToolkit 生成视频
    # 内容描述需要转义，确保 JSON 格式正确
    generator._add_line("# 调用 ExcalidrawToolkit 生成架构图视频")
    generator._add_line(f"content_description = {repr(node.content_description)}")
    generator._add_line(f"{prefix}_toolkit = ExcalidrawToolkit()")
    generator._add_line(
        f"{prefix}_video_path = {prefix}_toolkit.generate_excalidraw_video(content_description, step={step_id})"
    )

    # 3. 创建 VideoMobject 并添加到场景中
    generator._add_line("# 将生成的视频嵌入到场景中")
    generator._add_line(f"{prefix}_video = VideoMobject(filename={prefix}_video_path, speed=1.0, loop=False)")
    generator._add_line(f"{prefix}_video.move_to(ORIGIN)")

    # 4. 调整视频大小，确保适合屏幕
    generator._add_line("# 调整视频大小以适合屏幕")
    generator._add_line("screen_width = config.frame_width * 0.8")  # 留出一些边距
    generator._add_line(f"if {prefix}_video.width > screen_width:")
    generator._indent_block()
    generator._add_line(f"scale_factor = screen_width / {prefix}_video.width")
    generator._add_line(f"{prefix}_video.scale(scale_factor)")
    generator._dedent_block()

    # 4.5 将 VideoMobject 赋给 self 属性并更新区域状态
    self_video_var = f"self.{prefix}_video"
    dsl_id = f"{prefix}_arch_diagram"
    generator._add_line("# 将视频对象赋值给 self 属性")
    generator._add_line(f"{self_video_var} = {prefix}_video")
    generator._add_line("# 更新 'main' 区域内容状态")
    generator._update_region_content("main", self_video_var, dsl_id)

    # --- 添加到场景并播放，并可能带有语音 ---
    narration_text = node.narration
    if narration_text:
        logger.info(f"为架构图 {prefix} 添加语音旁白")
        generator._add_line(f"with self.voiceover({repr(narration_text)}) as tracker: # noqa: F841")
        generator._indent_block()
        generator._add_line("# 添加视频到场景并播放 (带语音)")
        # 使用 self 属性进行动画
        generator._add_line(f"self.play(FadeIn({self_video_var}))")
        # 获取视频时长
        generator._add_line("# 使用ffmpeg获取视频时长信息")
        generator._add_line(f"probe = ffmpeg.probe({prefix}_video_path)")
        generator._add_line('video_info = next(s for s in probe["streams"] if s["codec_type"] == "video")')
        generator._add_line('duration = float(video_info["duration"])')
        generator._add_line("self.wait(max(5, duration)) # 由 voiceover 处理等待")
        generator._dedent_block()
    else:
        logger.info(f"架构图 {prefix} 无语音旁白")
        generator._add_line("# 添加视频到场景并播放 (无语音)")
        # 使用 self 属性进行动画
        generator._add_line(f"self.play(FadeIn({self_video_var}))")
        generator._add_line("# 使用ffmpeg获取视频时长，确保至少播放5秒")
        generator._add_line(f"probe = ffmpeg.probe({prefix}_video_path)")
        generator._add_line('video_info = next(s for s in probe["streams"] if s["codec_type"] == "video")')
        generator._add_line('duration = float(video_info["duration"])')
        generator._add_line("self.wait(max(5, duration))  # 播放视频，时长取视频实际时长和5秒的较大值")

    # 6. 淡出视频 (移除手动 FadeOut，由区域清理机制处理)
    # generator._add_line("# 淡出视频")
    # generator._add_line(f"self.play(FadeOut({self_video_var}))") # Replaced self_video_var here, but removing the line anyway

    logger.info("AnimateArchitectureDiagramNode 访问完成，生成了架构图视频代码")
