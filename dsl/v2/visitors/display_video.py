# dsl/v2/visitors/display_video.py

import shlex  # For safe filename representation
from typing import TYPE_CHECKING, Any

from loguru import logger

if TYPE_CHECKING:
    from ..ast_nodes import DisplayVideoNode
    from ..code_generator import CodeGenerator


def visit_display_video(generator: "CodeGenerator", node: "DisplayVideoNode", **kwargs: Any) -> None:
    """
    生成 DisplayVideoNode 对应的 Manim 代码。

    在指定区域显示视频，并可选择性地叠加文本。

    参数:
        generator: 代码生成器实例
        node: DisplayVideoNode 实例
        **kwargs: 额外的参数
    """
    target_region_id = getattr(node, "target_region_id", None) or "main"
    overlay_text = getattr(node, "overlay_text", None)
    video_path = node.video_path
    speed = getattr(node, "speed", 1.0)
    overlay_animation_style = getattr(node, "overlay_animation_style", "simultaneous")
    overlay_animation_delay = getattr(node, "overlay_animation_delay", 0.5)
    logger.info(
        f"正在访问 DisplayVideoNode: video='{video_path}', target_region='{target_region_id}', overlay='{bool(overlay_text)}', overlay_anim='{overlay_animation_style}'"
    )

    # 1. 清理目标区域的先前内容
    generator._clear_region(target_region_id)

    # 2. 创建唯一变量名前缀和 ID
    prefix = f"video_display_{abs(hash(video_path + (overlay_text or '') + overlay_animation_style ) ) % 10000}"  # Add style to hash
    if node.id:
        reference_id = node.id
    else:
        reference_id = f"video_{prefix}"  # Auto-generate ID if not provided
    reference_var = f"self.{reference_id}_content"  # Variable to store the final Mobject in the scene class

    # 3. 添加必要的导入
    if not generator._has_import("ffmpeg"):
        generator._add_import("ffmpeg")
    if not generator._has_import("VideoMobject"):
        generator._add_import("manim_funcs.video_mobject", "VideoMobject")

    # 4. 生成创建视频对象的代码
    generator._add_line(f"# 创建视频对象: {video_path}")
    # Use shlex.quote for safer filename handling in generated code
    safe_video_path = shlex.quote(video_path)
    generator._add_line(f"{prefix}_video = VideoMobject(filename='{safe_video_path}', speed={speed}, loop=False)")
    generator._add_line("start_time = self.renderer.time")

    # 5. 如果有叠加文本，创建文本对象并组合
    final_display_object_var = f"{prefix}_video"  # Default to video object
    text_animation_play_line = None  # Store the text animation command if sequential

    if overlay_text:
        text_font_size = 32
        generator._add_line(f"# ---- 处理叠加文本 (模式: {overlay_animation_style}) ----")

        if overlay_animation_style == "sequential":
            generator._add_line("# 创建逐行文本对象")
            lines = overlay_text.strip().split("\n")
            line_vars = []
            # Create text lines and rectangles
            text_rect_pairs = []
            generator._add_line("max_width = 0")
            for i, line_content in enumerate(lines):
                line_var = f"{prefix}_text_line_{i}"
                rect_var = f"{prefix}_rect_{i}"
                generator._add_line(
                    f'{line_var} = Text({repr(line_content)}, font="Maple Mono NF CN", font_size={text_font_size}, weight=BOLD, color="YELLOW")'
                )
                generator._add_line(
                    f"{rect_var} = SurroundingRectangle({line_var}, buff=0.2, fill_opacity=0.5, fill_color=BLACK, stroke_width=0)"
                )
                generator._add_line(f"max_width = max(max_width, {line_var}.width)")
                generator._add_line(f"{prefix}_vgroup_{i} = VGroup({rect_var}, {line_var})")  # Rectangle behind text
                line_vars.append(f"{prefix}_vgroup_{i}")
                text_rect_pairs.append((line_var, rect_var))

            # Stretch all rectangles to max width
            for _, rect_var in text_rect_pairs:
                generator._add_line(f"{rect_var}.stretch_to_fit_width(max_width + 0.2)")

            generator._add_line("# 将文本行组合成 Group")
            generator._add_line(f"{prefix}_text_lines_group = Group(*[{', '.join(line_vars)}])")
            generator._add_line(f"{prefix}_text_lines_group.arrange(DOWN, buff=0.5, aligned_edge=LEFT)")

            # Position text group relative to video (Top Left Corner + padding)
            # This positioning happens *before* scaling the final group
            generator._add_line("# 定位文本组到视频中心")
            generator._add_line(f"{prefix}_text_lines_group.move_to({prefix}_video)")

            # Create the final group containing video and text lines
            generator._add_line("# 创建包含视频和文本行的最终组")
            generator._add_line(f"{prefix}_final_group = Group({prefix}_video, {prefix}_text_lines_group)")
            final_display_object_var = f"{prefix}_final_group"

            # Prepare the sequential animation command
            generator._add_line("# 准备文本行的 LaggedStart 动画")
            animation_args = f"*[FadeIn(line) for line in {prefix}_text_lines_group]"
            # Calculate run_time based on delay and number of lines
            run_time_calc = f"max(1.0, {len(lines)} * {overlay_animation_delay})"
            text_animation_play_line = (
                f"self.play(LaggedStart({animation_args}, lag_ratio=1.0, run_time={run_time_calc}))"
            )

        else:  # Simultaneous or default
            text_position = getattr(node, "text_position", "BOTTOM").upper()
            generator._add_line("# 创建单个文本对象")
            generator._add_line(
                f'{prefix}_text = Text({repr(overlay_text)}, font="Maple Mono NF CN", font_size={text_font_size}, color="YELLOW")'
            )

            generator._add_line("# 将视频和文本组合成 Group")
            if text_position == "TOP":
                generator._add_line(f"{prefix}_group = Group({prefix}_text, {prefix}_video)")
                generator._add_line(f"{prefix}_group.arrange(DOWN, buff=0.2)")
            elif text_position == "CENTER":
                generator._add_line("# 将文本放置在视频中心")
                generator._add_line(f"{prefix}_text.move_to({prefix}_video.get_center())")
                generator._add_line(
                    f"{prefix}_group = Group({prefix}_video, {prefix}_text)"
                )  # Use Group for direct overlay
            else:  # Default to BOTTOM
                generator._add_line(f"{prefix}_group = Group({prefix}_video, {prefix}_text)")
                generator._add_line(f"{prefix}_group.arrange(DOWN, buff=0.2)")

            final_display_object_var = f"{prefix}_group"  # The group is the final object

    # 6. 定位到目标区域中心
    target_rect = generator._get_region_rect(target_region_id)
    if target_rect:
        generator._add_line(f"# 定位到区域 '{target_region_id}' 中心")
        generator._add_line(f"{final_display_object_var}.move_to({list(target_rect.get_center())})")
    else:
        generator._add_line(f"# 区域 '{target_region_id}' 未找到, 定位到 ORIGIN")
        generator._add_line(f"{final_display_object_var}.move_to(ORIGIN)")

    # 7. 缩放以适应区域边界 (带边距)
    if target_rect:
        generator._add_line(f"# 计算缩放因子以适应区域 '{target_region_id}' (保留边距)")
        # Use the video object's dimensions for scaling calculation even if grouped
        generator._add_line(f"base_width = {prefix}_video.width")
        generator._add_line(f"base_height = {prefix}_video.height")
        generator._add_line(f"target_width = {target_rect.width} * 0.9")  # 90% of region width
        generator._add_line(f"target_height = {target_rect.height} * 0.9")  # 90% of region height
        generator._add_line("width_scale = target_width / base_width if base_width > 0 else 1.0")
        generator._add_line("height_scale = target_height / base_height if base_height > 0 else 1.0")
        generator._add_line("scale_factor = min(width_scale, height_scale, 1.0)")  # Cap scaling at 1.0 (don't enlarge)
        generator._add_line("if scale_factor < 1.0:")  # Only scale down
        generator._add_line(f"    {final_display_object_var}.scale(scale_factor)")
        # Re-center after scaling
        generator._add_line(f"    {final_display_object_var}.move_to({list(target_rect.get_center())})")

    # 8. 保存对象引用到 self
    generator._add_line(f"# 保存对象引用 (ID: {reference_id})")
    generator._add_line(f"{reference_var} = {final_display_object_var}")

    # --- 使用动画添加到场景，并可能带有语音 ---
    transition_in = getattr(node, "transition_in", "fadeIn")
    narration_text = node.narration

    if narration_text:
        logger.info(f"为视频 {reference_id} 添加语音旁白")
        generator._add_line(f"with self.voiceover({repr(narration_text)}) as tracker: # noqa: F841")
        generator._indent_block()
        generator._add_line("# ---- 添加入场动画 (带语音) ----")
        if overlay_animation_style == "sequential" and text_animation_play_line:
            generator._add_line("# 首先添加视频部分")
            if transition_in == "fadeIn":
                generator._add_line(f"self.play(FadeIn({prefix}_video), run_time=0.5)")
            else:
                generator._add_line(f"self.add({prefix}_video)")
            generator._add_line("# 然后播放文本行的动画")
            generator._add_line(text_animation_play_line)
        else:
            generator._add_line(f"# 播放整体入场动画 ('{transition_in}')")
            if transition_in == "fadeIn":
                generator._add_line(f"self.play(FadeIn({reference_var}), run_time=1)")
            else:
                generator._add_line(f"self.add({reference_var})")
                # generator._add_line("self.wait(0.1)") # voiceover handles wait

        # 10. 等待视频播放完成 (voiceover 会处理，但保留获取时长的代码以防万一)
        generator._add_line("# 获取视频时长 (voiceover 会管理播放时间)")
        generator._add_line("try:")
        generator._add_line(f"    probe = ffmpeg.probe('{safe_video_path}')")
        generator._add_line('    video_info = next((s for s in probe["streams"] if s["codec_type"] == "video"), None)')
        generator._add_line(f'    duration = float(video_info["duration"]) / {speed} if video_info else 5.0')
        generator._add_line("except Exception as e:")
        generator._add_line(f"    logger.warning(f'无法获取视频 {video_path} 时长: {{e}}, 使用默认值 5 秒')")
        generator._add_line("    duration = 5.0")
        generator._add_line(
            "self.wait(max(0.1, duration - (self.renderer.time - start_time)))"
        )  # voiceover handles wait
        generator._dedent_block()
    else:
        logger.info(f"视频 {reference_id} 无语音旁白")
        generator._add_line("# ---- 添加入场动画 (无语音) ----")
        if overlay_animation_style == "sequential" and text_animation_play_line:
            generator._add_line("# 首先添加视频部分")
            if transition_in == "fadeIn":
                generator._add_line(f"self.play(FadeIn({prefix}_video), run_time=0.5)")
            else:
                generator._add_line(f"self.add({prefix}_video)")
            generator._add_line("# 然后播放文本行的动画")
            generator._add_line(text_animation_play_line)
        else:
            generator._add_line(f"# 播放整体入场动画 ('{transition_in}')")
            if transition_in == "fadeIn":
                generator._add_line(f"self.play(FadeIn({reference_var}), run_time=1)")
            else:
                generator._add_line(f"self.add({reference_var})")
                generator._add_line("self.wait(0.1)")

        # 10. 等待视频播放完成
        generator._add_line("# 获取视频时长并等待播放")
        generator._add_line("start_time = self.renderer.time")  # Capture time before wait
        generator._add_line("try:")
        generator._add_line(f"    probe = ffmpeg.probe('{safe_video_path}')")
        generator._add_line('    video_info = next((s for s in probe["streams"] if s["codec_type"] == "video"), None)')
        generator._add_line('    duration = float(video_info["duration"]) / speed if video_info else 5.0')
        generator._add_line("except Exception as e:")
        generator._add_line(f"    logger.warning(f'无法获取视频 {video_path} 时长: {{e}}, 使用默认值 5 秒')")
        generator._add_line("    duration = 5.0")
        generator._add_line("self.wait(max(0.1, duration - (self.renderer.time - start_time)))")

    # 11. 更新区域内容状态 (重要：用于后续清理)
    generator._update_region_content(target_region_id, reference_var, reference_id)

    logger.info(f"DisplayVideoNode 访问完成，生成了视频显示代码: {reference_var}")
