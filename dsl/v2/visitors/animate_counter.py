# dsl/v2/visitors/animate_counter.py

from typing import TYPE_CHECKING, Any

from loguru import logger
from manim import DEFAULT_MOBJECT_TO_MOBJECT_BUFFER

if TYPE_CHECKING:
    from ..ast_nodes import AnimateCounterNode
    from ..code_generator import CodeGenerator


def _generate_numeric_counter_code(
    generator: "CodeGenerator", node: "AnimateCounterNode", prefix: str, target_region_id: str, **kwargs: Any
) -> None:
    """生成数字计数器动画的 Manim 代码。"""
    logger.debug(f"生成数字计数器代码: {prefix}")

    # 计算小数位数，整数不显示小数位
    target_value = node.target_value
    decimal_places = 0
    if target_value % 1 == 0:  # 检查是否为整数
        decimal_places = 0
    else:
        target_str = str(target_value)
        if "." in target_str:
            decimal_places = len(target_str.split(".")[1])

    # 1. 创建数字显示
    generator._add_line(f"# 创建计数器: 从 {node.start_value} 到 {target_value}")
    generator._add_line(f"{prefix}_number = DecimalNumber(")
    generator._add_line(f"    number={node.start_value},")
    generator._add_line(f"    num_decimal_places={decimal_places},")
    generator._add_line("    include_sign=False,")
    generator._add_line("    group_with_commas=True,")
    generator._add_line("    edge_to_fix=RIGHT,")
    generator._add_line(")")

    # 2. 创建组合对象 (数字 + 可选的标签和单位)
    generator._add_line("# 创建组合对象 (数字 + 可选的标签和单位)")

    has_label = bool(node.label)
    has_unit = bool(node.unit)

    if has_label:
        generator._add_line("# 添加标签")
        generator._add_line(f'{prefix}_label = Text("{node.label}", font_size=24)')

    if has_unit:
        generator._add_line("# 添加单位")
        generator._add_line(f'{prefix}_unit = Text("{node.unit}", font_size=24)')

    # 计算动态间距 (仅用于标签和数字之间)
    target_val_int = int(abs(target_value))
    num_digits = len(str(target_val_int)) if target_val_int != 0 else 1
    dynamic_buff = DEFAULT_MOBJECT_TO_MOBJECT_BUFFER * 0.5 + num_digits * 0.25
    logger.debug(f"计算动态间距 dynamic_buff: {dynamic_buff} (digits: {num_digits})")

    generator._add_line("# 创建计数器组并按需排列")
    group_elements = [f"{prefix}_number"]
    if has_label:
        group_elements.insert(0, f"{prefix}_label")  # Label comes first
    if has_unit:
        group_elements.append(f"{prefix}_unit")  # Unit comes last

    generator._add_line(f"{prefix}_group = VGroup({', '.join(group_elements)})")

    if has_label and has_unit:
        generator._add_line(f"{prefix}_label.next_to({prefix}_number, LEFT, buff={dynamic_buff})")
        generator._add_line(
            f"{prefix}_unit.next_to({prefix}_number, RIGHT, buff=DEFAULT_MOBJECT_TO_MOBJECT_BUFFER * 0.5)"
        )
        generator._add_line(f"{prefix}_group.arrange(RIGHT, buff={dynamic_buff})")  # Arrange label-number-unit
    elif has_label:
        generator._add_line(f"{prefix}_label.next_to({prefix}_number, LEFT, buff={dynamic_buff})")
        generator._add_line(f"{prefix}_group.arrange(RIGHT, buff={dynamic_buff})")  # Arrange label-number
    elif has_unit:
        generator._add_line(
            f"{prefix}_unit.next_to({prefix}_number, RIGHT, buff=DEFAULT_MOBJECT_TO_MOBJECT_BUFFER * 0.5)"
        )
        generator._add_line(
            f"{prefix}_group.arrange(RIGHT, buff=DEFAULT_MOBJECT_TO_MOBJECT_BUFFER * 0.5)"
        )  # Arrange number-unit

    # 3. 获取目标区域并定位/缩放 Group
    target_rect = generator._get_region_rect(target_region_id)
    if target_rect:
        generator._add_line(f"# 定位并缩放计数器组到区域 '{target_region_id}'")
        generator._add_line(f"{prefix}_group.move_to({list(target_rect.get_center())})")

        generator._add_line(f"target_width = {target_rect.width}")
        generator._add_line(f"target_height = {target_rect.height}")
        generator._add_line("scale_factor = 1.0")
        generator._add_line(f"if {prefix}_group.width > 0 and target_width > 0:")  # Avoid division by zero
        generator._add_line(f"    scale_w = target_width * 0.8 / {prefix}_group.width")
        generator._add_line("    scale_factor = min(scale_factor, scale_w)")
        generator._add_line(f"if {prefix}_group.height > 0 and target_height > 0:")  # Avoid division by zero
        generator._add_line(f"    scale_h = target_height * 0.8 / {prefix}_group.height")
        generator._add_line("    scale_factor = min(scale_factor, scale_h)")

        generator._add_line("if scale_factor < 1.0:")
        generator._add_line(f"    {prefix}_group.scale(scale_factor)")
    else:
        generator._add_line("# 区域未找到，使用默认 ORIGIN 定位")
        generator._add_line(f"{prefix}_group.move_to(ORIGIN)")

    # 4. 将组添加到场景中
    generator._add_line("# 将计数器组添加到场景中")
    generator._add_line(f"self.play(Create({prefix}_group))")

    self_attribute_name = prefix + "_content"
    generator._add_line(f"self.{self_attribute_name} = {prefix}_group")
    generator._update_region_content(target_region_id, f"self.{self_attribute_name}", node.id or prefix)
    generator._add_line("self.wait(0.5)  # 短暂暂停，让观众看清初始值")

    # --- 动画计数并可能带有语音 ---
    narration_text = node.narration
    animation_block = [
        f"# 动画更新数字从 {node.start_value} 到 {target_value}",
        "self.play(",
        f"    ChangeDecimalToValue({prefix}_number, {target_value}),",
        f"    run_time={node.duration},",
        ")",
    ]
    if node.effect == "flash":
        animation_block.append(f"# 添加结尾强调效果: {node.effect}")
        animation_block.append(f"self.play(Flash({prefix}_number, color=WHITE, flash_radius=0.5))")
    elif node.effect == "zoom":  # Placeholder for potential zoom effect
        animation_block.append("# TODO: 实现 zoom 效果")
        logger.warning(f"Counter effect 'zoom' for {prefix} is not yet implemented.")
        # Example: animation_block.append(f"self.play({prefix}_number.animate.scale(1.2), rate_func=there_and_back, run_time=0.5)")

    if narration_text:
        logger.info(f"为计数器 {prefix} 添加语音旁白")
        generator._add_line(f"with self.voiceover(text={repr(narration_text)}) as tracker:")
        generator._indent_block()
        for line in animation_block:
            generator._add_line(line)
        generator._dedent_block()
    else:
        logger.info(f"计数器 {prefix} 无语音旁白")
        for line in animation_block:
            generator._add_line(line)

    logger.info(f"数字计数器代码生成完成: {prefix}_group")


def _generate_curve_graph_code(
    generator: "CodeGenerator", node: "AnimateCounterNode", prefix: str, target_region_id: str, **kwargs: Any
) -> None:
    """生成曲线图表动画的 Manim 代码。"""
    logger.debug(f"生成曲线图表代码: {prefix}")

    # 1. 清理目标区域 (已在 visit_animate_counter 中完成)
    # generator._clear_region(target_region_id) # No need to clear again

    # 2. 创建坐标系
    generator._add_line(f"# 创建坐标系 for {prefix}")
    generator._add_line(f"{prefix}_axes = Axes(")
    # TODO: Make ranges configurable or dynamic?
    generator._add_line("    x_range=[0, 6, 1],")
    generator._add_line("    y_range=[0, 35, 5],")
    generator._add_line("    x_length=8,")
    generator._add_line("    y_length=6,")
    generator._add_line(
        "    axis_config={'include_numbers': False, 'tip_shape': StealthTip, 'include_ticks': False, 'stroke_width': 4,},"
    )
    generator._add_line("    tips=True,")
    generator._add_line(")")

    # # 3. 添加轴标签
    # generator._add_line("# 添加轴标签")
    # # TODO: Use node.label for title? Or new fields for x/y labels?
    # generator._add_line(f"{prefix}_axis_labels = {prefix}_axes.get_axis_labels(")
    # generator._add_line("    x_label=Tex(\"Time\"), y_label=Tex(\"Value\")") # Escaped quotes
    # generator._add_line(")") # Close get_axis_labels parenthesis

    # 4. 定义指数函数 y = 2^x
    generator._add_line("# 定义并绘制指数函数 y = 2^x")
    generator._add_line(f"{prefix}_graph = {prefix}_axes.plot(")
    generator._add_line("    lambda x: x**2,")
    generator._add_line("    x_range=[0, 5, 1],")
    generator._add_line("    color=YELLOW,")
    generator._add_line("    use_smoothing=True,")
    generator._add_line("    stroke_width=8,")  # 设置曲线粗细
    generator._add_line(")")  # Close plot parenthesis

    # 5. 为曲线添加标签（放置在曲线顶端）
    generator._add_line("# 为曲线添加标签（位于曲线顶端）")
    generator._add_line("# 获取曲线的最大x值点")
    generator._add_line(f"curve_end_point = {prefix}_graph.points[-1]  # 获取曲线的最后一个点")
    target_value = f"{node.target_value} {node.unit}"
    generator._add_line(f'{prefix}_value_label = MathTex(r"{target_value}", font_size=48)')
    generator._add_line(f"{prefix}_value_label.next_to(curve_end_point, UR, buff=0.2)")
    generator._add_line(f"{prefix}_graph_label = {prefix}_value_label  # 保持变量名一致性")

    # 6. (可选) 添加标题 - 使用 node.label
    title_var = f"{prefix}_title"
    if node.label:
        generator._add_line("# 添加图表标题")
        # Use repr to handle quotes and special characters safely in the label string
        generator._add_line(f"{title_var} = Text({repr(node.label)}, font_size=36)")
    else:
        # If no label, create an empty Mobject placeholder to avoid errors later
        # But maybe better to conditionally add it to the group?
        generator._add_line(f"{title_var} = VGroup()")  # Create an empty group if no title
        logger.debug(f"No label provided for curve {prefix}, skipping title.")

    # 7. 组合所有元素
    generator._add_line("# 组合图表元素")
    # Conditionally add title to the group elements
    group_elements = [f"{prefix}_axes", f"{prefix}_graph", f"{prefix}_graph_label"]
    if node.label:
        group_elements.insert(0, title_var)  # Title at the beginning if exists

    # Ensure elements are correctly formatted strings for join
    generator._add_line(f"{prefix}_group = VGroup({', '.join(map(str, group_elements))})")

    # Arrange title relative to axes if it exists
    if node.label:
        generator._add_line(f"{title_var}.next_to({prefix}_axes, UP, buff=0.2)")  # Smaller buff

    # 8. 获取目标区域并定位/缩放 Group
    target_rect = generator._get_region_rect(target_region_id)
    if target_rect:
        generator._add_line(f"# 定位并缩放图表组到区域 '{target_region_id}'")
        # Move the group first before potentially scaling the title position relatively
        generator._add_line(f"{prefix}_group.move_to({list(target_rect.get_center())})")

        # Scale the entire group (including title) to fit the region
        generator._add_line(f"target_width = {target_rect.width}")
        generator._add_line(f"target_height = {target_rect.height}")
        generator._add_line("scale_factor = 1.0")
        # Use buffering (e.g., 0.9) to leave some space
        generator._add_line(f"if {prefix}_group.width > 0 and target_width > 0:")  # Avoid division by zero
        generator._add_line(f"    scale_w = target_width * 0.9 / {prefix}_group.width")
        generator._add_line("    scale_factor = min(scale_factor, scale_w)")
        generator._add_line(f"if {prefix}_group.height > 0 and target_height > 0:")  # Avoid division by zero
        generator._add_line(f"    scale_h = target_height * 0.9 / {prefix}_group.height")
        generator._add_line("    scale_factor = min(scale_factor, scale_h)")

        generator._add_line("if scale_factor < 1.0:")
        generator._add_line(f"    {prefix}_group.scale(scale_factor)")

        # Re-apply relative positioning for title *after* scaling if necessary
        # Scaling might distort relative positions slightly, re-arranging ensures consistency.
        # However, scaling the whole group usually preserves relative internal structure. Let's skip re-arranging for now.
        # if node.label and scale_factor < 1.0:
        #    generator._add_line(f"    {title_var}.next_to({prefix}_axes, UP, buff=0.2 * scale_factor)") # Scale buff?

    else:
        generator._add_line("# 区域未找到，使用默认 ORIGIN 定位")
        generator._add_line(f"{prefix}_group.move_to(ORIGIN)")
        # Arrange title relative to axes even if at ORIGIN
        if node.label:
            generator._add_line(f"{title_var}.next_to({prefix}_axes, UP, buff=0.2)")

    # 9. 动画显示
    generator._add_line("# 动画显示图表")
    animation_block = []
    if node.label:
        animation_block.append(f"self.play(Write({title_var}))")

    animation_block.extend(
        [
            "# 显示坐标轴、标签和标题 (如果存在)",
            f"self.play(Create({prefix}_axes))",
            "# 动态绘制指数曲线",
            f"self.play(Create({prefix}_graph), run_time={node.duration})",
            "# 显示曲线的方程标签",
            f"self.play(Write({prefix}_graph_label))",
            "# 添加结尾强调效果: flash",
            f"self.play(Flash({prefix}_graph_label))",
        ]
    )

    narration_text = node.narration
    if narration_text:
        logger.info(f"为曲线图表 {prefix} 添加语音旁白")
        generator._add_line(f"with self.voiceover(text={repr(narration_text)}) as tracker:")
        generator._indent_block()
        for line in animation_block:
            generator._add_line(line)
        generator._dedent_block()
    else:
        logger.info(f"曲线图表 {prefix} 无语音旁白")
        for line in animation_block:
            generator._add_line(line)

    # 10. 更新状态和清理准备
    self_attribute_name = prefix + "_content"
    generator._add_line(f"self.{self_attribute_name} = {prefix}_group")
    generator._update_region_content(target_region_id, f"self.{self_attribute_name}", node.id or prefix)

    logger.info(f"曲线图表代码生成完成: {prefix}_group")


def visit_animate_counter(generator: "CodeGenerator", node: "AnimateCounterNode", **kwargs: Any) -> None:
    """
    生成 AnimateCounterNode 对应的 Manim 代码。

    根据 `counter_type` 的值，生成数字计数器动画或曲线图表动画。
    """
    target_region_id = getattr(node, "target_region_id", None) or "main"
    logger.info(
        f"正在访问 AnimateCounterNode: type='{node.counter_type}', target_region='{target_region_id}', id='{node.id}'"
    )

    # Generate a unique prefix based on node properties to avoid collisions
    # Include counter_type in the hash for better distinction
    label_str = str(node.label) if node.label is not None else ""
    unit_str = str(node.unit) if node.unit is not None else ""  # Only relevant for 'counter' type but hash anyway
    value_str = str(node.target_value) if node.counter_type == "counter" else ""  # Only relevant for 'counter'
    content_hash = (
        abs(hash(value_str + label_str + unit_str + target_region_id + node.counter_type + (node.id or ""))) % 10000
    )
    prefix = f"{node.counter_type}_{content_hash}"  # e.g., counter_1234 or curve_5678

    # Always clear the target region before adding new content
    generator._clear_region(target_region_id)

    if node.counter_type == "counter":
        _generate_numeric_counter_code(generator, node, prefix, target_region_id, **kwargs)
    elif node.counter_type == "curve":
        # Check for Manim imports needed by curve generation
        _generate_curve_graph_code(generator, node, prefix, target_region_id, **kwargs)
    else:
        logger.error(f"未知的 AnimateCounterNode counter_type: {node.counter_type}")
        # Optionally raise an error or generate a placeholder comment
        generator._add_line(f"# 错误: 未知的 counter_type '{node.counter_type}'")

    logger.info(f"AnimateCounterNode 访问完成: {prefix}")
