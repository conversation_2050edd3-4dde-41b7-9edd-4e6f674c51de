# agentic-feynman 材料增强流程重构 TODO

1. **接口调整**  
   - [x] enhance_material函数参数调整：支持同时传入original_content和material_content，并修改所有调用处。
2. **内容插桩流程**  
   - [x] 增加基于大模型的插桩分析逻辑：输入original_content、material_content和purpose，输出带插桩标记的完整content文本（方式A）。
   - [x] 设计插桩标记格式（如特殊tag或JSON字符串），确保后续易于识别和替换。
3. **工具调用与内容融合**  
   - [x] 遍历插桩后的content，遇到插桩标记时自动调用对应工具，将结果替换到原文合适位置，并附加工具说明和suggested_usage。
   - [x] 其余内容原样拼接，生成最终增强材料。
4. **代码结构与兼容性调整**  
   - [x] 检查并适配material_agent_refactored.py等相关模块的调用和输出，确保整体流程无缝衔接。（已完成MaterialAgent主流程与新enhancer接口的兼容）
5. **测试与示例**  
   - [ ] 增加或更新测试用例，验证新流程的正确性与效果。
   - [ ] 提供典型输入输出示例，便于后续维护和优化。
