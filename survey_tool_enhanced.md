# Agent AI：迈向通用人工智能的多模态智能体综述

## 引言：Agent AI 的勃兴与通用人工智能的愿景

自1956年达特茅斯会议定义AI以来，创造能感知、规划并与环境交互的系统一直是人工智能的核心目标。随着大语言模型（LLM）和视觉语言模型（VLM）的崛起，AI正迎来融合语言、视觉、记忆、推理和适应性的新范式——即多模态智能体（Agent AI），标志着亚里士底德整体论在AI领域的回归。

本综述将Agent AI定义为：一类能感知视觉、语言及其他环境数据，并能产生有意义具身行动的交互系统。它通过融合外部知识、多感官输入和人类反馈，专注于基于具身行动预测来改进智能体。通过在真实环境中开发具身智能体系统，有望缓解大基础模型中常见的“幻觉”现象，减少其生成与环境不符输出的倾向。Agent AI 不仅涵盖物理世界的行动与交互，更展望了未来，人们可在虚拟环境中轻松创建并与具身智能体交互。这一愿景，有可能将我们引向通用人工智能 (AGI) 的新路线。

![Agent AI 系统概览](output/2401.03568/medias/page_1_image_0.png)
*Agent AI 系统概览，展现了其在不同领域和应用中感知与行动的能力。Agent AI 正成为迈向通用人工智能 (AGI) 的有前景途径。*

当前，多模态AI系统正变得无处不在。将这些系统具身化为物理和虚拟环境中的智能体，是一种极具前景的交互方式。现有系统已将LLMs和VLMs等基础模型作为构建具身智能体的基本模块，这极大地促进了模型处理和解释视觉及上下文数据的能力，对创建更复杂、更具情境感知的AI系统至关重要。


<补充素材>
<用途>
在讲解开始时，播放论文页面的录屏，作为视觉引入，展现论文的完整视图，让观众对原始材料有一个直观印象。
</用途>
<工具名>
screen_recording
</工具名>
<工具输出>
{'tool_name': 'screen_recording', 'type': 'video_recording', 'source_type': 'github', 'url': 'https://github.com/xiangechen/chili3d', 'file_path': 'tool_enhance/screen_record.mp4', 'full_path': 'tool_enhance/screen_record.mp4', 'status': 'exists'}
</工具输出>
</补充素材>

## 核心体系：Agent AI 的集成与范式

### 2.1 Agent AI 的集成：以基础模型为基石

Agent AI 的发展与大基础模型如 LLMs 和 VLMs 的进步密切相关。尽管这些模型在理解、生成、编辑和与未知环境交互方面仍有局限，但将其集成到 Agent AI 框架中，可显著提升其对用户输入的理解，从而构建复杂且适应性强的人机交互系统。Agent AI 的最新进展，通过这些基础模型，为具身智能体解锁通用智能带来了紧迫的催化作用。大型动作模型或智能体-视觉-语言模型为通用具身系统（如在复杂环境中的规划、问题解决和学习）开辟了新的可能性。

**无限智能体（Infinite AI Agent）**：理想的智能体系统应具备预测建模、决策制定、处理歧义和持续改进的能力。传统AI智能体需针对每个新任务收集大量训练数据，成本高昂甚至不可行。“无限智能体”概念旨在将从通用基础模型（如GPT-X、DALL-E）中学习到的记忆信息迁移到新的领域或场景，用于物理或虚拟世界中的场景理解、生成和交互编辑。

![The multi-model agent AI for 2D/3D embodied generation and editing interaction in cross-reality.](output/2401.03568/medias/page_8_image_4.png)
*图2: 跨现实环境下用于2D/3D具身生成和编辑交互的多模态 Agent AI。*

这种无限智能体的一个应用实例是机器人学中的 RoboGen，它能够自主运行任务建议、环境生成和技能学习的循环，将大型模型中嵌入的知识迁移到机器人领域。

**Agent AI 与大型基础模型**：大型基础模型在为智能体行动生成基准数据方面扮演关键角色，例如机器人操作和导航。它们通过图像编辑模型生成高层子目标，或利用 LLM 识别地标并结合 VLM 进行视觉关联，从而引导低层策略。

然而，集成大型基础模型也带来了挑战：

-   **幻觉**：文本生成智能体常出现“幻觉”，即生成无意义或与源内容不符的文本。多模态智能体系统中的 VLM 也可能出现幻觉，通常是由于过度依赖训练数据中物体和视觉线索的共现。一种有前景的缓解方法是使用检索增强生成（RAG）或通过外部知识检索来确保语言输出的可靠性。
-   **偏见与包容性**：AI智能体可能因训练数据中存在的社会偏见而产生偏见。为解决此问题，需要多样化和包容性的训练数据、偏见检测与校正、伦理准则以及持续监控和更新。
-   **数据隐私与使用**：AI智能体处理用户数据时，必须考虑数据收集、使用、存储、安全、删除、保留、可移植性和隐私政策等问题，确保用户隐私和数据合规性。
-   **可解释性**：当前AI模型的黑箱特性使其决策过程难以理解。通过模仿学习中的解耦和泛化，可提升智能体的可解释性。将 LLM/VLM 的焦点通过提示工程限制，或通过更高层的预执行验证和修改，可增强可解释性。

![Figure 3: Example of the Emergent Interactive Mechanism](output/2401.03568/medias/page_12_image_5.png)
*利用智能体识别与图像相关的文本的涌现交互机制示例。该任务涉及使用网络的具身多模态AI智能体和人工标注的知识交互样本来融入外部世界信息。*

![Figure 4: A robot teaching system](output/2401.03568/medias/page_14_image_7.png)
*图4：机器人教学系统(Wake et al., 2023c)。用户可审查和调整每个步骤，确保系统的安全和鲁棒性。*

### 2.2 Agent AI 范式：迈向多模态通用智能体

本综述提出了一个用于训练 Agent AI 的新范式和框架。该框架旨在：

-   利用现有预训练模型和预训练策略，有效引导智能体理解文本或视觉输入等重要模态。
-   支持足够的长期任务规划能力。
-   整合记忆框架，用于编码和检索学习到的知识。
-   允许利用环境反馈有效训练智能体学习采取何种行动。

![多模态通用智能体的新范式](output/2401.03568/medias/page_15_image_8.png)
该新范式由五个主要模块组成：

1.  **环境与感知**：包括任务规划和技能观察。
2.  **智能体学习**：负责智能体的学习过程。
3.  **记忆**：存储和检索学习到的知识。
4.  **智能体行动**：执行具身行动。
5.  **认知**：处理高级推理和理解。


<补充素材>
<用途>
在讲解多模态通用智能体新范式时，通过动态绘制架构图，清晰展示其五个核心模块及其相互关系，帮助观众直观理解系统构成。
</用途>
<工具名>
architecture_diagram
</工具名>
<工具输出>
{'tool_name': 'architecture_diagram', 'type': 'architecture_diagram', 'file_path': 'tool_enhance/architecture_diagram_output.json', 'data': {'summary': {'content_theme': '系统架构可视化', 'target_audience': '架构图生成', 'processing_focus': '架构图生成和动画配置'}, 'architecture_config': {'content_description': '该新范式由五个主要模块组成：1) 环境与感知：包括任务规划和技能观察。2) 智能体学习：负责智能体的学习过程。3) 记忆：存储和检索学习到的知识。4) 智能体行动：执行具身行动。5) 认知：处理高级推理和理解。', 'animation_type': 'animate_architecture_diagram', 'narration': '这个架构图展示了系统的整体设计和组件关系，帮助理解架构图生成的技术实现。', 'id': 'arch_diagram_3993'}, 'metadata': {'processing_time': '实时生成', 'data_quality': '基于内容自动提取', 'content_length': 105, 'description_length': 105}}, 'status': 'created'}
</工具输出>
</补充素材>

**LLM 与 VLM 的引导作用**：LLM在任务规划、世界知识和逻辑推理方面表现出色，而VLM（如CLIP）提供通用视觉编码器和零样本视觉识别能力。这些模型可作为 Agent AI 组件的引导模型。

**智能体 Transformer 定义**：除了使用固定的 LLM 和 VLM，也可使用单一的智能体 Transformer 模型，将视觉、语言和“智能体令牌”（agent tokens）都作为输入。智能体令牌用于特定具身行为（如机器人控制器输入或工具API调用），相比专有 LLM，智能体 Transformer 更易定制、更可解释、且可能成本更低。

**智能体 Transformer 创建**：训练智能体 Transformer 模型主要分两步：
1.  **定义领域内的目标**：明确智能体在每个特定环境中的目标和行动空间，分配唯一的智能体令牌，并定义成功完成任务的规则。
2.  **持续改进**：持续监控模型性能，收集反馈进行微调和更新，并确保模型不产生偏见或不道德的输出。

## 学习策略：Agent AI 的训练机制

Agent AI 的学习策略强调了一种通过主动寻求用户反馈、行动信息和有用知识来进行生成和交互的范式。

### 3.1 强化学习（RL）

强化学习是训练具有智能行为的交互式智能体的重要方法。RL 虽具高度可扩展性，但在 reward 设计、数据收集效率和长时序步骤方面面临挑战。LLM/VLM 的引入为缓解这些问题提供了潜力：

-   **Reward 设计**：LLMs/VLMs 可用于设计 reward 函数，理解任务复杂性。
-   **数据收集效率**：LLMs/VLMs 可助力数据生成或作为 reward 函数一部分，提升数据效率。
-   **长时序步骤**：LLMs 可将复杂任务分解为子目标，并结合低层 RL 策略，解决长时序控制问题。

### 3.2 模仿学习（IL）

模仿学习通过模仿专家数据来学习策略。行为克隆（Behavioral Cloning, BC）是 IL 的主要框架，通过直接复制专家行为来训练机器人。最近的 BC 方法结合 LLM/VLM 技术，实现了更先进的端到端模型，例如 RT-1 和 RT-2，通过大量训练数据展现高泛化性能。

### 3.3 传统 RGB 和 In-context Learning

-   **传统 RGB**：利用 RGB 输入学习智能体行为面临维度诅咒。解决方案包括使用更多数据、引入归纳偏置、或通过图形模拟器合成数据、并弥合 Sim-to-Real 差距。
-   **In-context Learning**：通过在 LLM 提示中提供任务示例来上下文化模型输出。在多模态基础模型中，已证明在少量样本下对视觉理解任务有效。

### 3.4 智能体系统中的优化

智能体系统优化包括空间和时间两个维度。

-   **空间优化**：涉及多智能体协同、资源分配和空间组织，如利用大规模批处理强化学习和多智能体自博弈。
-   **时间优化**：关注任务调度、序列和时间线效率，如 LLM-DP 和 ReAct 等方法通过交互整合环境因素进行高效任务规划。

---

## 智能体分类：理解 Agent AI 的多样性

本综述将 Agent AI 分为多个类别：通用智能体、具身智能体、模拟与环境智能体、生成式智能体、知识与逻辑推理智能体，以及 LLM 和 VLM 智能体。


<补充素材>
<用途>
在介绍Agent AI的分类时，以思维导图的形式清晰展示其主要类别和子类别，帮助观众建立Agent AI领域的整体知识体系和概念关系。
</用途>
<工具名>
mindmap_generation
</工具名>
<工具输出>
{'tool_name': 'mindmap_generation', 'type': 'mindmap_generation', 'file_path': 'tool_enhance/mindmap_generation_output.json', 'data': {'suitable': True, 'reason': '内容清晰地呈现了Agent AI的多种分类，具有明确的主题和子主题的层次结构，非常适合生成思维导图。', 'mindmap_data': {'标题': 'Agent AI 的分类', '子章节': [{'标题': '通用智能体'}, {'标题': '具身智能体', '子章节': [{'标题': '行动智能体'}, {'标题': '交互式智能体'}]}, {'标题': '模拟与环境智能体'}, {'标题': '生成式智能体', '子章节': [{'标题': 'AR/VR/混合现实智能体'}]}, {'标题': '知识与逻辑推理智能体', '子章节': [{'标题': '知识智能体'}, {'标题': '逻辑智能体'}, {'标题': '情绪推理智能体'}, {'标题': '神经符号智能体'}]}, {'标题': 'LLM 和 VLM 智能体'}]}, 'layout_style': 'balance', 'max_depth': 3, 'focus_sequence': ['Agent AI 的分类', '通用智能体', '具身智能体', '模拟与环境智能体', '生成式智能体', '知识与逻辑推理智能体', 'LLM 和 VLM 智能体'], 'narration': '此思维导图梳理了Agent AI的多种分类，包括通用型、具身型、模拟与环境型、生成型、知识与逻辑推理型以及基于LLM和VLM的智能体，清晰展示了Agent AI的不同类型和演进方向。', 'metadata': {'total_nodes': 15, 'max_depth': 2, 'structure_quality': '良好'}}, 'status': 'created'}
</工具输出>
</补充素材>

### 4.1 通用智能体领域

通用智能体（GAs）的任务是成为对用户真正有价值的工具，它们需要自然交互，并能泛化到广泛的情境和模态。多模态智能体 AI（MMA）作为一个研究和产业社区的论坛，其进展由大型基础模型和交互式 AI 推动。

### 4.2 具身智能体

具身AI旨在创建能够学习并创造性地解决需要与环境交互的挑战性任务的智能体（如机器人）。

-   **行动智能体（Action Agents）**：在模拟物理环境或真实世界中执行物理动作的智能体，需积极与环境互动。可应用于游戏 AI 和机器人领域。在游戏 AI 中，智能体可与游戏环境和其他实体互动，自然语言可实现智能体与人类的流畅交流。例如在竞争性的外交游戏中，结合语言模型和 RL 的行动策略能实现人类水平的玩家表现。在不以优化特定目标为导向的场景（如模拟城镇居民），基础模型通过模仿人类行为，结合外部记忆，能生成可信的、具有对话和日常安排的智能体。
-   **交互式智能体（Interactive Agents）**：能够与世界进行交互的更广义智能体。其交互不一定需要物理动作，可能涉及向用户传递信息或修改环境。例如，具身交互式智能体可通过对话回答用户问题，或帮助用户解析现有信息，类似于聊天机器人。

### 4.3 模拟与环境智能体

通过与环境交互进行试错是 AI 智能体学习行为的有效方法，例如强化学习。由于物理智能体训练的时间成本和安全风险，模拟器成为训练策略的常用方法。目前，已提出多种用于具身 AI 研究的模拟平台，涵盖导航（如 Habitat）到物体操作（如 VirtualHome）。这些模拟器有助于学习实际场景中涉及智能体和机器人交互的策略，以及利用人类演示动作的 IL 策略学习。

### 4.4 生成式智能体

大型生成式 AI 模型在降低交互式内容制作成本方面潜力巨大，能赋能独立内容创作者。该类智能体的目标不仅是向场景添加交互式 3D 内容，还包括：向物体添加任意行为和交互规则、通过多模态 GPT-4V 模型从草图生成完整关卡几何体、使用扩散模型重新纹理化内容、根据简单用户提示创建自定义着色器和视觉特效。

-   **AR/VR/混合现实（XR）智能体**：XR 设置需要大量人工来创建虚拟世界的角色、环境和物体。XR 智能体通过促进创作者与构建工具的交互，可以协助此过程。早期实验表明，GPT 模型可在 Unity 引擎中用于调用引擎特定方法、下载 3D 模型并分配行为状态树和动画。该类智能体旨在构建一个高效连接大型 AI 模型（例如 GPT 系列和扩散图像模型）与渲染引擎的平台和工具集。

### 4.5 知识与逻辑推理智能体

-   **知识智能体**：对获取的知识系统进行推理，包括隐含知识（如 LLM 预训练所得）和显式知识（如知识库），以确保 AI 响应和行动与已知事实和逻辑原则一致。融合两者对于构建能理解、解释和运用知识的智能体至关重要。
-   **逻辑智能体**：在系统中应用逻辑推理处理特定数据或解决逻辑推理任务的组件。大型基础模型（如 GPT-4）的逻辑推理能力通常集成在总体架构中。然而，引入单独的逻辑子模块（如通过修改 LLM 的 Token 嵌入过程来显式建模逻辑层次）可以增强其逻辑能力。
-   **情绪推理智能体**：情绪理解和同理心在人机交互中至关重要。开发具有同理心感知的智能体是交互式智能体的一个有前景的方向，旨在创建能广泛进行情感理解的智能体，尤其是在当前许多语言模型在情感理解和同理心推理方面表现出偏见的情况下。
-   **神经符号智能体**：结合神经和符号系统。通过编码器-解码器模型将自然语言问题映射到形式语言解决方案，捕捉隐含的离散符号结构信息。此外，例如 Localized Visual Commonsense 模型，通过大型语言模型采样局部常识知识，并结合视觉模型自动生成描述，以支持精确的图像内推理任务。

### 4.6 LLM 和 VLM 智能体

LLM 作为智能体在任务规划方面表现出色，利用其大规模领域知识和零样本规划能力执行智能体任务。机器人研究中也利用 LLM 将自然语言指令分解为子任务，并通过低层控制器执行。同时，基于大规模文本、图像、视频数据训练的通用视觉对齐大型语言模型（如 RT-2）也展现出作为多模态具身智能体在各种环境中行动的潜力。

---

## 应用范畴：Agent AI 的实际落地

### 5.1 游戏领域：提升沉浸感与智能体验

游戏为测试 LLM 和 VLM 的智能体行为提供了独特的沙盒环境，扩展了其协作和决策能力。

-   **NPC 行为**：传统游戏 NPC 行为由预定义脚本驱动，缺乏动态适应性。LLMs 有助于赋予 NPC 自主性和适应性，通过处理海量文本习得模式，生成更具变化和类人化的响应，从而不断完善其行为。
-   **人类-NPC 交互**：利用 LLM 和 VLM 技术，游戏系统能分析和学习人类行为，提供更类人化的交互，提升游戏真实性和沉浸度，也为在受控但复杂环境中探索人机交互提供了平台。
-   **基于智能体的游戏分析**：LLM 可分析游戏内文本数据（如聊天记录、玩家反馈），识别玩家行为模式和偏好，以改进游戏机制和叙事。VLM 可解析游戏会话中的图像和视频数据，分析用户意图和行动。结合玩家互动、像素输入和自然语言规划，智能体模型可助力游戏动态的持续改进。
-   **场景合成**：游戏场景合成涉及自动生成 3D 场景。LLM 和 VLM 可利用网络规模知识制定规则，设计不重复、视觉震撼的景观，确保生成资产的语义一致性和多样性。同时，它们能协助物体摆放，遵循预定义或学习到的规则和美学，加速关卡设计。先进的算法能模拟自然光照和动态天气效果，提升真实感。LLM 可基于现实光照和大气条件数据集开发更逼真的算法，并根据玩家行动、游戏状态实时调整效果，提供更具互动性和沉浸感的体验。

![The embodied agent for user interactive gaming action prediction and interactive editing with Minecraft Dungeons gaming sense simulation and generation via GPT-4V.](output/2401.03568/medias/page_25_image_11.png)
*Minecraft Dungeons 游戏场景模拟与生成中用于用户交互式游戏动作预测和交互编辑的具身智能体（通过 GPT-4V）。*
GPT-4V 可用于高层描述和动作预测，生成与游戏视频相符的相关高层描述。这表明 Agent 增强文本为使用游戏动作先验生成 3D 场景提供了一种新方法，有助于改善场景的自然度。

![Figure 9: GPT-4V can effectively predict the high-level next actions when given the 'action history" and a 'gaming target" in the prompt. Furthermore, GPT-4V accurately recognized that the player is holding wooden logs in their hand and can incorporate this perceived information into its plan for future actions. Although GPT-4V appears to be capable of predicting some low-level actions (such as pressing 'E' to open the inventory), the model's outputs are not inherently suitable for raw low-level action prediction (including mouse movements) and likely requires supplemental modules for low-level action control.](output/2401.03568/medias/page_26_image_12.png)
GPT-4V 在给定“动作历史”和“游戏目标”后，能有效预测高级别下一步动作。*

### 5.2 机器人：驱动物理世界的智能与操作

机器人是需要有效与环境交互的代表性智能体。

-   **视觉运动控制**：整合视觉感知和运动动作，使机器人能解释视觉数据并相应调整动作，确保在装配线、工业自动化或辅助老年人等应用中的精确高效操作。在无控环境中，视觉信息对检测执行错误和实时验证结果至关重要。
-   **语言条件操作**：机器人系统能根据语言指令解释和执行任务，降低人机交互门槛。关键是开发鲁棒的自然语言处理算法，将指令（从直接命令到抽象指令）转换为可操作任务，并泛化到不同任务和环境。
-   **技能优化**：虽然 LLMs 在机器人任务规划方面有效，但涉及物理交互（如抓取）的任务需要更深的环境理解。这方面面临从场景中捕捉细微间接线索并有效转化为机器人技能的挑战。机器人社区正致力于收集增强数据集或开发直接从人类演示中获取技能的方法（如学习示范，模仿学习）。

**LLM/VLM 在机器人中的应用**：

-   **多模态系统**：将 LLM 和 VLM 作为编码器集成到端到端系统中，处理多模态信息，指导机器人基于语言指令和视觉线索执行动作。
-   **任务规划与技能训练**：LLM 的语言处理能力能将指令分解为机器人动作步骤，提升任务规划。LLM/VLM 还可用于设计奖励函数、生成数据或作为奖励函数的一部分来训练技能。

![Figure 13: Overview of the robot teaching system that integrates a ChatGPT-empowered task planner. The process involves two steps: Task planning, where the user employs the task planner to create an action sequence and adjusts the result through feedback as necessary, and Demonstration, where the user visually demonstrates the action sequence to provide information needed for robot operation. The vision system collects visual parameters that will be used for robot execution.](output/2401.03568/medias/page_31_image_16.png)
整合 ChatGPT 任务规划器的机器人教学系统概览。利用 ChatGPT 的任务规划能力，并通过人工示教进行参数化，可以实现精确有效的机器人操作。该系统能够根据语言指令和环境描述生成任务序列，并允许用户审查和调整以确保鲁棒性。

-   **现场优化**：通过将任务规划与实时环境数据整合，动态调整机器人技能，以应对不可预测的环境条件。这包括计算动作可行性、输出任务步骤的前提条件和后置条件，以及检测前提条件错误进行修订。
-   **对话智能体**：LLM 有助于创建与人类进行自然、情境敏感交互的对话机器人，处理和生成模仿人类对话的响应。LLM 在概念和情感属性估计方面发挥作用，提升人机通信的自然性和效率。
-   **导航智能体**：利用 LLM/VLM 增强的导航技术，使机器人能在更具挑战性的环境中导航，例如零样本目标导航和视觉语言导航（VLN）。

![Figure 18: Demonstration of embodied agent for the VLN task (Wang et al., 2019). The instruction, the local visual scene, and the global trajectories in a top-down view is shown. The agent does not have access to the top-down view. Path A is the demonstration path following the instruction. Path B and C are two different paths executed by the agent.](output/2401.03568/medias/page_35_image_34.png)
具身智能体在 VLN 任务中的演示。通过强化学习强制跨模态匹配，并通过自监督模仿学习来探索未见环境。

### 5.3 医疗健康：变革诊断与患者护理

LLM 和 VLM 在医疗健康领域具有巨大潜力，可作为诊断智能体、患者护理助手或治疗辅助工具。

-   **诊断智能体**：LLMs 作为医疗聊天机器人，通过理解多种语言、文化和健康状况，提供公平的医疗服务。然而，需警惕幻觉风险，因为错误诊断可能导致严重后果。
-   **知识检索智能体**：将诊断智能体与医疗知识检索智能体结合，可显著减少幻觉，提升响应质量和精确度。
-   **远程医疗与远程监控**： Agent AI 可通过辅助分诊信息、协调患者与医疗服务提供者，提高医疗可及性、改善沟通，并提升效率、降低成本。

![Figure 19: Example prompts and responses when using GPT-4V within the domain of healthcare image understanding. From left to right: (1) an image of a nurse and doctor conducting a CT scan, (2) a synthetic image of an irregular EKG scan, and (3) an image from the ISIC (Codella et al., 2018) skin lesion dataset. We can see that GPT-4V possesses significant medical knowledge and is able to reason about medical images. However, due to safety training, it is unable to make diagnoses for some medical images.](output/2401.03568/medias/page_37_image_35.png)
GPT-4V 在医疗图像理解领域的问答示例。它能理解医疗设备和程序，但出于安全考虑，不进行诊断。

![Figure 20: Example prompts and responses when using GPT-4V within the domain of healthcare video understanding. We input the example videos as 2x2 grids with overlaid text indicating the order of frames. In the first two examples, we prompt GPT-4V to examine the frames in the video to detect the clinical bedside activities performed on the volunteer patients. For the final example, we attempt to prompt GPT-4V to assess an echocardiogram video, however due to GPT-4V's safety training, it does not provide a detailed response. For clarity, we bold text that describes the activity of interest, and abbreviate model responses that are unnecessary. We gray-out faces from the individuals to preserve their privacy.](output/2401.03568/medias/page_38_image_37.png)
GPT-4V 在医疗视频理解领域的问答示例。展示了其识别患者护理活动的能力，但在技术视频分析上受安全训练限制。*

### 5.4 多模态智能体：融合视觉与语言

视觉和语言理解集成对于开发复杂的多模态 AI 智能体至关重要，包括图像字幕、视觉问答、视频语言生成和视频理解等任务。

-   **图像-语言理解与生成**：该任务涉及解释图像视觉内容并生成关联语言描述，需识别图像中物体、理解空间关系、生成准确描述场景的语句，并运用推理技能处理知识密集型视觉推理。
-   **视频与语言理解与生成**：视频理解将图像理解扩展到动态视觉内容，涉及解释视频帧序列并结合音频或文本信息。智能体应能与视觉、文本和音频模态交互，展现对视频内容的先进理解。
-   **知识密集型模型**：如知识增强型 Transformer (KAT)，结合了 GPT-3 的隐含知识和网站的显式知识，通过编码器-解码器结构进行集成，并在回答生成过程中进行联合推理。

![Figure 23: The KAT model (Gui et al., 2022a) uses a contrastive-learning-based module to retrieve knowledge entries from an explicit knowledge base and uses GPT-3 to retrieve implicit knowledge with supporting evidence. The integration of knowledge is processed by the respective encoder transformer and jointly with reasoning module and the decoder transformer via end-to-end training for answer generation.](output/2401.03568/medias/page_40_image_39.png)
KAT 模型架构，通过对比学习从显式知识库检索知识条目，并使用 GPT-3 检索带有支持证据的隐式知识。*

-   **视觉-语言 Transformer 智能体**：如 VLC 模型，仅用图像-字幕对预训练，通过简单线性投影和掩码图像/语言建模，在各种视觉-语言任务中取得有竞争力的结果，展示了大规模、弱监督、开放领域视觉-语言模型的潜力。

![Figure 24: The overall architecture of the VLC model (Gui et al., 2022b). Our model consists of three modules: (1) Modality-specific projection. We use a simple linear projection to embed patched images and a word embedding layer to embed tokenized text; (2) Multi-modal encoder. We use a 12-layer ViT (Dosovitskiy et al., 2021) initialized from MAE (He et al., 2022) (ImageNet-1K without labels) as our backbone; (3) Task-specific decoder. We learn our multi-modal representations by masked image/language modeling and image-text matching which are only used during pre-training. We use a 2-layer MLP to fine-tune our multi-modal encoder for downstream tasks. Importantly, we find that the masked image modeling objective is important throughout second-stage pre-training, not only for initialization of the visual transformer.](output/2401.03568/medias/page_41_image_40.png)
VLC 模型总体架构。

### 5.5 NLP 领域的 Agent：提升语言理解与生成

-   **LLM 智能体**：通过工具使用、知识库查询、改进推理与规划、结合系统与人类反馈等方式，提升语言理解和生成能力，例如 AutoGen 和 Retrieve What You Need。
-   **通用 LLM 智能体**：在理解智能体规划和人类反馈以进行知识推理和自然语言生成方面，具有关键作用。
-   **指令遵循 LLM 智能体**：专注于训练 LLM 智能体有效遵循人类指令，通过强化学习与人类反馈（RLHF）或指令调优，使其能根据指令生成响应。

![Figure 29: The logic transformer agent model (Wang et al., 2023e). We integrate a logical reasoning module into the transformer-based abstractive summarization model in order to endow the logic agent the ability to reason over text and dialogue logic, so that it can generate better-quality abstractive summarizations and reduce factuality errors.](output/2401.03568/medias/page_47_image_48.png)
逻辑 Transformer 智能体模型 (Wang et al., 2023e)。通过整合逻辑推理模块，提升抽象摘要质量并减少事实性错误。*

---

## 未来展望：跨模态、跨领域与持续学习

### 6.1 跨模态与跨领域理解

构建通用智能体面临跨模态缺乏大规模数据集和不同领域视觉外观及动作空间差异的挑战。现有多模态系统常使用固定子模块，但未来需改变策略，共同优化视觉编码器和 LLM 以实现更深度的跨模态理解。跨领域理解也类似，现有方法通常为每个特定领域单独微调模型，未能捕捉领域间的共性。

### 6.2 跨现实交互与 Sim-to-Real 迁移

使 AI 智能体成功理解和执行跨现实任务是一项持续挑战，尤其是其视觉差异和独立环境物理特性。Sim-to-Real 迁移是关键问题，旨在将模拟环境中训练的模型部署到真实世界，解决模拟与现实间（例如干扰、光照、重力）的差异。常用方法包括：

-   **领域随机化**：在模拟环境中随机变化参数，以应对真实世界的不确定性。
-   **领域适应**：通过大量模拟图像和少量真实世界图像训练模型，弥合模拟与现实之间的差距。
-   **模拟改进**：通过系统识别技术和照片级真实模拟器，提升模拟的真实性。

### 6.3 智能体的持续学习与自我提升

当前基于基础模型的 AI 智能体能够从多种数据源中学习，这带来了两个关键结果：

-   **人类交互数据**：利用大量的智能体-人类交互数据来训练和改进智能体。这可通过筛选成功与不成功的交互示例、人类偏好学习（用户选择最佳输出）、以及通过“红队测试”发现并纠正有害输出来实现。
-   **基础模型生成数据**：利用强大的基础模型人工制品，通过各种提示和数据配对技术，提取并生成有意义的训练数据。例如，LLM 指令调优允许将更大的专有 LLM 的输出用于微调更小的开源模型，实现知识蒸馏。VLM 也通过重新标注图像或视频来改进训练数据，提升后续 VLM 的性能。

尽管目前 AI 智能体大多与现有的预训练基础模型绑定，通常不进行持续的环境交互学习，但这是一个令人兴奋的未来方向。已有研究表明机器人控制的自改进智能体可通过环境交互持续学习和改进，无需监督。

---

## 数据集与排行榜：推动 Agent AI 发展

为加速研究，我们提出了两个基准数据集：“CuisineWorld”和“VideoAnalytica”，并发布了一系列基线模型，鼓励参与者探索新模型和系统。

### 7.1 CuisineWorld：多智能体游戏基准数据集

“CuisineWorld”是一个类似“Overcooked!”的文本游戏，为 AI 智能体协作提供了平台。该数据集将测试多智能体系统的协作效率，评估 LLM 及其他系统在动态场景中如何高效协作，尤其关注智能体对目标的理解和相互协调能力。它支持两种模式：集中式调度模式和去中心化模式。

该基准测试包含文本接口、可扩展的任务定义文件、多智能体交互接口以及人机交互功能。我们使用“协作分数”（CoS）来量化协作效率，并利用人工智能系统自动评估，同时进行人工评估。

### 7.2 VideoAnalytica：音频-视频-语言预训练数据集

“VideoAnalytica”是一个用于分析性视频演示理解的新基准。它关注如何利用视频演示来更好地理解长篇教学视频中嵌入的复杂、高层推理。其目标是评估视频语言模型的认知推理能力，使其超越单纯的识别任务和基本理解，达到更复杂和细致的视频理解水平。

VideoAnalytica 强调整合多种模态（如音频、视频和语言），以及模型应用领域特定知识来情境化和解释视频中呈现信息的能力。它主要包含两个任务：

1.  **视频文本检索**：需要准确检索教学视频中的相关文本，区分相关和不相关信息，并处理 LLM 生成的“强负例”。
2.  **视频辅助信息问答**：要求模型根据从视频中提取的信息回答问题，重点在于需要分析推理和彻底理解视频演示的复杂问题。

---

## 伦理考量与多元化声明

### 8.1 伦理考量

多模态 Agent AI 系统在交互式 AI、内容生成等方面应用广泛，具有积极效益。然而，也存在被恶意利用的风险，例如生成操纵性或欺骗性内容。因此，AI 系统开发必须遵循负责任的 AI 准则，如明确告知用户内容由 AI 生成，并提供用户控制。

需特别警惕在医疗等敏感领域部署 AI 智能体，因为偏见数据可能导致不准确诊断，以及数据隐私问题。在游戏产业中，AI 智能体将改变开发者角色，从脚本编写转向智能体学习流程优化，对社会经济产生影响。

此外，在模拟中学习协作策略存在领域迁移风险。需进行鲁棒测试和持续安全监控，以最小化真实世界中不可预测行为的风险。

### 8.2 多元化声明

本研究通过探索 AI 智能体模型在各个领域的适应性，拥抱挑战、视角和解决方案的多样性。我们致力于建立一个通过探索多模态和智能体 AI 的广泛主题而形成的多元化社区。

项目将涵盖：基础模型的应用、通用端到端系统、模态基础方法、直观人机界面以及驯服 LLM/VLM。我们希望通过独特的多元视角，拓宽对智能体 AI 潜力与局限的集体理解，并促进更具包容性的整体视角，以应对多模态 AI 智能体面临的广泛挑战。


<补充素材>
<用途>
在讲解伦理考量时，通过提出深度问题并提供详细回答，帮助观众超越表面内容，深入理解Agent AI发展中面临的潜在风险、社会影响以及如何负责任地应对这些挑战。
</用途>
<工具名>
deep_insight_qa
</工具名>
<工具输出>
{'tool_name': 'deep_insight_qa', 'type': 'deep_insight_qa', 'file_path': 'tool_enhance/deep_insight_qa.json', 'qa_data': {'summary': {'content_theme': 'RLSC微调模型在答案生成中的“简洁自信”涌现行为及其潜在风险', 'target_audience': '深度理解', 'insight_focus': '探讨RLSC模型行为的本质、价值与局限，特别是“模式锐化”的双面性'}, 'qa_pairs': [{'question': 'RLSC模型生成的“简洁自信”答案，是真正实现了“智慧的简化”，还是仅仅学会了“高效的猜谜”？这两种可能性在模型能力边界上会有何本质差异？', 'answer': '这好比一个学生，是真正理解了知识点并能简洁清晰地推理出答案（智慧的简化），还是仅仅通过记忆答案开头或关键词，直接给出看似正确的结论（高效的猜谜）。本质差异在于：前者代表了对问题核心的内化和路径优化，能泛化到新场景；后者是依赖于训练数据中已有的高置信度模式，一旦遇到陌生或需要跳出已知模式的问题，就会失效甚至误导。这决定了模型是“智者”还是“技巧大师”。'}, {'question': 'RLSC的“模式锐化”能力，在追求效率和避免冗余推理的同时，是否也可能无意中扼杀了模型在面对模棱两可或需发散性思考问题时的“探索性智慧”？', 'answer': '就像一个过度专业的医生，他能高效精准地诊断常见病，但当面对一种罕见或症状模糊的疑难杂症时，他可能会因为过于依赖“高置信度模式”而错过发散性思考或跳出常规路径探究的可能性。模型的“锐化”可能锁死了探索空间，让它只沿着最“明显”且“安全”的路径走，从而牺牲了发现“非显而易见”解决方案的潜力，这在创新性、艺术性或复杂决策场景中可能是致命的局限。'}, {'question': "如果说传统“Let's think step by step”是一种显式的“过程管理”，那么RLSC的涌现行为是否暗示了一种更为隐式的“结果导向型过程优化”？这种优化对未来AI模型的“思维”演进意味着什么？", 'answer': "“Let's think step by step”是教模型走路，每一步都得看；RLSC更像是教它骑自行车，过程中具体的转向、平衡是隐式的，最终目标是平稳快速地到达。这意味着AI可能不再需要显式指令去模仿人类的思考过程，而是通过结果反馈自发地优化其内部处理流程，让“思维”变得更加本能和高效。这可能预示着AI将发展出类似直觉或潜意识的“思考”模式，而非单纯的逻辑链条，但随之而来的是，理解其内部决策机制的难度也将大大增加。"}], 'overall_insights': ['RLSC的“简洁自信”行为，挑战了我们对AI“理解”与“优化”的传统认知：它可能并非通过显式推理优化，而是通过内化高置信度模式实现高效输出，这带来了效率提升，也引入了潜在的“模式固化”风险，值得深思其本质。', 'AI决策过程正在从“显式可追溯”向“隐式涌现”转变，这在提升效率的同时，也对我们理解和控制AI的能力提出了新挑战。未来的研究需要平衡效率与可解释性，深入剖析其内部机制，避免陷入“黑箱高效”的困境。']}, 'questions_count': 3, 'status': 'exists'}
</工具输出>
</补充素材>

## 总结：Agent AI 的未来之路

Agent AI 作为通用人工智能迈进的关键一步，正处于快速发展的阶段。它融合了 LLM/VLM 的强大能力，通过新的范式和学习机制，赋能智能体在游戏、机器人、医疗等多个领域进行感知、推理、决策和行动。虽然面临幻觉、偏见、数据隐私和 Sim-to-Real 等挑战，但持续的学习改进和新的数据集基准的建立，正在推动这一领域的突破。未来，Agent AI 将更紧密地与人类协同，在跨模态、跨领域和跨现实中展现出更强大的通用智能，最终重塑我们与数字世界的交互方式，并为社会和工业带来深远变革。