#!/usr/bin/env python
"""
DSL工具脚本 - 用于提取函数注释、生成文档和运行示例

此脚本可以：
1. 提取函数注释（单个函数或所有函数）并转成markdown格式
2. 将提取的示例转成完整DSL配置文件
3. 调用dsl_to_manim运行生成的配置进行功能测试
"""

import argparse
import importlib.util
import inspect
import json
import os
import subprocess
import sys
from pathlib import Path
from typing import Any, Optional

import yaml
from loguru import logger

# 添加项目根目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)


def get_animation_functions_path() -> Path:
    """获取动画函数目录的路径"""
    # 假设当前脚本在项目的scripts目录下
    base_dir = Path(__file__).parent.parent
    return base_dir / "dsl" / "v2" / "animation_functions"


def get_examples_path() -> Path:
    """获取示例输出目录的路径"""
    base_dir = Path(__file__).parent.parent
    return base_dir / "examples" / "generated"


def list_animation_functions() -> list[str]:
    """列出所有可用的动画函数模块"""
    animation_dir = get_animation_functions_path()
    return [py_file.stem for py_file in animation_dir.glob("*.py") if not py_file.stem.startswith("_")]


def load_module_doc(module_name: str) -> dict[str, dict]:
    """
    从模块的文件级别文档字符串中加载文档数据

    Args:
        module_name: 模块名，如 'animate_video'

    Returns:
        包含函数名和对应文档数据的字典
    """
    try:
        # 构建文件路径
        animation_dir = get_animation_functions_path()
        module_file = animation_dir / f"{module_name}.py"

        if not module_file.exists():
            logger.error(f"错误: 找不到模块文件: {module_file}")
            return {}

        # 读取文件内容以获取文件级别的文档字符串
        with open(module_file, encoding="utf-8") as f:
            content = f.read()

        # 提取文件级别的文档字符串
        docstring = ""
        if content.startswith('"""') or content.startswith("'''"):
            # 如果文件开头是文档字符串
            quote_type = content[:3]
            end_pos = content.find(quote_type, 3)
            if end_pos != -1:
                docstring = content[3:end_pos].strip()

        # 如果没有文件级别的文档字符串，尝试从主函数获取
        if not docstring:
            # 直接从文件加载模块
            spec = importlib.util.spec_from_file_location(module_name, module_file)
            if spec is None or spec.loader is None:
                logger.error(f"错误: 无法从文件加载模块规范: {module_file}")
                return {}

            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 尝试找到主函数并获取其文档字符串
            main_function_name = module_name
            if not hasattr(module, main_function_name):
                main_function_name = f"animate_{module_name.replace('animate_', '')}"

            if hasattr(module, main_function_name):
                main_function = getattr(module, main_function_name)
                docstring = inspect.getdoc(main_function) or ""
            else:
                # 如果没有找到主函数，尝试找到任何非私有函数
                for name, obj in inspect.getmembers(module, inspect.isfunction):
                    if not name.startswith("_") and not name.startswith("test_"):
                        docstring = inspect.getdoc(obj) or ""
                        if docstring:
                            break

        if not docstring:
            logger.warning(f"警告: 模块 {module_name} 没有找到有效的文档字符串")
            return {}

        try:
            # 解析YAML格式的文档
            doc_data = yaml.safe_load(docstring)
            if not isinstance(doc_data, dict):
                logger.warning(f"警告: 模块 {module_name} 的文档不是有效的YAML格式")
                return {}

            # 使用主函数名作为字典的键
            main_function_name = module_name
            if module_name.startswith("animate_"):
                # 如果模块名已经是 animate_xxx 格式，直接使用
                pass
            elif not module_name.startswith("animate_") and not any(n.startswith("animate_") for n in doc_data):
                # 如果模块名不是 animate_xxx 格式，并且文档中没有 animate_ 开头的键
                main_function_name = f"animate_{module_name}"

            return {main_function_name: doc_data}

        except yaml.YAMLError as e:
            logger.error(f"错误: 无法解析模块 {module_name} 的YAML文档: {e}")
            return {}

    except Exception as e:
        logger.error(f"错误: 处理模块时出错: {module_name}, {e}")
        return {}


def convert_to_markdown(function_name: str, doc_data: dict[str, Any]) -> str:
    """
    将函数文档数据转换为Markdown格式

    Args:
        function_name: 函数名
        doc_data: 函数文档数据

    Returns:
        Markdown格式的文档字符串
    """
    md = f"# {function_name}\n\n"

    # 添加效果描述
    if "effect" in doc_data:
        md += f"## 效果\n\n{doc_data['effect']}\n\n"

    # 添加使用场景
    if "use_cases" in doc_data and doc_data["use_cases"]:
        md += "## 使用场景\n\n"
        for use_case in doc_data["use_cases"]:
            md += f"- {use_case}\n"
        md += "\n"

    # 添加参数描述
    if "params" in doc_data and doc_data["params"]:
        md += "## 参数\n\n"
        md += "| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |\n"
        md += "| ------ | ---- | ---- | -------- | ------ |\n"

        for param_name, param_info in doc_data["params"].items():
            param_type = param_info.get("type", "")
            param_desc = param_info.get("desc", "")
            param_required = "是" if param_info.get("required", False) else "否"
            param_default = str(param_info.get("default", "")) if "default" in param_info else "-"

            md += f"| {param_name} | {param_type} | {param_desc} | {param_required} | {param_default} |\n"

        md += "\n"

    # 添加DSL示例
    if "dsl_examples" in doc_data and doc_data["dsl_examples"]:
        md += "## DSL示例\n\n"

        for i, example in enumerate(doc_data["dsl_examples"]):
            md += f"### 示例 {i+1}\n\n"
            md += "```json\n"
            md += json.dumps(example, ensure_ascii=False, indent=2)
            md += "\n```\n\n"

    # 添加注意事项
    if "notes" in doc_data and doc_data["notes"]:
        md += "## 注意事项\n\n"
        for note in doc_data["notes"]:
            md += f"- {note}\n"
        md += "\n"

    return md


def create_dsl_file(function_name: str, doc_data: dict[str, Any], output_dir: Optional[str] = None) -> Optional[str]:
    """
    从函数文档数据创建完整的DSL配置文件，将所有示例合并到一个文件中

    Args:
        function_name: 函数名
        doc_data: 函数文档数据
        output_dir: 输出目录，如果不提供则使用默认目录

    Returns:
        生成的DSL文件路径，如果生成失败则返回None
    """
    if "dsl_examples" not in doc_data or not doc_data["dsl_examples"]:
        logger.error(f"错误: 函数 {function_name} 的文档中没有DSL示例")
        return None

    examples = doc_data["dsl_examples"]
    if not examples:
        logger.error(f"错误: 函数 {function_name} 没有任何DSL示例")
        return None

    # 确定输出目录
    if output_dir:
        output_path = Path(output_dir)
    else:
        output_path = get_examples_path()

    output_path.mkdir(exist_ok=True, parents=True)

    # 创建输出文件路径，不再使用示例索引
    output_file = output_path / f"{function_name}_example.json"

    # 创建完整的DSL JSON结构
    actions = []

    # 将所有示例添加到actions列表中
    for example in examples:
        actions.append({"type": example.get("type", function_name), "params": example.get("params", {})})

    dsl_json = {
        "schema_version": "2.0-mvp",
        "metadata": {
            "title": f"{function_name} 示例",
            "author": "自动生成",
            "background_color": "BLACK",
        },
        "actions": actions,
    }

    # 保存为JSON文件
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(dsl_json, f, ensure_ascii=False, indent=4)

        logger.info(f"已生成包含所有示例的DSL文件: {output_file}")
        return str(output_file)

    except Exception as e:
        logger.error(f"错误: 无法创建DSL文件: {e}")
        return None


def run_dsl_file(dsl_file_path: str, quality: str = "l") -> bool:
    """
    运行DSL文件

    Args:
        dsl_file_path: DSL文件路径

    Returns:
        运行是否成功
    """
    try:
        cmd = ["uv", "run", "python", "-m", "dsl.v2.dsl_to_manim", dsl_file_path, "-r", "-q", quality]
        logger.info(f"运行命令: {' '.join(cmd)}")

        process = subprocess.run(cmd, capture_output=True, text=True)

        # 输出命令的标准输出和错误
        if process.stdout:
            logger.info("标准输出:")
            logger.info(process.stdout)

        if process.stderr:
            logger.error("标准错误:")
            logger.error(process.stderr)

        return process.returncode == 0

    except Exception as e:
        logger.error(f"错误: 运行DSL文件时出错: {e}")
        return False


def extract_all_to_markdown(output_dir: Optional[str] = None) -> None:
    """提取所有函数的文档并生成单个合并的Markdown文件"""
    modules = list_animation_functions()

    if not output_dir:
        output_dir = str(Path.cwd() / "docs")

    os.makedirs(output_dir, exist_ok=True)

    # 创建目录部分
    all_md_content = "# 动画函数文档\n\n"
    all_md_content += "## 目录\n\n"

    # 收集所有函数的markdown内容
    function_docs = []

    for module_name in modules:
        docs = load_module_doc(module_name)

        for func_name, doc_data in docs.items():
            # 添加到目录，使用锚点链接
            all_md_content += f"- [{func_name}](#{func_name.lower()})\n"

            # 存储函数文档内容
            function_docs.append({"name": func_name, "content": convert_to_markdown(func_name, doc_data)})

    # 添加空行分隔目录和内容
    all_md_content += "\n---\n\n"

    # 按函数名称排序（可选）
    function_docs.sort(key=lambda x: x["name"])

    # 添加所有函数文档到同一个文件
    for func_doc in function_docs:
        all_md_content += func_doc["content"] + "\n---\n\n"

    # 创建单个合并的markdown文件
    md_file = Path(output_dir) / "animation_functions.md"
    with open(md_file, "w", encoding="utf-8") as f:
        f.write(all_md_content)

    logger.info(f"已生成合并的Markdown文档: {md_file}")


def process_function(
    module_name: str,
    function_name: Optional[str] = None,
    output_dir: Optional[str] = None,
    generate_md: bool = True,
    generate_dsl: bool = True,
    run_example: bool = False,
    quality: str = "l",
) -> None:
    """
    处理指定模块，可选地生成Markdown、DSL文件并运行示例

    Args:
        module_name: 模块名称，如 'animate_video'
        function_name: 当使用文件级文档时此参数已废弃，保留是为了兼容性
        output_dir: 输出目录
        generate_md: 是否生成Markdown文档
        generate_dsl: 是否生成DSL配置文件
        run_example: 是否运行示例
        quality: DSL示例的渲染质量
    """
    # 加载模块文档
    docs = load_module_doc(module_name)

    if not docs:
        logger.error("未找到模块文档")
        return

    # 如果提供了函数名，过滤文档
    if function_name and function_name in docs:
        docs = {function_name: docs[function_name]}
    elif function_name:
        # 检查是否有匹配的函数名（人性化处理）
        matched_keys = [k for k in docs.keys() if function_name in k]
        if matched_keys:
            # 使用第一个匹配的函数
            docs = {matched_keys[0]: docs[matched_keys[0]]}
            logger.info(f"使用匹配的函数: {matched_keys[0]}")
        else:
            logger.warning(f"警告: 未找到函数 {function_name}，使用全部模块文档")

    for func_name, doc_data in docs.items():
        # 生成Markdown文档
        if generate_md:
            md_content = convert_to_markdown(func_name, doc_data)

            md_dir = output_dir if output_dir else str(Path.cwd() / "docs")
            os.makedirs(md_dir, exist_ok=True)

            md_file = Path(md_dir) / f"{func_name}.md"
            with open(md_file, "w", encoding="utf-8") as f:
                f.write(md_content)

            logger.info(f"已生成Markdown文档: {md_file}")

        # 生成DSL文件
        if generate_dsl:
            dsl_file = create_dsl_file(func_name, doc_data, output_dir)

            # 运行示例
            if run_example and dsl_file:
                logger.info(f"运行 DSL示例: {dsl_file}")
                success = run_dsl_file(dsl_file, quality)
                if success:
                    logger.info("DSL示例运行成功")
                else:
                    logger.error("DSL示例运行失败")


def main():
    """主函数，处理命令行参数并执行相应操作"""
    parser = argparse.ArgumentParser(description="DSL工具 - 提取函数注释，生成文档和DSL配置文件")

    # 主要命令选项
    parser.add_argument("--list", "-l", action="store_true", help="列出所有可用的动画函数")
    parser.add_argument("--module", "-m", help="要处理的模块名称，如 animate_video")
    parser.add_argument("--function", "-f", help="要处理的函数名称，如果不提供则处理模块中的所有函数")

    # 输出控制
    parser.add_argument("--output-dir", "-o", help="输出目录路径")

    # 行为控制
    parser.add_argument("--no-markdown", action="store_true", help="不生成Markdown文档")
    parser.add_argument("--no-dsl", action="store_true", help="不生成DSL配置文件")
    parser.add_argument("--run", "-r", action="store_true", help="生成并运行DSL示例")
    parser.add_argument("--quality", "-q", default="l", help="将所有示例合并到一个DSL文件中")

    args = parser.parse_args()

    # 提取所有函数的文档
    extract_all_to_markdown(args.output_dir)

    # 列出所有函数
    if args.list:
        logger.info("可用的动画函数:")
        for func in list_animation_functions():
            logger.info(f"  - {func}")
        return

    # 处理指定模块
    if args.module:
        process_function(
            module_name=args.module,
            function_name=args.function,
            output_dir=args.output_dir,
            generate_md=not args.no_markdown,
            generate_dsl=not args.no_dsl,
            run_example=args.run,
            quality=args.quality,
        )
        return

    # 如果没有指定任何操作，显示帮助
    parser.print_help()


if __name__ == "__main__":
    main()
