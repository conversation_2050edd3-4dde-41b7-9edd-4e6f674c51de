"""
Title utilities for creating standardized titles in Manim animations.
"""

from manim import *
from dsl.v2.themes.theme_utils import ThemeUtils


def create_title(title: str, scene):
    """
    创建标准化的标题文本对象，执行优化的动效序列
    
    Args:
        title: 标题文本内容
        scene: Man<PERSON>场景对象（必须提供）
        
    Returns:
        tuple: (initial_title, final_title) 
               - initial_title: 初始状态的标题（在画面中央）
               - final_title: 最终状态的标题（移动后的位置）
    """
    # 创建初始状态的标题副本（用于转场保存）
    initial_title = Text(
        title,
        font="PingFang SC",
        color=WHITE,
        font_size=ThemeUtils.get_font_size("h1"),
        weight=BOLD,
    )
    initial_title.move_to(ORIGIN)  # 保持在画面中央
    
    # 创建标题文本对象用于动画
    title_text = Text(
        title,
        font="PingFang SC",  # 使用苹果系统的中文字体
        color=WHITE,
        font_size=ThemeUtils.get_font_size("h1"),
        weight=BOLD,  # 加粗字体
    )
    
    # 执行动画序列 - 使用固定参数
    # 第一步：从画面中心出现
    title_text.move_to(ORIGIN)
    scene.play(FadeIn(title_text), run_time=0.5)
    
    # 第二步：缓慢放大
    scene.play(
        title_text.animate.scale(1.5),
        run_time=1.0
    )
    
    # 第三步：移动到顶部1/5处并缩小回原始大小
    # 计算顶部1/5处的位置：frame_height的2/5处（因为ORIGIN在中心，向上移动2/5就是顶部1/5）
    top_position = UP * (scene.frame_height * 2/5)
    scene.play(
        title_text.animate.scale(1/1.5).move_to(top_position),
        run_time=0.8
    )
    
    # 返回初始状态和最终状态
    return initial_title, title_text

