from camel.models import BaseModelBackend, ModelFactory
from camel.types import ModelPlatformType
from loguru import logger


def create_model(config_file="config/config.yaml", model_type=None) -> BaseModelBackend:
    """根据配置创建模型实例"""
    import yaml

    with open(config_file) as f:
        config = yaml.safe_load(f)
    platform = config["model"].get("platform", "openrouter")
    model_type_in_config = config["model"].get("type", "google/gemini-2.5-flash-preview")
    if model_type is None:
        model_type = model_type_in_config
    logger.info(f"Creating model {model_type} on {platform}")
    return ModelFactory.create(
        model_platform=ModelPlatformType.from_name(platform),
        model_type=model_type,
        api_key=config["model"]["api"].get("openrouter_api_key"),
    )
