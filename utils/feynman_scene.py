import re
from math import ceil

import wcwidth
from manim_voiceover import VoiceoverScene


def add_wrapped_subcaption(
    self,
    subcaption: str,
    duration: float,
    subcaption_buff: float = 0.1,
    max_subcaption_len: int = 70,
) -> None:
    """
    使用 wcwidth 结合正则表达式对文本进行分词，确保不会在单词内部或标点前拆分，
    同时根据各段显示宽度按比例分配语音时长，从而保证语音与字幕对齐。
    """
    # 清理多余空格
    subcaption = " ".join(subcaption.split())
    total_width = wcwidth.wcswidth(subcaption)
    n_chunks = ceil(total_width / max_subcaption_len)
    target_width = total_width / n_chunks

    # 正则将文本拆分为：CJK单字符、英文单词、标点符号和空白（保留空格）
    token_pattern = r"[\u4e00-\u9fff]|[A-Za-z0-9]+|[^\w\s\u4e00-\u9fff]+|\s+"
    tokens = re.findall(token_pattern, subcaption)

    chunks_ = []
    current_chunk = ""
    current_width = 0

    for token in tokens:
        token_width = wcwidth.wcswidth(token)
        # 当累计宽度超出目标宽度，并且当前已有内容时分割
        if current_chunk and (current_width + token_width > target_width):
            # 避免在标点前拆分：若 token 非空且首字符非字母数字（例如标点）
            if token.strip() and not token[0].isalnum():
                current_chunk += token
                current_width += token_width
                chunks_.append(current_chunk)
                current_chunk = ""
                current_width = 0
            else:
                chunks_.append(current_chunk)
                current_chunk = token
                current_width = token_width
        else:
            current_chunk += token
            current_width += token_width

    if current_chunk:
        chunks_.append(current_chunk)

    # 如果最后一段宽度太小，则与前一段合并（允许超过max_subcaption_len）
    if len(chunks_) > 1 and wcwidth.wcswidth(chunks_[-1]) < (target_width / 4):
        chunks_[-2] += chunks_[-1]
        chunks_.pop()

    # 根据每一段的实际宽度分配时长比例
    actual_total = sum(wcwidth.wcswidth(chunk) for chunk in chunks_)
    subcaption_weights = [wcwidth.wcswidth(chunk) / actual_total for chunk in chunks_]

    current_offset = 0
    for idx, caption in enumerate(chunks_):
        chunk_duration = duration * subcaption_weights[idx]
        self.add_subcaption(
            caption,
            duration=max(chunk_duration - subcaption_buff, 0),
            offset=current_offset,
        )
        current_offset += chunk_duration


VoiceoverScene.add_wrapped_subcaption = add_wrapped_subcaption


class FeynmanScene(VoiceoverScene):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
