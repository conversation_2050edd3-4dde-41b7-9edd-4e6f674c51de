import os
import json
import logging
from typing import List, Dict, Any, Optional, Union
import re

# 这里应该导入实际使用的向量存储和嵌入模型库
# 以下是示例导入
try:
    import chromadb
    from chromadb.utils import embedding_functions
except ImportError:
    print("ChromaDB未安装，请使用pip install chromadb安装")

logger = logging.getLogger(__name__)

class RAGToolkit:
    """检索增强生成工具包"""
    
    def __init__(
        self, 
        chroma_db_path: str = "data/rag/chroma_db",
        manim_docs_path: str = "data/rag/manim_docs",
        embedding_model: str = "text-embedding-3-large",
        session_id: str = None,
        use_langfuse: bool = False
    ):
        """
        初始化RAG工具包
        
        Args:
            chroma_db_path: ChromaDB存储路径
            manim_docs_path: Manim文档路径
            embedding_model: 嵌入模型名称
            session_id: 会话ID
            use_langfuse: 是否使用Langfuse日志
        """
        self.chroma_db_path = chroma_db_path
        self.manim_docs_path = manim_docs_path
        self.embedding_model = embedding_model
        self.session_id = session_id
        self.use_langfuse = use_langfuse
        
        # 初始化向量存储
        self._initialize_vector_store()
        
        # 加载插件信息
        self.plugins_info = self._load_plugins_info()
    
    def _initialize_vector_store(self):
        """初始化向量存储"""
        try:
            # 创建ChromaDB客户端
            self.client = chromadb.PersistentClient(path=self.chroma_db_path)
            
            # 初始化嵌入函数
            self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name="all-MiniLM-L6-v2"  # 实际应用中应该使用指定的embedding_model
            )
            
            # 获取或创建集合
            self.collection = self.client.get_or_create_collection(
                name="manim_docs",
                embedding_function=self.embedding_function
            )
            
            logger.info(f"向量存储初始化成功: {self.chroma_db_path}")
        except Exception as e:
            logger.error(f"向量存储初始化失败: {str(e)}")
            # 创建一个空实现，以便代码可以继续运行
            self.client = None
            self.collection = None
    
    def _load_plugins_info(self) -> Dict[str, Dict[str, Any]]:
        """
        加载Manim插件信息
        
        Returns:
            Dict: 插件信息字典
        """
        # 插件信息文件路径
        plugins_file = os.path.join(os.path.dirname(self.manim_docs_path), "plugins_info.json")
        
        # 默认插件信息
        default_plugins = {
            "manim_circuit": {
                "description": "电路图绘制插件",
                "keywords": ["电路", "电子", "电压", "电流", "开关", "电阻", "电容"]
            },
            "manim_physics": {
                "description": "物理模拟插件",
                "keywords": ["物理", "力学", "重力", "弹性", "碰撞", "流体", "轨迹"]
            },
            "manim_chemistry": {
                "description": "化学分子结构绘制插件",
                "keywords": ["化学", "分子", "原子", "反应", "键", "结构"]
            },
            "manim_dsa": {
                "description": "数据结构和算法可视化插件",
                "keywords": ["算法", "数据结构", "排序", "搜索", "图", "树", "链表"]
            },
            "manim_ml": {
                "description": "机器学习可视化插件",
                "keywords": ["机器学习", "深度学习", "神经网络", "优化", "激活函数", "权重"]
            }
        }
        
        try:
            if os.path.exists(plugins_file):
                with open(plugins_file, 'r') as f:
                    return json.load(f)
            return default_plugins
        except Exception as e:
            logger.error(f"加载插件信息失败: {str(e)}")
            return default_plugins
    
    def search_relevant_context(self, query: str, limit: int = 5) -> str:
        """
        搜索相关上下文
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            
        Returns:
            str: 格式化的上下文文本
        """
        if not self.collection:
            return "RAG未正确初始化，无法执行搜索"
        
        try:
            # 执行查询
            results = self.collection.query(
                query_texts=[query],
                n_results=limit
            )
            
            # 格式化结果
            if results["documents"] and results["documents"][0]:
                formatted_results = "相关上下文：\n\n"
                for i, doc in enumerate(results["documents"][0], 1):
                    formatted_results += f"{i}. {doc}\n\n"
                return formatted_results
            
            return "未找到相关上下文"
        except Exception as e:
            logger.error(f"搜索相关上下文失败: {str(e)}")
            return f"搜索过程中出错: {str(e)}"
    
    def detect_relevant_plugins(self, topic: str, description: str) -> List[str]:
        """
        检测相关插件
        
        Args:
            topic: 主题
            description: 描述
            
        Returns:
            List[str]: 相关插件列表
        """
        combined_text = f"{topic} {description}".lower()
        relevant_plugins = []
        
        for plugin_name, plugin_info in self.plugins_info.items():
            keywords = plugin_info.get("keywords", [])
            for keyword in keywords:
                if keyword.lower() in combined_text:
                    relevant_plugins.append(plugin_name)
                    break
        
        logger.info(f"检测到的相关插件: {relevant_plugins}")
        return relevant_plugins
    
    def search_with_queries(self, queries: List[str], limit_per_query: int = 3) -> str:
        """
        使用多个查询搜索上下文
        
        Args:
            queries: 查询列表
            limit_per_query: 每个查询的结果数量限制
            
        Returns:
            str: 格式化的上下文文本
        """
        if not queries:
            return "未提供查询"
        
        # 去重并限制查询数量
        unique_queries = list(set(queries))[:5]  # 最多5个查询
        
        all_results = []
        for query in unique_queries:
            # 执行单个查询
            context = self.search_relevant_context(query, limit=limit_per_query)
            if "未找到相关上下文" not in context and "RAG未正确初始化" not in context:
                all_results.append(f"查询: {query}\n{context}")
        
        if all_results:
            return "\n".join(all_results)
        return "未找到相关上下文"
    
    def generate_queries_for_vision_storyboard(
        self, 
        scene_plan: str, 
        relevant_plugins: List[str],
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为视觉故事板生成查询
        
        Args:
            scene_plan: 场景计划
            relevant_plugins: 相关插件列表
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 从场景计划中提取关键词
        keywords = self._extract_keywords(scene_plan)
        
        # 生成查询
        queries = [
            f"Manim实现 {topic} 可视化",
            f"Manim如何创建{keywords[:3] if len(keywords) > 3 else keywords}的动画"
        ]
        
        # 添加插件相关查询
        for plugin in relevant_plugins:
            queries.append(f"如何使用{plugin}插件在Manim中创建{topic}可视化")
        
        # 去重
        return list(set(queries))
    
    def generate_queries_for_technical(
        self, 
        storyboard: str, 
        relevant_plugins: List[str],
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为技术实现生成查询
        
        Args:
            storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 从故事板中提取关键词
        keywords = self._extract_keywords(storyboard)
        
        # 提取可能的Manim对象
        manim_objects = self._extract_manim_objects(storyboard)
        
        # 生成查询
        queries = [
            f"Manim技术实现 {topic} 动画",
            f"Manim如何创建{keywords[:3] if len(keywords) > 3 else keywords}的技术实现"
        ]
        
        # 添加对象相关查询
        for obj in manim_objects[:3]:  # 最多3个对象
            queries.append(f"Manim中{obj}对象的属性和方法")
        
        # 添加插件相关查询
        for plugin in relevant_plugins:
            queries.append(f"{plugin}插件的核心类和方法")
        
        # 去重
        return list(set(queries))
    
    def generate_queries_for_narration(
        self, 
        storyboard: str, 
        relevant_plugins: List[str],
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为动画叙述生成查询
        
        Args:
            storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 从故事板中提取关键词
        keywords = self._extract_keywords(storyboard)
        
        # 生成查询
        queries = [
            f"Manim VoiceoverScene使用方法",
            f"{topic} 教学讲解关键点",
            f"如何解释{keywords[:3] if len(keywords) > 3 else keywords}概念"
        ]
        
        # 去重
        return list(set(queries))
    
    def generate_queries_for_code(
        self, 
        implementation_plan: str, 
        relevant_plugins: List[str],
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为代码生成生成查询
        
        Args:
            implementation_plan: 技术实现计划
            relevant_plugins: 相关插件列表
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 提取可能的Manim对象
        manim_objects = self._extract_manim_objects(implementation_plan)
        
        # 提取可能的动画方法
        animation_methods = self._extract_animation_methods(implementation_plan)
        
        # 生成查询
        queries = [
            f"Manim代码实现 {topic} 场景{scene_number}",
            "Manim VoiceoverScene同步动画和语音"
        ]
        
        # 添加对象相关查询
        for obj in manim_objects[:3]:  # 最多3个对象
            queries.append(f"Manim {obj} 类的完整代码示例")
        
        # 添加动画方法相关查询
        for method in animation_methods[:3]:  # 最多3个方法
            queries.append(f"Manim {method} 方法参数和使用示例")
        
        # 添加插件相关查询
        for plugin in relevant_plugins:
            queries.append(f"{plugin} 插件的代码使用示例")
        
        # 去重
        return list(set(queries))
    
    def generate_queries_for_fix_error(
        self, 
        error: str, 
        code: str,
        topic: str = None,
        scene_number: int = None,
        session_id: str = None
    ) -> List[str]:
        """
        为错误修复生成查询
        
        Args:
            error: 错误信息
            code: 代码
            topic: 主题
            scene_number: 场景编号
            session_id: 会话ID
            
        Returns:
            List[str]: 查询列表
        """
        # 提取错误关键词
        error_keywords = self._extract_keywords(error)
        
        # 从错误中提取可能的类名和方法名
        error_classes = re.findall(r'[A-Z][a-zA-Z]+', error)
        error_methods = re.findall(r'[a-z][a-zA-Z_]+\(', error)
        error_methods = [m[:-1] for m in error_methods]  # 移除括号
        
        # 生成查询
        queries = [
            f"Manim错误: {' '.join(error_keywords[:5])}"
        ]
        
        # 添加类和方法相关查询
        for cls in error_classes[:2]:
            queries.append(f"Manim {cls} 类常见错误")
        
        for method in error_methods[:2]:
            queries.append(f"Manim {method} 方法参数和使用示例")
        
        # 去重
        return list(set(queries))
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 关键词列表
        """
        # 简单实现，实际应用中应该使用更复杂的关键词提取算法
        words = re.findall(r'\b[a-zA-Z\u4e00-\u9fa5]{2,}\b', text)
        # 过滤常见停用词
        stopwords = {"的", "地", "得", "和", "与", "或", "the", "and", "in", "on", "of", "for", "to", "a", "an"}
        keywords = [word for word in words if word.lower() not in stopwords]
        # 返回出现频率最高的前10个关键词
        from collections import Counter
        return [word for word, _ in Counter(keywords).most_common(10)]
    
    def _extract_manim_objects(self, text: str) -> List[str]:
        """
        从文本中提取可能的Manim对象
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: Manim对象列表
        """
        # Manim常见对象列表
        common_objects = {
            "Circle", "Square", "Rectangle", "Triangle", "Polygon", "Line", "Arrow", 
            "Text", "Tex", "MathTex", "VGroup", "Graph", "Axes", "NumberPlane", 
            "CoordinateSystem", "Dot", "Point", "SVGMobject", "ImageMobject", "Scene",
            "VoiceoverScene", "MovingCameraScene", "ThreeDScene"
        }
        
        # 查找文本中提到的Manim对象
        found_objects = []
        for obj in common_objects:
            if obj in text:
                found_objects.append(obj)
        
        return found_objects
    
    def _extract_animation_methods(self, text: str) -> List[str]:
        """
        从文本中提取可能的动画方法
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 动画方法列表
        """
        # Manim常见动画方法
        common_animations = {
            "Write", "Create", "FadeIn", "FadeOut", "Transform", "ReplacementTransform",
            "MoveAlongPath", "Rotate", "Scale", "GrowFromCenter", "DrawBorderThenFill",
            "Indicate", "Flash", "AnimationGroup", "Succession", "LaggedStart"
        }
        
        # 查找文本中提到的动画方法
        found_animations = []
        for anim in common_animations:
            if anim in text:
                found_animations.append(anim)
        
        return found_animations 