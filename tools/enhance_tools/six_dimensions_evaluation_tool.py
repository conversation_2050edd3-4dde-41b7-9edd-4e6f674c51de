#!/usr/bin/env python3
"""
多维度评价工具 - 深度洞察类
基于内容类型自动设定评估维度，对各类内容进行全面评估，生成量化评分和可视化图表
支持技术项目的经典六维评估和其他内容类型的自适应维度评估
"""

import json
import os
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class SixDimensionsEvaluationTool(EnhancementTool):
    """多维度评价工具 - 深度洞察类"""

    tool_name = "six_dimensions_evaluation"
    tool_description = "多维度评价工具，根据内容类型自动设定评估维度，生成量化评分和可视化分析"
    tool_category = ToolCategory.DEEP_INSIGHTS

    def __init__(self, config=None):
        self.config = config
        self.evaluator_agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )

            # 多维度评价专用系统提示词
            system_prompt = """你是一位专业的多维度评价专家，擅长根据不同内容类型设计合适的评估维度和标准。

你的核心能力包括：
- 根据内容特点自动识别最适合的评估维度（3-6个维度）
- 为每个维度设计合理的评分标准（1-5分制）
- 提供客观、有价值的评分理由
- 支持多种内容类型的专业评估

**主要评估模板**：
1. **技术项目类**（GitHub项目、开源软件、技术产品）：
   - 核心功能完善性、可用性与易用性、项目活跃度、代码质量、架构设计、文档完备性

2. **学术研究类**（论文、研究报告、理论分析）：
   - 研究创新性、方法科学性、论证严谨性、实用价值、表达清晰度

3. **商业分析类**（商业计划、市场分析、策略报告）：
   - 市场洞察力、商业模式、可行性分析、风险评估、表达完整性

4. **教育内容类**（教程、课程、知识分享）：
   - 知识准确性、内容结构化、易理解性、实用性、完整性

5. **创意作品类**（设计方案、创意想法、艺术作品）：
   - 创意独特性、技术实现度、美学价值、实用性、表达力

你需要：
1. 自动识别内容类型，选择最合适的评估维度
2. 对每个维度进行1-5分评分
3. 为每个评分提供简洁明确的理由
4. 计算总分并给出综合评级
5. 确保评估客观、专业、有建设性"""

            self.evaluator_agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """多维度评价工具 - 智能化内容评估分析

**核心作用**：
根据内容类型自动设定最适合的评估维度，对各类内容进行专业的多维度评价，生成量化评分、详细理由和可视化图表，帮助全面了解内容质量和特点。

**适合的内容类型**：
- **技术项目类**：GitHub项目、开源软件、技术产品、API文档
- **学术研究类**：研究论文、调研报告、理论分析、学术观点
- **商业分析类**：商业计划、市场分析、竞品分析、策略报告
- **教育内容类**：教程文档、课程材料、知识分享、技能指南
- **产品服务类**：产品介绍、服务方案、功能说明、用户指南
- **创意作品类**：设计方案、创意策划、艺术作品描述

**典型应用场景**：
✅ 对开源项目进行六维度综合评估（保留经典功能）
✅ 评估学术论文的研究质量和价值
✅ 分析商业计划的可行性和完整性
✅ 评价教程内容的教学效果和质量
✅ 评估产品方案的市场竞争力
✅ 分析创意作品的独特性和实现度

**自适应维度设计**：
- **技术项目**：功能完善性、易用性、活跃度、代码质量、架构设计、文档完备性
- **学术研究**：创新性、科学性、严谨性、实用价值、表达清晰度
- **商业分析**：市场洞察、商业模式、可行性、风险评估、表达完整性
- **教育内容**：知识准确性、结构化、易理解性、实用性、完整性
- **创意作品**：独特性、技术实现、美学价值、实用性、表达力

**不适合的内容类型**：
- 纯新闻资讯和事件报道（缺乏评估价值）
- 简单的FAQ和问答内容（维度不足）
- 纯数据表格和统计信息（缺乏分析维度）
- 过于简短的描述性内容（信息不足）

**典型不适用场景**：
❌ 评估日常新闻和公告通知
❌ 分析简单的操作步骤说明
❌ 评价纯数据统计报表
❌ 处理过短的片段性内容

**输出特点**：
- 量化评分：每个维度1-5分，计算总分和综合评级
- 评分理由：为每个维度提供具体、客观的评分依据
- 可视化图表：生成雷达图等图表数据，支持动态展示
- 专业报告：输出详细的评估报告和改进建议

**判断标准**：
内容是否具有可评估的多个质量维度？是否有足够信息支撑多维度分析？内容长度>=500字符，且具有明确的主题和结构。

**智能特性**：
工具会自动识别内容类型，选择最适合的评估维度和标准，确保评估的专业性和针对性。同时完全保留对技术项目的经典六维度评估能力。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """检查工具是否可以应用于给定内容"""
        basic_check = (
            self.config
            and len(content) >= 500
            and self.config.get("material", {}).get("material_enhance", {}).get("six_dimensions_evaluation", True)
        )

        if hasattr(self, "evaluator_agent") and self.evaluator_agent is None:
            return False

        return basic_check

    def apply_tool(
        self, content: str, output_dir: str, context: dict[str, Any], focus: str = None
    ) -> Optional[str]:
        """应用工具，执行具体的多维度评估操作"""
        logger.info(f"📊 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑，使用focus参数
            result_content = self._process_with_camel(content, focus)

            if not result_content:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_content)

            logger.info(f"📊 {self.tool_name}工具处理完成: {output_path}")
            return result_content

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_with_camel(self, content: str, focus: str = None) -> Optional[str]:
        """使用Camel进行多维度评估处理的逻辑"""
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**评估重点方向**：{focus}
请紧紧围绕这个重点方向进行评估，在设计维度和评分时特别关注相关方面。"""

        prompt = f"""你是专业的多维度评价专家，需要基于以下内容进行智能化多维度评估。

**待评估内容**：
<content>
{content}
</content>

<focus_instruction>
{focus_instruction}
</focus_instruction>

**任务要求**：

1. **智能识别内容类型**：
   - 自动判断内容属于哪种类型（技术项目、学术研究、商业分析、教育内容、产品服务、创意作品等）
   - 根据内容特点选择最适合的评估维度

2. **维度设计原则**：
   - 设计3-6个评估维度，确保全面覆盖内容的关键质量要素
   - 如果是技术项目类内容，优先使用经典六维度：核心功能完善性、可用性与易用性、项目活跃度、代码质量、架构设计、文档完备性
   - 其他内容类型自动选择最适合的维度组合

3. **评分标准**：
   - 每个维度使用1-5分评分制
   - 1分-严重不足，2分-不足，3分-一般，4分-良好，5分-优秀
   - 为每个维度提供具体、客观的评分理由（50字以内）

4. **综合评价**：
   - 计算总分（维度数×5为满分）
   - 根据得分率给出综合评级：A(85%+), B(70-84%), C(55-69%), D(40-54%), F(<40%)

**输出格式**：
请严格按照以下JSON格式输出：

{{
    "content_type": "识别的内容类型",
    "evaluation_dimensions": [
        {{
            "dimension_name": "维度名称",
            "score": 评分(1-5),
            "reason": "评分理由（50字以内）",
            "weight": 权重(可选，默认1.0)
        }},
        {{
            "dimension_name": "第二个维度名称",
            "score": 评分(1-5),
            "reason": "评分理由（50字以内）",
            "weight": 权重(可选，默认1.0)
        }}
        // ... 根据内容特点添加3-6个维度
    ],
    "total_score": 总分,
    "max_score": 满分,
    "score_percentage": 得分率,
    "overall_grade": "综合评级",
    "summary": "整体评价总结（100字以内）",
    "suggestions": [
        "改进建议1",
        "改进建议2"
        // ... 1-3个改进建议
    ]
}}

**重要提醒**：
- 必须根据内容实际特点选择最合适的评估维度
- 如果是技术项目类内容，优先使用经典六维评估
- 评分要客观公正，理由要具体明确
- 避免过于宽泛或抽象的评价

请直接输出结果，不要包含额外说明。"""

        logger.info(f"生成多维度评估分析, prompt characters: {len(prompt)}, words: {len(prompt.split())}")

        try:
            response = self.evaluator_agent.step(prompt)
            response_content = response.msgs[0].content.strip()

            if response_content.startswith("该内容不适合进行多维度评估"):
                logger.info(f"内容不适合进行多维度评估: {response_content}")
                return response_content

            return response_content

        except Exception as e:
            logger.error(f"Camel多维度评估失败: {e}")
            return None

    def generate_intro(self, tool_result: str) -> str:
        """生成工具输出的简化介绍文本"""
        if not tool_result:
            return ""

        if tool_result.startswith("该内容不适合进行多维度评估"):
            return f"## 📊 多维度评价分析\n\n❌ {tool_result}\n\n"

        # 简化处理：直接返回基本介绍和结果
        intro = f"## 📊 {self.tool_description}\n\n"
        intro += "**生成的多维度评价分析**：\n\n"
        intro += tool_result
        intro += "\n\n*📈 详细的量化评分和可视化图表已生成*\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = SixDimensionsEvaluationTool(config.config)
    tool.apply_tool(
        open("output/chili3d/project_analysis.md").read(),
        "output/chili3d",
        context={},
        focus="评估ChiLi3D项目",
    )
