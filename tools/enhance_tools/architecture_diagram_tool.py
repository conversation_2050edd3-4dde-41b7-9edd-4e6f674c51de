#!/usr/bin/env python3
"""
架构图生成工具 - 多模态呈现类
生成架构描述，用于后续动画渲染
"""

import os
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class ArchitectureDiagramTool(EnhancementTool):
    """架构图生成工具 - 多模态呈现类"""

    tool_name = "architecture_diagram"
    tool_description = "基于LLM生成架构图描述，用于后续动画渲染"
    tool_category = ToolCategory.MULTIMODAL_PRESENTATION

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """
        初始化 Camel LLM agent（结构化架构信息）
        """
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )
            system_prompt = """
你是一名专业的系统架构分析专家，负责从内容中提取系统架构的层次结构、组件、模块、交互关系等关键信息。确保结构清晰，逻辑性强。
"""
            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name} LLM模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name} LLM模型初始化失败: {e}")

    def _generate_with_llm(self, content: str, focus: str = None) -> Optional[str]:
        focus_instruction = ""
        if focus:
            focus_instruction = (
                f"\n**重点关注方向**：{focus}\n请在生成架构图时特别关注这个方向，确保相关内容在结构中充分体现。"
            )
        prompt = f"""
你是一名专业的系统架构师，需要基于以下内容生成架构设计。
<content>
{content}
</content>
<focus_instruction>
{focus_instruction}
</focus_instruction>
**任务要求**：
1. 根据重点关注方向，从输入内容中提取相关内容，生成架构设计。
2. **架构设计**：
   - 识别系统的核心模块或组件
   - 识别模块或组件之间的关系，如依赖关系、并列关系、独立模块等
3. **输出格式**：
   直接输出对架构的简明描述，包括架构图的标题、主要组件名称和主要关系。

**输出示例**：
架构图标题：智慧医疗诊断平台简要架构
架构图描述：
## 主要模块
- 用户管理：负责注册、登录和权限控制
- 诊断引擎：分析症状并生成诊断建议
- 医生工作台：支持病例管理和医嘱处理
- 检查集成：对接外部检验系统
- 数据存储：集中管理医疗数据
- 消息通知：推送消息与提醒

## 组件关系
- 用户管理为其他模块提供认证
- 医生工作台依赖诊断引擎和检查集成
- 诊断引擎和检查集成均访问数据存储
- 消息通知由业务模块触发推送
"""
        logger.info(f"生成架构图，prompt长度: {len(prompt)}, words: {len(prompt.split())}")
        try:
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content.strip()
            return response_content
        except Exception as e:
            logger.error(f"Camel结构化架构提取失败: {e}")
            return None

    def _init_toolkit(self):
        """初始化ExcalidrawToolkit"""
        try:
            from tools.excalidraw_toolkit import ExcalidrawToolkit

            self.toolkit = ExcalidrawToolkit()
            logger.info(f"{self.tool_name}工具包初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}工具包初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """架构图生成工具 - 将复杂的系统架构和流程可视化

**核心作用**：
架构图工具通过可视化方式展示系统组件、模块、层次关系以及它们之间的交互和数据流，帮助观众理解复杂系统的设计和运作方式。

**适合的内容类型**：
- 系统架构描述 (如微服务架构、三层架构)
- 技术方案说明 (如特定技术的实现方案)
- 业务流程图 (如用户注册流程、订单处理流程)
- 数据流图 (如数据在系统中的流转路径)
- 组件关系说明 (如模块间的依赖、接口调用)

**典型应用场景**：
✅ 展示一个电商平台的后端服务架构
✅ 说明一个AI模型训练和推理的完整流程
✅ 图解一个APP的用户登录和验证机制
✅ 可视化一个数据湖的构建和数据处理管道

**不适合的内容类型**：
- 纯理论阐述 (如算法原理、设计模式概念)
- 代码片段或伪代码
- 缺乏明确结构或组件的文本
- 个人观点或评论性内容

**典型不适用场景**：
❌ 解释排序算法的具体实现步骤
❌ 描述一个数学定理的证明过程
❌ 展示一段Python代码的功能
❌ 评论某个技术的优缺点

**判断标准**：
内容是否描述了一个包含多个组件/模块/步骤的系统或流程？是否需要清晰展示这些元素之间的结构、关系或交互？如果答案是肯定的，则适合使用架构图工具。

**输出形式**：生成文本格式的架构图描述"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        return True

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any], focus: str = None) -> Optional[str]:
        """应用工具，执行具体的扩充操作"""
        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑
            result_data = self._generate_with_llm(content, focus)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_data)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")
            return result_data

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        architecture_config = data.get("architecture_config", {})
        content_description = architecture_config.get("content_description", "")
        narration = architecture_config.get("narration", "")
        diagram_id = architecture_config.get("id", "")

        intro = f"## 🏗️ {self.tool_description}\n\n"
        intro += f"**架构图ID**: {diagram_id}\n\n"
        intro += f"**描述长度**: {len(content_description)} 字符\n\n"
        intro += "**动画配置**:\n"
        intro += "```json\n"
        intro += "{\n"
        intro += '  "type": "animate_architecture_diagram",\n'
        intro += '  "params": {\n'
        intro += f'    "content_description": "{content_description[:100]}...",\n'
        intro += f'    "narration": "{narration}",\n'
        intro += f'    "id": "{diagram_id}"\n'
        intro += "  }\n"
        intro += "}\n"
        intro += "```\n\n"
        intro += "**使用说明**: 上述配置可直接用于animate_architecture_diagram函数，生成交互式架构图动画。\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = ArchitectureDiagramTool(config.config)
    tool.apply_tool(
        open("output/chili3d/project_analysis.md").read(),
        "./test_output",
        context={},
        focus="Chili3D的技术架构，包括前端、WebAssembly模块、OpenCascade、Three.js等核心组件及其相互关系。",
    )
