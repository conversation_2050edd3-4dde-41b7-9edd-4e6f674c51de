#!/usr/bin/env python3
"""
核心信息提取工具 - 内容结构化组织类
专门提取文档的核心关键信息，包括标题、关键词、核心内容要点，为讲解思路提供清晰的结构化指导
"""

import json
import os
import re
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class CoreInfoExtractionTool(EnhancementTool):
    """核心信息提取工具 - 内容结构化组织类"""

    tool_name = "core_info_extraction"
    tool_description = "从内容中提取核心关键信息，包括主题标题、关键词、核心内容要点，为讲解思路提供清晰的结构化指导"
    tool_category = ToolCategory.CONTENT_ORGANIZATION

    def __init__(self, config=None):
        self.config = config
        self.extraction_agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )

            # 定制化系统提示词
            system_prompt = """你是专业的内容分析专家，专门从文档中提取核心关键信息，为讲解者提供清晰的结构化指导。

你的职责是：
1. 识别文档的主要主题和核心观点
2. 提取最重要的关键概念和术语
3. 总结核心内容要点，形成讲解思路
4. 为每个要点提供简洁的解释说明
5. 构建层次化的信息结构

**提取原则**：
1. **主题聚焦**：识别1个主要主题和2-3个次要主题
2. **关键词精选**：选择5-8个最核心的概念词汇
3. **要点提炼**：提取3-5个核心要点，每个要点控制在50字以内
4. **层次分明**：区分主要内容和支撑细节
5. **讲解导向**：站在讲解者角度，提供易于理解和传达的结构

**避免内容**：
- 过度细节和技术参数
- 冗长的解释和描述
- 重复性信息
- 无关的背景信息

请确保提取的信息能够为讲解者提供清晰的思路指导，帮助构建有条理的内容结构。"""

            self.extraction_agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """核心信息提取工具 - 从内容中提取核心关键信息，为内容理解和讲解提供结构化指导

**核心作用**：
核心信息提取是一种内容分析工具，专门从复杂文档中识别和提取最重要的信息要素，包括主题标题、关键概念、核心要点等，帮助观众快速理解内容精髓和为讲解者准备结构化材料。

**适合的内容类型**：
- 学术论文和研究报告（如技术论文、研究成果、理论分析）
- 技术文档和教程材料（如开发指南、技术规范、学习资料）
- 产品介绍和项目说明（如产品文档、项目报告、方案介绍）
- 理论阐述和概念解释（如知识科普、原理说明、概念介绍）

**典型应用场景**：
✅ 分析一篇机器学习论文的核心贡献和关键概念
✅ 提取技术文档的主要功能点和使用要点
✅ 总结产品介绍的核心特性和价值主张
✅ 梳理教程材料的知识结构和学习要点

**不适合的内容类型**：
- 纯数据表格和统计信息（如财务报表、数据清单、参数列表）
- 操作步骤和流程指南（如安装教程、配置手册、操作说明）
- 代码片段和技术实现（如程序代码、脚本文件、配置文件）
- 简短的新闻资讯（如新闻摘要、简讯、公告通知）

**典型不适用场景**：
❌ 提取代码文件中的函数和变量信息
❌ 分析数据表格中的统计数值和趋势
❌ 总结软件安装步骤和配置流程
❌ 处理简短的新闻标题和摘要信息

**判断标准**：
内容是否包含明确的主题和核心观点？是否存在需要理解和掌握的关键概念？内容是否具有一定的深度和复杂性，需要结构化梳理？如果答案是肯定的，则适合使用核心信息提取工具。

**输出形式**：生成结构化的核心信息摘要，包含主题标题、关键概念列表、核心要点说明和讲解建议。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关、模型可用
        basic_check = (
            self.config
            and len(content) >= 400
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 检查agent是否初始化成功
        if self.extraction_agent is None:
            return False

        return basic_check

    def apply_tool(
        self, content: str, output_dir: str, context: dict[str, Any], focus: str = None
    ) -> Optional[dict[str, Any]]:
        """应用工具，执行具体的核心信息提取操作"""
        logger.info(f"🔧 应用{self.tool_name}工具，准备提取核心信息")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = json.load(f)
                # return {
                #    "tool_name": self.tool_name,
                #    "type": self.tool_name,
                #    "file_path": output_path,
                #    "data": existing_data,
                #    "core_points_count": len(existing_data.get("core_points", [])),
                #    "status": "exists",
                # }
                return existing_data.get("core_points", [])

            # 执行具体的工具逻辑，传递focus参数
            result_data = self._process_with_camel(content, context, focus)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            # return {
            #    "tool_name": self.tool_name,
            #    "type": self.tool_name,
            #    "file_path": output_path,
            #    "data": result_data,
            #    "core_points_count": len(result_data.get("core_points", [])),
            #    "status": "created",
            # }
            return result_data.get("core_points", [])

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_with_camel(self, content: str, context: dict[str, Any], focus: str = None) -> Optional[dict[str, Any]]:
        """使用Camel进行核心信息提取的处理逻辑"""
        purpose = context.get("purpose", "核心信息提取")

        # 构建focus指令
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**重点关注方向**：{focus}
请在提取核心信息时特别关注这个方向，确保相关内容在提取结果中得到充分体现和突出展示。"""

        # 构建结构化提示词
        prompt = f"""**请从以下内容中提取核心关键信息，为讲解思路提供结构化指导**：

**原始内容**：
{content[:8000]}{'...(内容截断)' if len(content) > 8000 else ''}

**提取目标**：{purpose}

{focus_instruction}

**提取要求**：

1. **主题识别**：
   - 确定1个主要主题（15字以内）
   - 识别2-3个次要主题（每个10字以内）

2. **关键词提取**：
   - 选择5-8个最核心的概念词汇
   - 每个关键词附加简短解释（20字以内）

3. **核心要点**：
   - 提取3-5个核心要点
   - 每个要点包含：标题（15字以内）+ 说明（50字以内）
   - 按重要性排序

4. **讲解建议**：
   - 提供2-3条讲解思路建议
   - 每条建议控制在30字以内

**输出要求**：
```json
{{
    "main_theme": "主要主题标题",
    "sub_themes": [
        "次要主题1",
        "次要主题2",
        "次要主题3"
    ],
    "keywords": [
        {{
            "term": "关键词1",
            "explanation": "简短解释"
        }},
        {{
            "term": "关键词2",
            "explanation": "简短解释"
        }}
    ],
    "core_points": [
        {{
            "title": "核心要点1标题",
            "description": "核心要点1的详细说明",
            "importance": "high|medium|low"
        }},
        {{
            "title": "核心要点2标题",
            "description": "核心要点2的详细说明",
            "importance": "high|medium|low"
        }}
    ],
    "teaching_suggestions": [
        "讲解建议1",
        "讲解建议2",
        "讲解建议3"
    ],
    "content_structure": {{
        "introduction": "适合作为开场介绍的内容要点",
        "main_body": "适合作为主体讲解的核心内容",
        "conclusion": "适合作为总结的关键信息"
    }},
    "metadata": {{
        "content_type": "识别的内容类型",
        "difficulty_level": "beginner|intermediate|advanced",
        "estimated_time": "预估讲解时间（分钟）",
        "target_audience": "目标受众"
    }}
}}
```

**关键要求**：
- 信息要精炼准确，避免冗余
- 要点要层次分明，逻辑清晰
- 关键词要具有代表性和重要性
- 讲解建议要实用可操作
- 结构要利于快速理解和传达"""

        try:
            # 调用Camel agent进行处理
            response = self.extraction_agent.step(prompt)
            response_content = response.msgs[0].content

            # 提取JSON
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个响应
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            return result_data

        except Exception as e:
            logger.error(f"Camel核心信息提取失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        intro = f"## 🎯 {self.tool_description}\n\n"

        # 主题信息
        main_theme = data.get("main_theme", "")
        sub_themes = data.get("sub_themes", [])
        if main_theme:
            intro += "### 🏷️ 核心主题\n\n"
            intro += f"**主要主题**：{main_theme}\n\n"
            if sub_themes:
                intro += f"**次要主题**：{' | '.join(sub_themes)}\n\n"

        # 关键词列表
        keywords = data.get("keywords", [])
        if keywords:
            intro += "### 🔑 关键概念\n\n"
            for keyword in keywords:
                term = keyword.get("term", "")
                explanation = keyword.get("explanation", "")
                intro += f"- **{term}**：{explanation}\n"
            intro += "\n"

        # 核心要点
        core_points = data.get("core_points", [])
        if core_points:
            intro += "### 📋 核心要点\n\n"
            for i, point in enumerate(core_points, 1):
                title = point.get("title", f"要点{i}")
                description = point.get("description", "")
                importance = point.get("importance", "medium")
                importance_icon = {"high": "🔥", "medium": "⭐", "low": "💡"}.get(importance, "⭐")
                intro += f"{i}. {importance_icon} **{title}**\n"
                intro += f"   {description}\n\n"

        # 讲解建议
        suggestions = data.get("teaching_suggestions", [])
        if suggestions:
            intro += "### 💭 讲解建议\n\n"
            for i, suggestion in enumerate(suggestions, 1):
                intro += f"{i}. {suggestion}\n"
            intro += "\n"

        # 内容结构
        structure = data.get("content_structure", {})
        if structure:
            intro += "### 📖 内容结构\n\n"
            if structure.get("introduction"):
                intro += f"**开场导入**：{structure.get('introduction')}\n\n"
            if structure.get("main_body"):
                intro += f"**主体讲解**：{structure.get('main_body')}\n\n"
            if structure.get("conclusion"):
                intro += f"**总结收尾**：{structure.get('conclusion')}\n\n"

        # 元数据信息
        metadata = data.get("metadata", {})
        if metadata:
            intro += "### 📊 内容特征\n\n"
            content_type = metadata.get("content_type", "")
            difficulty = metadata.get("difficulty_level", "")
            time = metadata.get("estimated_time", "")
            audience = metadata.get("target_audience", "")

            intro += f"- **内容类型**：{content_type}\n"
            intro += f"- **难度级别**：{difficulty}\n"
            intro += f"- **预估时长**：{time}分钟\n"
            intro += f"- **目标受众**：{audience}\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = CoreInfoExtractionTool(config.config)
    
    # 测试内容
    test_content = """
    深度学习中的注意力机制：Transformer架构详解
    
    注意力机制是深度学习领域的一个重要突破，特别是在自然语言处理任务中展现出了卓越的性能。
    Transformer架构完全基于注意力机制，摒弃了传统的循环神经网络和卷积神经网络结构。
    
    核心概念：
    1. 自注意力机制（Self-Attention）：允许模型在处理序列中的每个位置时，关注序列中的所有位置。
    2. 多头注意力（Multi-Head Attention）：通过多个注意力头并行计算，捕获不同类型的依赖关系。
    3. 位置编码（Positional Encoding）：由于Transformer没有循环结构，需要额外的位置信息。
    4. 前馈神经网络（Feed-Forward Network）：在每个注意力层后添加的全连接层。
    
    Transformer的优势：
    - 并行化计算：不同于RNN的序列计算，Transformer可以并行处理所有位置
    - 长距离依赖：通过注意力机制直接建模任意距离的依赖关系
    - 可解释性：注意力权重提供了模型决策的可视化解释
    
    应用领域：
    Transformer架构不仅在机器翻译、文本摘要等NLP任务中表现出色，还被成功应用到计算机视觉、
    语音识别等其他领域，成为了现代深度学习的基础架构之一。
    
    技术细节：
    - 注意力计算公式：Attention(Q,K,V) = softmax(QK^T/√d_k)V
    - 模型复杂度：O(n²d)，其中n是序列长度，d是特征维度
    - 训练技巧：使用残差连接、层归一化、学习率调度等技术
    """
    
    # 测试focus参数
    test_focus = "重点关注Transformer架构的核心技术原理和创新点，特别是注意力机制的工作原理和优势"
    
    result = tool.apply_tool(
        content=test_content,
        output_dir="./test_output",
        context={"purpose": "深度学习技术解析"},
        focus=test_focus
    )
    
    if result:
        print("核心信息提取完成！")
        print(f"提取的核心要点数量: {len(result)}")
        
        # 显示核心要点
        for i, point in enumerate(result, 1):
            if isinstance(point, dict):
                title = point.get("title", f"要点{i}")
                description = point.get("description", "")
                importance = point.get("importance", "medium")
                print(f"\n{i}. [{importance.upper()}] {title}")
                print(f"   {description}")
        
        # 如果result是完整数据，生成介绍文本
        if result and isinstance(result, list) and len(result) > 0:
            print("\n核心信息提取成功！")
        else:
            print("\n需要查看完整的JSON文件获取详细信息")
    else:
        print("核心信息提取失败！")
