#!/usr/bin/env python3
"""
Deep Insight工具 - 深度洞察分析
从多角度、多层次对内容进行深入洞察，产生有价值的认知观点
"""

import json
import os
import re
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class DeepInsightTool(EnhancementTool):
    """Deep Insight工具 - 深度洞察分析"""

    tool_name = "deep_insight"
    tool_description = "深度洞察分析工具，从多维度视角深入挖掘内容的深层含义和价值观点"
    tool_category = ToolCategory.DEEP_INSIGHTS

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型进行深度分析"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )

            # 深度洞察专用系统提示词
            system_prompt = """你是一位资深的深度洞察专家，擅长针对具体问题给出精准、深刻的答案。

你的核心原则：
- 直接回答：正面回应用户的焦点方向，不回避、不泛化
- 精准洞察：找到关键机制和核心价值点，避免抽象概念
- 严控字数：标题15字内，描述100字内，言简意赅
- 具体务实：说明"为什么这样设计"、"关键在哪里"、"解决了什么问题"

你的工作方式：
1. 准确理解焦点方向的具体要求
2. 在内容中找到相关的具体体现和机制
3. 挖掘其背后的设计原理和实际价值
4. 用最简洁的语言表达核心洞察

严格禁止：
- 使用模糊的抽象理论术语
- 超出字数限制
- 回避焦点进行泛泛而谈
- 给出无法验证的宽泛观点

你的目标是针对焦点方向给出具体、深刻、简洁的洞察分析。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """深度洞察分析工具 - 运用多种思维模型挖掘深层价值

**核心作用**：
通过苏格拉底追问、批判性思维、第一性原理等方法，对具有思辨价值的内容进行深层次分析，揭示表面现象背后的本质机制、设计原理和内在价值，产生具有启发性的认知洞察。

**🎯 最适合的内容特征**：
- **有观点争议**：存在不同观点或需要深入思考的内容
- **有设计思想**：体现某种设计理念或创新思路的内容
- **有因果逻辑**：可以探究"为什么这样"、"怎么实现的"的内容
- **有价值判断**：涉及策略选择、方法评估、趋势分析的内容
- **有理论深度**：包含概念模型、原理阐述、机制解释的内容

**✅ 典型适用场景（具体示例）**：
- **学术论文分析**：如"为什么该算法能突破传统限制？核心创新点在哪里？"
- **商业模式解读**：如"这种盈利模式的底层逻辑是什么？风险在哪里？"
- **技术原理探究**：如"这个设计为什么巧妙？解决了什么根本问题？"
- **策略思维分析**：如"这个决策背后的考虑因素有哪些？反向思考的风险？"
- **现象本质挖掘**：如"这个趋势反映了什么深层变化？驱动因素是什么？"
- **观点批判评估**：如"这个结论的前提假设是否成立？逻辑链条是否完整？"

**❌ 明确不适用的内容类型**：
- **纯操作指南**：安装步骤、配置说明、使用教程等程序性内容
- **事实性描述**：数据报告、规格参数、历史事件等客观陈述
- **日常资讯**：新闻动态、公告通知、会议记录等信息性内容
- **技术细节**：代码实现、接口文档、调试信息等技术性细节
- **简单问答**：FAQ、基础概念解释、定义说明等直接回答

**❌ 典型不适用场景举例**：
- 分析"如何安装Python"的操作步骤
- 解读财务报表中的数据表格
- 处理"公司年会通知"的事务性信息
- 分析API接口的参数说明文档
- 解释"什么是机器学习"的基础定义

**🔍 核心分析方法**：
- **连续追问**：针对关键点进行5个Why的深入挖掘
- **假设质疑**：识别并质疑内容中的隐含假设和前提条件
- **反向验证**：从结论倒推过程，检验逻辑的完整性
- **多角度审视**：从不同利益相关者角度重新审视问题
- **第一性拆解**：将复杂现象拆解到最基本的原理和要素

**💡 智能判断标准**：
1. **思辨价值测试**：内容是否提出了值得深入思考的观点或现象？
2. **追问空间评估**：是否可以进行"为什么"、"如何实现"的连续追问？
3. **洞察潜力判断**：是否存在表面现象背后的深层机制值得挖掘？
4. **认知价值衡量**：分析结果是否能带来新的认知启发或思维角度？

**📋 输出形式**：
针对焦点方向生成1-3个核心洞察，每个洞察包含：
- 洞察标题（15字内，突出核心发现）
- 洞察描述（100字内，说明具体机制和价值）
- 直接回答焦点问题，避免抽象概念，注重实用性"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 200  # 降低长度要求，支持更小的内容单元
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 如果需要LLM支持，检查agent是否初始化成功
        if hasattr(self, "agent") and self.agent is None:
            return False

        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any], focus: str = None) -> Optional[str]:
        """应用工具，执行具体的深度洞察操作"""
        logger.info(f"💡 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = f.read()
                return existing_data

            # 执行具体的工具逻辑，使用focus参数而不是context
            result_content = self._process_with_camel(content, focus)

            if not result_content:
                return None

            # 保存结果数据（保持原有格式以便后续处理）
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result_content)

            logger.info(f"💡 {self.tool_name}工具处理完成: {output_path}")
            # 返回深度洞察内容字符串，符合新的简化要求
            return result_content

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_with_camel(self, content: str, focus: str = None) -> Optional[str]:
        """使用Camel进行深度洞察处理的逻辑"""
        # 构建结构化提示词，重点利用focus信息
        focus_instruction = ""
        if focus:
            focus_instruction = f"""
**重点关注方向**：{focus}
请紧紧围绕这个焦点方向进行深度洞察分析，通过连续追问和批判性思考来挖掘相关的深层价值和启发。"""

        prompt = f"""你是专业的深度洞察分析专家，必须针对以下焦点方向给出直接、具体的回答。
<focus_instruction>
{focus_instruction}
</focus_instruction>

**核心要求**：
1. **直接回答**：必须正面回答焦点方向的具体问题，不能回避或泛化
2. **具体务实**：避免抽象概念和空洞理论，要给出具体的机制、原因、影响
3. **字数严控**：每个洞察描述严格控制在30字以内，标题控制在10字以内
4. **真正深入**：要揭示"为什么这样设计"、"关键在哪里"、"真正解决了什么问题"

**分析流程**：
- 第一步：找到焦点方向在内容中的具体体现
- 第二步：挖掘其背后的设计原理或核心机制
- 第三步：识别关键价值点和实际意义

**输出格式**：
请严格按照以下JSON格式输出，确保字数限制：

{{
    {{
        "insight_title": "洞察标题（10字内）",
        "insight_description": "针对焦点的具体洞察，说明关键机制和价值（30字内）"
    }}
    // 最多3个洞察，重质量不重数量
}}

**严格禁止**：
- 使用模糊的抽象概念和理论术语
- 回避焦点方向，进行泛泛而谈
- 超出字数限制
- 给出无法验证或过于宽泛的观点

请直接输出JSON结果，不要包含额外说明。

**待分析内容**：
<content>
{content}
</content>

"""

        logger.info(f"生成深度洞察分析, prompt characters: {len(prompt)}, words: {len(prompt.split())}")

        try:
            # 调用Camel agent进行处理
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content.strip()

            # 检查是否不适合生成深度洞察
            if response_content.startswith("该内容不适合生成深度洞察"):
                logger.info(f"内容不适合生成深度洞察: {response_content}")
                return response_content

            # 返回生成的深度洞察内容
            return response_content

        except Exception as e:
            logger.error(f"Camel数据提取失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        # 兼容新的返回格式：tool_result现在直接是字符串内容
        if isinstance(tool_result, str):
            result_content = tool_result
        else:
            # 兼容旧格式
            result_content = tool_result.get("data", "") if tool_result else ""

        if not result_content:
            return ""

        # 检查是否不适合生成深度洞察
        if result_content.startswith("该内容不适合生成深度洞察"):
            return f"## 💡 深度洞察分析\n\n❌ {result_content}\n\n"

        # 生成深度洞察介绍
        intro = f"## 💡 {self.tool_description}\n\n"
        intro += "**针对焦点的深度洞察**：\n\n"
        intro += result_content
        intro += "\n\n*🎯 针对性分析，直接回答焦点方向，言简意赅*\n\n"

        return intro


if __name__ == "__main__":
    from utils.common import Config

    config = Config()
    tool = DeepInsightTool(config.config)
    tool.apply_tool(
        open("output/2506.06395/2506.06395.md").read(),
        "output/2506.06395",
        context={},
        focus="基于论文内容，生成关于RLSC的三个深度洞察分析",
        #focus="分析RLSC的损失函数如何巧妙地避免了传统RL对外部奖励的依赖",
    )
