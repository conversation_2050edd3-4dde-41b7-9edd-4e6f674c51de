# animate_text_only

## 效果

创建纯文本内容的动态展示动画，专门用于文字信息的呈现。支持清单、要点、步骤等文本内容的动态显示，
包含渐变背景、发光字体效果、逐字显示动画等视觉特效，适合纯文本内容的精美展示。

## 使用场景

- 展示要点清单、任务列表、检查清单等文本内容
- 创建纯文字的动态内容展示，如学习要点、工作计划等
- 制作带有视觉特效的文本演示，突出重要信息
- 文字内容的分步展示和强调

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 文本展示的主标题 | 否 | ✨ 夏日出游 Checklist |
| items | List[Dict] | 文本项目列表，每个项目包含icon、text、tags字段，颜色会自动分配 | 否 | None |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_text_only",
  "params": {
    "title": "📚 学习要点总结",
    "items": [
      {
        "icon": "📖",
        "text": "理论知识学习",
        "tags": "#基础概念 #核心原理"
      },
      {
        "icon": "💻",
        "text": "实践操作练习",
        "tags": "#动手实践 #技能提升"
      },
      {
        "icon": "🎯",
        "text": "目标达成检验",
        "tags": "#效果评估 #持续改进"
      }
    ],
    "narration": "这是我们学习过程中需要重点关注的三个要点。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_text_only",
  "params": {
    "title": "🎯 工作计划清单",
    "items": [
      {
        "icon": "📋",
        "text": "制定详细计划",
        "tags": "#时间安排 #任务分解"
      },
      {
        "icon": "⚡",
        "text": "高效执行任务",
        "tags": "#专注投入 #质量保证"
      },
      {
        "icon": "📊",
        "text": "跟踪进度反馈",
        "tags": "#进度监控 #及时调整"
      },
      {
        "icon": "✅",
        "text": "完成总结复盘",
        "tags": "#成果总结 #经验提炼"
      }
    ],
    "narration": "高效工作需要遵循这四个关键步骤。"
  }
}
```

## 注意事项

- 专门用于纯文本内容的动态展示，不涉及图片或视频
- 支持最多5个文本项目的展示
- 每个文本项包含图标、主文本、标签，颜色自动分配
- 具有发光字体效果和逐字显示动画
- 适合要点总结、清单展示、步骤说明等文本场景
- 使用渐变背景和动态特效提升文本展示的视觉效果

