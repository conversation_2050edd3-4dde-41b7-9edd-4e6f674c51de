import os
import logging
from typing import Optional, Dict, Any, List

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend
import yaml

logger = logging.getLogger(__name__)

class TechnicalImplementationAgent:
    """技术实现代理，负责生成定理教学视频的Manim技术实现计划"""
    
    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化技术实现代理
        
        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Technical Implementation Expert",
                content="你是一位专业的Manim技术专家，擅长将视觉故事板转化为详细的技术实现计划，精通Manim库的各种对象、动画和布局技术。"
            ),
            model=model
        )
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("technical_implementation", {})
            self.prompts = self.agent_config.get("prompts", {})
            
            logger.info(f"技术实现代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}
    
    async def generate(
        self, 
        topic: str, 
        description: str, 
        scene_number: int,
        scene_outline: str,
        vision_storyboard: str,
        relevant_plugins: List[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成技术实现计划（与工作流接口兼容）
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_number: 场景编号
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 包含生成的技术实现计划的字典
        """
        logger.info(f"为场景{scene_number}生成技术实现计划: {topic}")
        
        try:
            if relevant_plugins is None:
                relevant_plugins = []
                
            # 生成技术实现计划
            technical_implementation = await self.generate_technical_implementation(
                scene_number=scene_number,
                topic=topic,
                description=description,
                scene_outline=scene_outline,
                vision_storyboard=vision_storyboard,
                relevant_plugins=relevant_plugins,
                rag_context=None,
                session_id=session_id
            )
            
            return {
                "status": "success",
                "technical_implementation": technical_implementation
            }
        except Exception as e:
            logger.error(f"生成技术实现计划失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def generate_technical_implementation(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        vision_storyboard: str,
        relevant_plugins: List[str],
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        生成技术实现计划
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            str: 生成的技术实现计划
        """
        logger.info(f"生成场景{scene_number}的技术实现计划: {topic}")
        
        # 构建提示
        prompt = self._build_prompt(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            relevant_plugins=relevant_plugins,
            rag_context=rag_context
        )
        
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取内容
        technical_implementation = response.msg.content
        logger.info(f"技术实现计划生成完成: {len(technical_implementation)} 字符")
        
        return technical_implementation
    
    def _build_prompt(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        vision_storyboard: str,
        relevant_plugins: List[str],
        rag_context: Optional[str] = None
    ) -> str:
        """
        构建技术实现提示
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            
        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("technical_implementation_template", "")
        
        if not template:
            # 默认提示模板
            template = """
你是一位专业的Manim技术专家，需要为数学定理教学视频的场景{scene_number}创建一个详细的技术实现计划。

主题：{topic}
描述：{description}
场景大纲：{scene_outline}
视觉故事板：{vision_storyboard}

请创建一个详细的技术实现计划，描述如何使用Manim实现视觉故事板中的效果。

你的技术实现计划应该包括：
1. 所需的Manim对象和类
2. 对象属性设置（颜色、大小、位置等）
3. 动画序列和时间安排
4. 组织结构（VGroup层次结构）
5. 空间布局策略（确保遵循0.5单位安全边距和0.3单位最小间距）

请以以下格式输出技术实现计划：

<TECHNICAL_IMPLEMENTATION>
场景{scene_number}：[场景名称]

阶段1：[阶段名称]
- [Manim对象创建和设置]
- [对象位置和属性]
- [动画序列]
- [VGroup结构]
        
阶段2：[阶段名称]
- [Manim对象创建和设置]
- [对象位置和属性]
- [动画序列]
- [VGroup结构]
        
...

技术说明：
- [空间约束和布局策略]
- [特殊技术实现细节]
- [潜在问题和解决方案]
</TECHNICAL_IMPLEMENTATION>

请确保技术实现计划是可行的，符合Manim的能力，并且能够有效实现视觉故事板中描述的效果。
"""
        
        # 格式化相关插件
        plugins_str = "、".join(relevant_plugins) if relevant_plugins else "无"
        
        # 替换占位符
        prompt = template.format(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            relevant_plugins=plugins_str
        )
        
        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考资料（用于辅助技术实现）：\n{rag_context}"
        
        return prompt 