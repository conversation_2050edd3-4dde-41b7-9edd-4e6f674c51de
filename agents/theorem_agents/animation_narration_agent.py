import os
import logging
from typing import Optional, Dict, Any, List

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend
import yaml

logger = logging.getLogger(__name__)

class AnimationNarrationAgent:
    """动画叙述代理，负责生成定理教学视频的旁白脚本和动画时间安排"""
    
    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化动画叙述代理
        
        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Animation Narration Expert",
                content="你是一位专业的教育视频旁白脚本专家，擅长创作清晰、引人入胜的数学解释，并规划与动画精确同步的讲解内容。"
            ),
            model=model
        )
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("animation_narration", {})
            self.prompts = self.agent_config.get("prompts", {})
            
            logger.info(f"动画叙述代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}
    
    async def generate(
        self, 
        topic: str, 
        description: str, 
        scene_number: int,
        scene_outline: str,
        vision_storyboard: str,
        technical_implementation: str,
        relevant_plugins: List[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成动画叙述（与工作流接口兼容）
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_number: 场景编号
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            technical_implementation: 技术实现计划 
            relevant_plugins: 相关插件列表
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 包含生成的动画叙述的字典
        """
        logger.info(f"为场景{scene_number}生成动画叙述: {topic}")
        
        try:
            if relevant_plugins is None:
                relevant_plugins = []
                
            # 生成动画叙述
            animation_narration = await self.generate_animation_narration(
                scene_number=scene_number,
                topic=topic,
                description=description,
                scene_outline=scene_outline,
                vision_storyboard=vision_storyboard,
                technical_implementation=technical_implementation,
                relevant_plugins=relevant_plugins,
                rag_context=None,
                session_id=session_id
            )
            
            return {
                "status": "success",
                "animation_narration": animation_narration
            }
        except Exception as e:
            logger.error(f"生成动画叙述失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def generate_animation_narration(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        vision_storyboard: str,
        technical_implementation: str,
        relevant_plugins: List[str],
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        生成动画叙述
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            technical_implementation: 技术实现计划
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            str: 生成的动画叙述
        """
        logger.info(f"生成场景{scene_number}的动画叙述: {topic}")
        
        # 构建提示
        prompt = self._build_prompt(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            technical_implementation=technical_implementation,
            relevant_plugins=relevant_plugins,
            rag_context=rag_context
        )
        
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取内容
        animation_narration = response.msg.content
        logger.info(f"动画叙述生成完成: {len(animation_narration)} 字符")
        
        return animation_narration
    
    def _build_prompt(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        vision_storyboard: str,
        technical_implementation: str,
        relevant_plugins: List[str],
        rag_context: Optional[str] = None
    ) -> str:
        """
        构建动画叙述提示
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            vision_storyboard: 视觉故事板
            technical_implementation: 技术实现计划
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            
        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("animation_narration_template", "")
        
        if not template:
            # 默认提示模板
            template = """
你是一位专业的教育视频旁白脚本专家，需要为数学定理教学视频的场景{scene_number}创建一个详细的动画叙述计划。

主题：{topic}
描述：{description}
场景大纲：{scene_outline}
视觉故事板：{vision_storyboard}
技术实现计划：{technical_implementation}

请创建一个详细的动画叙述计划，包含旁白脚本和动画时间安排。

你的动画叙述计划应该包括：
1. 旁白脚本（与动画同步）
2. 每段旁白对应的动画时间点
3. 语音语调和节奏建议
4. 关键强调点

请以以下格式输出动画叙述计划：

<ANIMATION_NARRATION>
场景{scene_number}：[场景名称]

阶段1：[阶段名称]
- 旁白：[旁白文本]
- 时间点：[对应的动画时间点]
- 语调：[语调建议]
        
阶段2：[阶段名称]
- 旁白：[旁白文本]
- 时间点：[对应的动画时间点]
- 语调：[语调建议]
        
...

叙述说明：
- [语速和节奏建议]
- [关键术语发音指导]
- [叙述风格建议]
</ANIMATION_NARRATION>

请确保旁白内容清晰、简洁、易于理解，并与视觉元素和动画完美同步。
"""
        
        # 格式化相关插件
        plugins_str = "、".join(relevant_plugins) if relevant_plugins else "无"
        
        # 替换占位符
        prompt = template.format(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            vision_storyboard=vision_storyboard,
            technical_implementation=technical_implementation,
            relevant_plugins=plugins_str
        )
        
        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考资料（用于辅助叙述）：\n{rag_context}"
        
        return prompt 