import os
import logging
from typing import Optional, Dict, Any

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend
import yaml

logger = logging.getLogger(__name__)

class ScenePlanAgent:
    """场景规划代理，负责生成定理教学视频的整体场景规划"""
    
    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化场景规划代理
        
        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Scene Planning Expert",
                content="你是一位专业的教育视频场景规划专家，擅长将复杂的数学定理分解为连贯、易于理解的场景序列。"
            ),
            model=model
        )
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("scene_plan", {})
            self.prompts = self.agent_config.get("prompts", {})
            
            logger.info(f"场景规划代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}
    
    def generate(
        self, 
        topic: str, 
        description: str, 
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成场景规划
        
        Args:
            topic: 定理主题
            description: 定理描述
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 包含生成的场景规划的字典
        """
        logger.info(f"生成场景规划: {topic}")
        
        try:
            # 构建提示
            prompt = self._build_prompt(topic, description, rag_context)
            
            # 生成响应
            message = BaseMessage.make_user_message(role_name="User", content=prompt)
            response = self.agent.step(message)
            
            # 提取内容
            scene_plan = response.msg.content
            logger.info(f"场景规划生成完成: {len(scene_plan)} 字符")
            
            return {
                "status": "success",
                "scene_outline": scene_plan
            }
        except Exception as e:
            logger.error(f"生成场景规划失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    def generate_scene_plan(
        self, 
        topic: str, 
        description: str, 
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        生成场景规划
        
        Args:
            topic: 定理主题
            description: 定理描述
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            str: 生成的场景规划
        """
        logger.info(f"生成场景规划: {topic}")
        
        # 构建提示
        prompt = self._build_prompt(topic, description, rag_context)
        
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取内容
        scene_plan = response.msg.content
        logger.info(f"场景规划生成完成: {len(scene_plan)} 字符")
        
        return scene_plan
    
    def _build_prompt(self, topic: str, description: str, rag_context: Optional[str] = None) -> str:
        """
        构建场景规划提示
        
        Args:
            topic: 定理主题
            description: 定理描述
            rag_context: RAG检索的上下文
            
        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("scene_plan_template", "")
        
        if not template:
            # 默认提示模板
            template = """
你是一位专业的教育视频场景规划专家，需要为以下数学定理创建一个清晰的场景规划：

主题：{topic}
描述：{description}

请规划一个定理教学视频的场景大纲，将定理的讲解分解为3-5个连贯且逻辑清晰的场景。每个场景都应该有明确的教学目标和内容重点。

你的场景规划应该包括：
1. 场景数量和顺序
2. 每个场景的主要内容和重点
3. 场景之间的连接和过渡
4. 确保整体逻辑连贯性

请以以下格式输出场景大纲：

<SCENE_OUTLINE>
场景1: [场景名称]
- [关键内容点1]
- [关键内容点2]
...

场景2: [场景名称]
- [关键内容点1]
- [关键内容点2]
...
</SCENE_OUTLINE>

请确保场景规划是完整的，能够全面覆盖定理的关键概念、证明过程和应用。
"""
        
        # 替换占位符
        prompt = template.format(
            topic=topic,
            description=description
        )
        
        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考信息（用于辅助规划）：\n{rag_context}"
        
        return prompt 