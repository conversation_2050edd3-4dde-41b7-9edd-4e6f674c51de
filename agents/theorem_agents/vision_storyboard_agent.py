import os
import logging
from typing import Optional, Dict, Any

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend
import yaml

logger = logging.getLogger(__name__)

class VisionStoryboardAgent:
    """视觉故事板代理，负责生成定理教学视频的视觉故事板"""
    
    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化视觉故事板代理
        
        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Vision Storyboard Expert",
                content="你是一位专业的教育动画视觉设计专家，擅长将抽象的数学概念转化为引人入胜的视觉故事板。"
            ),
            model=model
        )
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("vision_storyboard", {})
            self.prompts = self.agent_config.get("prompts", {})
            
            logger.info(f"视觉故事板代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}
    
    async def generate(
        self, 
        topic: str, 
        description: str, 
        scene_number: int,
        scene_outline: str,
        relevant_plugins: list = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成视觉故事板（与工作流接口兼容）
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_number: 场景编号
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表 
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 包含生成的视觉故事板的字典
        """
        logger.info(f"为场景{scene_number}生成视觉故事板: {topic}")
        
        try:
            if relevant_plugins is None:
                relevant_plugins = []
                
            # 生成视觉故事板
            vision_storyboard = await self.generate_vision_storyboard(
                scene_number=scene_number,
                topic=topic,
                description=description,
                scene_outline=scene_outline,
                relevant_plugins=relevant_plugins,
                rag_context=None,
                session_id=session_id
            )
            
            return {
                "status": "success",
                "vision_storyboard": vision_storyboard
            }
        except Exception as e:
            logger.error(f"生成视觉故事板失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
            
    async def generate_vision_storyboard(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        relevant_plugins: list,
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        生成视觉故事板
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            str: 生成的视觉故事板
        """
        logger.info(f"生成场景{scene_number}的视觉故事板: {topic}")
        
        # 构建提示
        prompt = self._build_prompt(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            relevant_plugins=relevant_plugins,
            rag_context=rag_context
        )
        
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取内容
        vision_storyboard = response.msg.content
        logger.info(f"视觉故事板生成完成: {len(vision_storyboard)} 字符")
        
        return vision_storyboard
    
    def _build_prompt(
        self, 
        scene_number: int,
        topic: str, 
        description: str, 
        scene_outline: str,
        relevant_plugins: list,
        rag_context: Optional[str] = None
    ) -> str:
        """
        构建视觉故事板提示
        
        Args:
            scene_number: 场景编号
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            relevant_plugins: 相关插件列表
            rag_context: RAG检索的上下文
            
        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("vision_storyboard_template", "")
        
        if not template:
            # 默认提示模板
            template = """
你是一位专业的教育动画视觉设计专家，需要为数学定理教学视频的场景{scene_number}创建一个详细的视觉故事板。

主题：{topic}
描述：{description}
场景大纲：{scene_outline}

请创建一个详细的视觉故事板，描述该场景的视觉元素、动画效果和屏幕布局。

你的视觉故事板应该包括：
1. 分阶段的视觉呈现计划
2. 每个关键元素的视觉特性（颜色、大小、位置）
3. 动画和过渡效果
4. 视觉层次结构和重点突出方式

请以以下格式输出视觉故事板：

<SCENE_VISION_STORYBOARD>
场景{scene_number}：[场景名称]

阶段1：[阶段名称]
- [视觉元素1描述]
- [视觉元素2描述]
- [动画效果描述]
        
阶段2：[阶段名称]
- [视觉元素1描述]
- [视觉元素2描述]
- [动画效果描述]
        
...

视觉设计说明：
- [颜色方案说明]
- [空间布局说明]
- [动画节奏说明]
</SCENE_VISION_STORYBOARD>

请确保视觉故事板与场景大纲保持一致，并设计出既美观又能有效传达数学概念的视觉效果。
"""
        
        # 格式化相关插件
        plugins_str = "、".join(relevant_plugins) if relevant_plugins else "无"
        
        # 替换占位符
        prompt = template.format(
            scene_number=scene_number,
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            relevant_plugins=plugins_str
        )
        
        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考信息（用于辅助设计）：\n{rag_context}"
        
        return prompt 