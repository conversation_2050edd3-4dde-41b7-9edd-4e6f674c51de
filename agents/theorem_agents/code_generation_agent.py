import os
import logging
from typing import Optional, Dict, Any, List

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import BaseModelBackend
import yaml

from utils.format import extract_code

logger = logging.getLogger(__name__)

class CodeGenerationAgent:
    """代码生成代理，负责生成定理教学视频的Manim代码"""
    
    def __init__(self, model: BaseModelBackend, config_path: str = "config/config.yaml"):
        """
        初始化代码生成代理
        
        Args:
            model: 使用的模型后端
            config_path: 配置文件路径
        """
        self.model = model
        self.config_path = config_path
        
        # 加载配置
        self.load_config(config_path)
        
        # 创建代理
        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message(
                role_name="Manim Code Generation Expert",
                content="你是一位专业的Manim代码生成专家，擅长编写高质量的Manim代码实现数学概念可视化，能够根据技术实现计划和动画叙述精确地实现<PERSON><PERSON>代码。"
            ),
            model=model
        )
    
    def load_config(self, config_path: str):
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # 设置配置属性
            self.agent_config = config.get("agents", {}).get("code_generator", {})
            self.prompts = self.agent_config.get("prompts", {})
            
            logger.info(f"代码生成代理配置加载成功: {config_path}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.agent_config = {}
            self.prompts = {}
    
    async def generate(
        self, 
        topic: str, 
        description: str, 
        scene_number: int,
        technical_implementation: str,
        animation_narration: str,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成Manim代码（与工作流接口兼容）
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_number: 场景编号
            technical_implementation: 技术实现计划
            animation_narration: 动画叙述
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 包含生成的Manim代码的字典
        """
        logger.info(f"为场景{scene_number}生成Manim代码: {topic}")
        
        try:
            # 构建场景实现字典
            scene_implementation = {
                "technical_implementation": technical_implementation,
                "animation_narration": animation_narration
            }
                
            # 生成代码
            code = await self.generate_code(
                topic=topic,
                description=description,
                scene_outline="",  # 在该接口中不使用场景大纲
                scene_implementation=scene_implementation,
                scene_number=scene_number,
                rag_context=None,
                session_id=session_id
            )
            
            return {
                "status": "success",
                "code": code
            }
        except Exception as e:
            logger.error(f"生成Manim代码失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def generate_code(
        self, 
        topic: str, 
        description: str, 
        scene_outline: str,
        scene_implementation: Dict[str, str],
        scene_number: int,
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        生成Manim代码
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            scene_implementation: 场景实现字典（包含视觉故事板、技术实现、动画叙述）
            scene_number: 场景编号
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            str: 生成的Manim代码
        """
        logger.info(f"生成场景{scene_number}的Manim代码: {topic}")
        
        # 构建提示
        prompt = self._build_prompt(
            topic=topic,
            description=description,
            scene_outline=scene_outline,
            scene_implementation=scene_implementation,
            scene_number=scene_number,
            rag_context=rag_context
        )
        
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取代码
        code = extract_code(response.msg.content)
        if not code:
            code = response.msg.content  # 如果无法提取代码，则使用完整响应
            
        logger.info(f"Manim代码生成完成: {len(code)} 字符")
        
        return code
    
    def _build_prompt(
        self, 
        topic: str, 
        description: str, 
        scene_outline: str,
        scene_implementation: Dict[str, str],
        scene_number: int,
        rag_context: Optional[str] = None
    ) -> str:
        """
        构建代码生成提示
        
        Args:
            topic: 定理主题
            description: 定理描述
            scene_outline: 场景大纲
            scene_implementation: 场景实现字典（包含视觉故事板、技术实现、动画叙述）
            scene_number: 场景编号
            rag_context: RAG检索的上下文
            
        Returns:
            str: 构建的提示
        """
        # 获取自定义提示模板（如果有）
        template = self.prompts.get("code_generation_template", "")
        
        if not template:
            # 默认提示模板
            template = """
你是一位专业的Manim代码生成专家，需要为数学定理教学视频的场景{scene_number}生成完整的Manim代码。

主题：{topic}
描述：{description}
技术实现计划：{technical_implementation}
动画叙述：{animation_narration}

请生成完整的、可执行的Manim代码，实现技术实现计划中描述的内容，并与动画叙述同步。

代码规范要求：
1. 使用VoiceoverScene作为基类，配合KokoroService语音服务
2. 使用正确的导入语句（包括必要的插件）
3. 创建Scene{scene_number}类，实现construct方法
4. 使用模块化的设计，创建辅助类/方法
5. 确保空间约束（安全边距0.5单位，最小间距0.3单位）
6. 使用相对定位方法，确保布局正确
7. 结合with self.voiceover(text="...")语句与动画同步
8. 添加适当的注释说明代码逻辑

请在<CODE>和</CODE>标签之间输出Python代码：

<CODE>
[完整Manim代码]
</CODE>

请确保代码是完整的、可直接执行的，并且实现了技术实现计划中的所有功能。
"""
        
        # 提取实现细节
        technical_implementation = scene_implementation.get("technical_implementation", "")
        animation_narration = scene_implementation.get("animation_narration", "")
        
        # 替换占位符
        prompt = template.format(
            scene_number=scene_number,
            topic=topic,
            description=description,
            technical_implementation=technical_implementation,
            animation_narration=animation_narration
        )
        
        # 添加RAG上下文
        if rag_context:
            prompt += f"\n\n参考代码示例（用于辅助实现）：\n{rag_context}"
        
        return prompt
    
    async def fix_code_errors(
        self, 
        code: str, 
        error: str, 
        implementation_plan: str,
        scene_number: int,
        topic: str,
        rag_context: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        修复代码错误
        
        Args:
            code: 有错误的代码
            error: 错误信息
            implementation_plan: 实现计划
            scene_number: 场景编号
            topic: 主题
            rag_context: RAG检索的上下文
            session_id: 会话ID
            
        Returns:
            str: 修复后的代码
        """
        logger.info(f"修复场景{scene_number}的代码错误")
        
        # 构建错误修复提示
        prompt = f"""
你是一位专业的Manim代码修复专家，需要修复以下代码中的错误:

错误信息:
{error}

当前代码:
```python
{code}
```

原始实现计划:
{implementation_plan}

请根据错误信息修复代码，保持原有的功能和结构，确保修复后的代码能正确执行。
请在<CODE>和</CODE>标签之间提供完整的修复后代码:

<CODE>
[修复后的完整代码]
</CODE>
"""

        # 添加RAG上下文（如果有）
        if rag_context:
            prompt += f"\n\n参考资料（用于辅助修复）：\n{rag_context}"
            
        # 生成响应
        message = BaseMessage.make_user_message(role_name="User", content=prompt)
        response = self.agent.step(message)
        
        # 提取代码
        fixed_code = extract_code(response.msg.content)
        if not fixed_code:
            fixed_code = response.msg.content
            
        logger.info(f"代码修复完成: {len(fixed_code)} 字符")
        
        return fixed_code 