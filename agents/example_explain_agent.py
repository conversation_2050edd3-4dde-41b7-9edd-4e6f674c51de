from dotenv import load_dotenv

load_dotenv()

import datetime
import json
import logging
import os
import re
import sys
from typing import Any, Dict, List

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.societies import RolePlaying
from camel.types import ModelPlatformType, TaskType
from camel.toolkits import FunctionTool

# 导入create_model函数
from utils.create_llm_model import create_model

# 仿真数据生成工具类
class SimulationToolkit:
    """仿真/模拟数据生成工具包"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_simulation_data(self, description: str, data_type: str = "numerical", 
                               sample_size: int = 100, output_format: str = "table") -> dict:
        """
        生成仿真/模拟数据
        
        Args:
            description (str): 数据描述，说明需要什么样的数据，如"梯度下降优化过程"、"分类数据集"等
            data_type (str): 数据类型，可选值：numerical（数值型）、categorical（分类型）、time_series（时间序列）等。默认为"numerical"
            sample_size (int): 样本大小，生成数据的行数，必须为正整数。默认为100
            output_format (str): 输出格式，可选值：table（仅表格）、plot（仅图表）、both（表格和图表）。默认为"table"
        
        Returns:
            dict: 包含代码、数据和可视化结果的字典，包含以下键：
                - status: 执行状态（success/error）
                - description: 数据描述
                - code: 生成的Python代码
                - data_summary: 数据摘要信息
                - data_sample: 数据样本
                - plot_path: 图表文件路径（如果生成）
                - message: 执行结果消息
        """
        try:
            import numpy as np
            import pandas as pd
            import matplotlib.pyplot as plt
            import matplotlib
            # seaborn是可选依赖
            try:
                import seaborn as sns
                sns_available = True
            except ImportError:
                sns_available = False
                self.logger.warning("seaborn未安装，将使用matplotlib进行可视化")
            
            from io import StringIO
            import base64
            from matplotlib.backends.backend_agg import FigureCanvasAgg
            import os
            
            # 设置中文字体支持
            try:
                # 尝试设置中文字体 - 使用macOS系统字体
                plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Hiragino Sans GB', 'Arial Unicode MS', 'Hiragino Sans', 'DejaVu Sans', 'Arial', 'sans-serif']
                plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
                # 设置字体大小
                plt.rcParams['font.size'] = 10
                plt.rcParams['axes.titlesize'] = 12
                plt.rcParams['axes.labelsize'] = 10
                plt.rcParams['xtick.labelsize'] = 9
                plt.rcParams['ytick.labelsize'] = 9
                plt.rcParams['legend.fontsize'] = 9
                self.logger.info("中文字体设置成功")
            except Exception as e:
                self.logger.warning(f"字体设置失败，将使用默认字体: {e}")
            
            self.logger.info(f"开始生成仿真数据: {description}")
            
            # 生成代码字符串
            code_lines = [
                "import numpy as np",
                "import pandas as pd", 
                "import matplotlib.pyplot as plt",
                "",
                f"# 仿真数据生成: {description}",
                f"np.random.seed(42)  # 设置随机种子确保可重现性",
                f"sample_size = {sample_size}",
                ""
            ]
            
            # 根据描述生成相应的数据
            if "梯度下降" in description or "优化" in description:
                # 梯度下降相关的仿真数据
                code_lines.extend([
                    "# 生成梯度下降优化过程数据",
                    "learning_rates = [0.01, 0.1, 0.5]",
                    "iterations = np.arange(0, 100)",
                    "results = {}",
                    "",
                    "# 模拟不同学习率下的损失函数变化",
                    "for lr in learning_rates:",
                    "    # 模拟损失函数: 初始损失随迭代次数指数衰减",
                    "    initial_loss = 10.0",
                    "    noise = np.random.normal(0, 0.1, len(iterations))",
                    "    loss = initial_loss * np.exp(-lr * iterations * 0.1) + noise",
                    "    loss = np.maximum(loss, 0.01)  # 确保损失不为负",
                    "    results[f'lr_{lr}'] = loss",
                    "",
                    "# 创建DataFrame",
                    "df = pd.DataFrame(results, index=iterations)",
                    "df.index.name = 'iteration'",
                ])
                
                # 执行代码生成数据
                exec_globals = {}
                exec('\n'.join(code_lines), exec_globals)
                df = exec_globals['df']
                
            elif "机器学习" in description or "分类" in description:
                # 机器学习分类数据
                code_lines.extend([
                    "# 生成二分类数据集",
                    "from sklearn.datasets import make_classification",
                    "X, y = make_classification(n_samples=sample_size, n_features=2, ",
                    "                          n_redundant=0, n_informative=2,",
                    "                          n_clusters_per_class=1, random_state=42)",
                    "",
                    "# 创建DataFrame",
                    "df = pd.DataFrame({",
                    "    'feature_1': X[:, 0],",
                    "    'feature_2': X[:, 1], ",
                    "    'target': y",
                    "})",
                ])
                
                exec_globals = {}
                exec('\n'.join(code_lines), exec_globals)
                df = exec_globals['df']
                
            else:
                # 通用数值数据
                code_lines.extend([
                    "# 生成通用数值数据",
                    "x = np.linspace(0, 10, sample_size)",
                    "y = 2 * x + 1 + np.random.normal(0, 1, sample_size)",
                    "",
                    "df = pd.DataFrame({'x': x, 'y': y})",
                ])
                
                exec_globals = {}
                exec('\n'.join(code_lines), exec_globals)
                df = exec_globals['df']
            
            # 生成数据摘要
            data_summary = {
                "shape": df.shape,
                "columns": list(df.columns),
                "head": df.head().to_dict(),
                "describe": df.describe().to_dict() if df.select_dtypes(include=[np.number]).shape[1] > 0 else {},
            }
            
            # 生成可视化
            plot_code = []
            plot_path = None
            
            if output_format in ["plot", "both"]:
                plt.style.use('default')
                fig, ax = plt.subplots(figsize=(10, 6))
                
                if "梯度下降" in description:
                    plot_code.extend([
                        "",
                        "# 绘制梯度下降过程",
                        "plt.figure(figsize=(10, 6))",
                        "for lr in learning_rates:",
                        "    plt.plot(iterations, df[f'lr_{lr}'], label=f'学习率 {lr}', linewidth=2)",
                        "plt.xlabel('迭代次数')",
                        "plt.ylabel('损失函数值')",
                        "plt.title('不同学习率下的梯度下降收敛过程')",
                        "plt.legend()",
                        "plt.grid(True, alpha=0.3)",
                        "plt.show()"
                    ])
                    
                    for i, lr in enumerate([0.01, 0.1, 0.5]):
                        if f'lr_{lr}' in df.columns:
                            ax.plot(df.index, df[f'lr_{lr}'], label=f'学习率 {lr}', linewidth=2)
                    ax.set_xlabel('迭代次数')
                    ax.set_ylabel('损失函数值')
                    ax.set_title('不同学习率下的梯度下降收敛过程')
                    ax.legend()
                    ax.grid(True, alpha=0.3)
                    
                elif "分类" in description and 'target' in df.columns:
                    plot_code.extend([
                        "",
                        "# 绘制分类数据散点图",
                        "plt.figure(figsize=(10, 6))",
                        "colors = ['red', 'blue']",
                        "for i, target in enumerate([0, 1]):",
                        "    mask = df['target'] == target",
                        "    plt.scatter(df[mask]['feature_1'], df[mask]['feature_2'], ",
                        "               c=colors[i], label=f'类别 {target}', alpha=0.7)",
                        "plt.xlabel('特征 1')",
                        "plt.ylabel('特征 2')",
                        "plt.title('二分类数据集可视化')",
                        "plt.legend()",
                        "plt.grid(True, alpha=0.3)",
                        "plt.show()"
                    ])
                    
                    colors = ['red', 'blue']
                    for i, target in enumerate([0, 1]):
                        mask = df['target'] == target
                        ax.scatter(df[mask]['feature_1'], df[mask]['feature_2'], 
                                 c=colors[i], label=f'类别 {target}', alpha=0.7)
                    ax.set_xlabel('特征 1')
                    ax.set_ylabel('特征 2')
                    ax.set_title('二分类数据集可视化')
                    ax.legend()
                    ax.grid(True, alpha=0.3)
                    
                else:
                    plot_code.extend([
                        "",
                        "# 绘制数据散点图",
                        "plt.figure(figsize=(10, 6))",
                        "plt.scatter(df['x'], df['y'], alpha=0.7)",
                        "plt.xlabel('X')",
                        "plt.ylabel('Y')",
                        "plt.title('数据分布图')",
                        "plt.grid(True, alpha=0.3)",
                        "plt.show()"
                    ])
                    
                    ax.scatter(df['x'], df['y'], alpha=0.7)
                    ax.set_xlabel('X')
                    ax.set_ylabel('Y')
                    ax.set_title('数据分布图')
                    ax.grid(True, alpha=0.3)
                
                # 保存图片
                os.makedirs("output/simulations", exist_ok=True)
                plot_path = f"output/simulations/simulation_{hash(description) % 10000}.png"
                plt.tight_layout()
                plt.savefig(plot_path, dpi=150, bbox_inches='tight')
                plt.close()
            
            # 组合完整代码
            full_code = '\n'.join(code_lines + plot_code)
            
            # 添加图片链接信息到代码中
            if plot_path:
                full_code += f"\n\n# 生成的图表文件路径: {plot_path}\n"
                full_code += f"# 图表已保存，可以查看可视化结果\n"
            
            result = {
                "status": "success",
                "description": description,
                "code": full_code,
                "data_summary": data_summary,
                "data_sample": df.head(10).to_dict('records'),
                "plot_path": plot_path,
                "plot_markdown": f"![仿真结果图表]({plot_path})" if plot_path else None,
                "message": f"成功生成{df.shape[0]}行{df.shape[1]}列的仿真数据" + (f"，图表已保存到 {plot_path}" if plot_path else "")
            }
            
            self.logger.info(f"仿真数据生成完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"生成仿真数据失败: {str(e)}")
            return {
                "status": "error",
                "message": f"生成仿真数据失败: {str(e)}",
                "code": "# 代码执行失败",
                "data_summary": {},
                "data_sample": [],
                "plot_path": None
            }

# 设置日志级别为INFO，并配置日志格式
log_file = f"output/example_explain_agent_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(),  # 同时输出到控制台
    ],
)

# 获取logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义综合例子生成专家角色提示
EXAMPLE_GENERATOR_PROMPT = """
你是一位顶级的例子生成专家，专门为复杂概念创造高质量、细节丰富、易懂的具体例子。

## 核心能力
1. **概念提取与分析**：准确识别核心概念、原理或题目，分析其关键要素和步骤
2. **例子设计**：创造具体、量化、准确的例子来解释抽象概念
3. **数学公式处理**：使用LaTeX格式正确表示数学公式和方程
4. **数据仿真**：当需要数据支撑时，可以调用仿真工具生成相关数据和可视化

## 质量标准
- **数据量化**：所有数据必须具体、准确，有明确的数值
- **步骤详细**：每个步骤都要清晰说明，不遗漏关键环节
- **逻辑清晰**：整个过程逻辑连贯，易于理解
- **结果明确**：最终结果要明确，能够验证
- **原理揭示**：深入揭示概念的本质原理和机制
- **易于记忆**：例子要生动具体，便于理解和记忆

## 数学公式格式要求
- 行内公式使用：$公式内容$
- 独立公式使用：$$公式内容$$
- 复杂公式要分行显示，使用适当的LaTeX命令
- 例如：
  - 梯度：$\\nabla f(x) = \\frac{\\partial f}{\\partial x}$
  - 损失函数：$$J(\\theta) = \\frac{1}{2m}\\sum_{i=1}^{m}(h_\\theta(x^{(i)}) - y^{(i)})^2$$
  - 参数更新：$$\\theta := \\theta - \\alpha \\nabla J(\\theta)$$

## 仿真数据工具使用
当例子需要数据支撑时，**必须**调用generate_simulation_data工具：
- 描述需要什么样的数据（如"梯度下降优化过程"、"分类数据集"、"抛物运动轨迹"等）
- 工具会生成相应的代码、数据和可视化图表
- 将生成的代码和结果整合到例子中
- **重要：如果工具生成了图表，必须在输出中包含图片链接**

**重要：当你在例子中提到"仿真数据"、"模拟"、"数据展示"时，必须实际调用工具！**

调用示例：
```
我需要生成抛物运动的仿真数据来支撑这个例子。
[调用generate_simulation_data工具，参数：description="抛物运动轨迹", data_type="numerical", sample_size=100, output_format="both"]
```

**工具调用后的处理：**
1. 将工具返回的代码包含到例子中
2. 如果生成了图表，使用以下格式添加图片链接：
   ```markdown
   ![仿真结果图表](图片路径)
   ```
3. 解释仿真结果和数据含义

## 输出格式
请按照以下markdown格式输出：

```markdown
# 核心概念识别

## 概念名称
[提取的核心概念名称]

## 概念描述  
[概念的准确定义和说明]

## 关键要素
- 要素1：[说明]
- 要素2：[说明]
- ...

## 核心步骤
1. 步骤1：[详细说明]
2. 步骤2：[详细说明]
3. ...

## 理解难点
- 难点1：[说明为什么难理解]
- 难点2：[说明为什么难理解]
- ...

# 具体例子

## 例子标题
[一个吸引人且准确的标题]

## 背景设定
[设置一个具体的应用场景]

## 详细步骤解释

### 步骤1：[步骤名称]
[详细说明，包含具体数值和计算]

如果涉及数学公式，使用LaTeX格式：
$$公式内容$$

如果需要数据支撑，调用仿真工具并展示结果。

### 步骤2：[步骤名称]
[继续详细说明...]

### 步骤N：[最终步骤]
[最终结果和验证]

## 核心原理揭示
[深入解释为什么这样做，揭示本质机制]

## 关键要点总结
- 要点1：[重要发现或规律]
- 要点2：[关键技巧或注意事项]
- 要点3：[实际应用价值]
```

请确保例子具体、准确、易懂，真正帮助读者理解核心概念的本质。
"""

# 定义质量评审专家角色提示
QUALITY_REVIEWER_PROMPT = """
你是一位严格的质量评审专家，专门评估例子的质量，确保例子准确、完整、易懂、高质量。

## 评审标准
1. **准确性**：数据、计算、逻辑是否准确无误
2. **完整性**：步骤是否完整，逻辑链条是否连贯，是否遗漏关键环节
3. **具体性**：是否足够具体和量化，细节是否丰富
4. **易懂性**：是否清晰易懂，逻辑是否正确
5. **揭示性**：是否真正揭示了核心概念的本质原理
6. **质量性**：整体质量是否达到高标准

## 评审要求
- 严格检查每个数据的合理性和准确性
- 验证每个步骤的逻辑性和必要性
- 确认例子的教学效果和理解价值
- 检查是否遗漏了重要的核心步骤或原理
- 评估细节的丰富程度和质量

## 输出格式要求
请按以下markdown格式输出评审结果：

```markdown
# 质量评审报告

## 准确性评估
**评分**：[优秀/良好/需要改进]
**评价**：[数据和计算是否准确，有什么错误]

## 完整性评估
**评分**：[优秀/良好/需要改进]
**评价**：[逻辑是否完整，缺少什么环节]

## 具体性评估
**评分**：[优秀/良好/需要改进]
**评价**：[是否足够具体和量化，细节是否丰富]

## 易懂性评估
**评分**：[优秀/良好/需要改进]
**评价**：[是否清晰易懂，逻辑是否正确]

## 揭示性评估
**评分**：[优秀/良好/需要改进]
**评价**：[是否揭示了核心原理，效果如何]

## 质量性评估
**评分**：[优秀/良好/需要改进]
**评价**：[整体质量是否达到高标准]

## 总体评分
**评分**：[优秀/良好/需要改进]

## 具体改进建议
[详细的改进建议，包括：]
- 需要修正的数据或计算
- 需要补充的步骤或逻辑
- 需要增加的细节
- 需要强化的原理解释
- 其他改进建议

## 是否通过质量检查
**结果**：[通过/需要修改]
```

请严格按照标准进行评审，确保例子达到高质量要求。
"""

class ExampleExplainAgent:
    """
    例子解释代理
    
    职责：
    1. 从输入的文本或markdown文件中提取核心概念/原理/题目
    2. 分析核心知识的要素/步骤
    3. 生成非常具体的、贴近生活的例子来解释核心知识
    4. 使用roleplay方式确保例子质量
    5. 输出高质量的例子解释
    """

    class Config:
        """例子解释代理配置子模块"""

        def __init__(self, config_dict=None):
            """初始化配置"""
            if not config_dict:
                config_dict = {}

            # 模型配置
            self.model = {
                "type": config_dict.get("model", {}).get("type", "google/gemini-2.5-flash-preview-05-20"),
                "temperature": config_dict.get("model", {}).get("temperature", 0.7),
                "api": config_dict.get("model", {}).get("api", {}),
            }

            # 文件配置
            self.files = {
                "output_dir": config_dict.get("files", {}).get("output_dir", "output"),
            }

            # 例子解释代理特定配置
            example_config = config_dict.get("example_explain", {})
            self.max_rounds = example_config.get("max_rounds", 2)
            self.output_dir = example_config.get("output_dir", "output")
            self.quality_threshold = example_config.get("quality_threshold", "良好")  # 质量阈值：优秀/良好/需要改进

    def __init__(self, config_path="config/config.yaml"):
        """初始化例子解释代理"""
        # 加载配置
        config_dict = self._load_yaml_config(config_path)

        # 初始化配置子模块
        self.config = self.Config(config_dict)

        # 初始化模型
        self.model = self._create_model()

        # 初始化仿真工具
        self._simulation_toolkit = SimulationToolkit()
        
        # 创建工具函数
        simulation_tool = FunctionTool(self._simulation_toolkit.generate_simulation_data)
        self._tools = [simulation_tool]

        logger.info("例子解释代理初始化完成")

    def _load_yaml_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path, encoding="utf-8") as file:
                config = yaml.safe_load(file)
            logger.info("从 %s 加载配置", config_path)
            return config
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            return {}

    def _create_model(self):
        """创建模型实例"""
        # 直接使用utils中的create_model函数
        return create_model(config_file="config/config.yaml")

    def _create_tool_agent(self, system_prompt):
        """创建一个用于工具调用的代理"""
        return ChatAgent(system_message=system_prompt, model=self.model, tools=self._tools)

    def read_material(self, material_file_path: str) -> str:
        """
        读取原始素材文件

        参数:
        - material_file_path: 素材文件路径

        返回:
        - str: 素材内容
        """
        try:
            with open(material_file_path, encoding="utf-8") as f:
                content = f.read()
            logger.info(f"成功读取素材文件: {material_file_path}, 长度: {len(content)} 字符")
            return content
        except Exception as e:
            logger.error(f"读取素材文件失败: {str(e)}")
            raise

    def generate_detailed_example(self, content: str) -> str:
        """
        直接从内容生成高质量的详细例子

        参数:
        - content: 输入内容

        返回:
        - str: 生成的例子内容
        """
        logger.info("开始生成高质量详细例子")

        try:
            # 创建例子生成专家（支持工具调用）
            example_agent = self._create_tool_agent(EXAMPLE_GENERATOR_PROMPT)

            # 构建用户消息
            user_message = f"""
            请从以下内容中提取核心概念，并生成高质量、细节丰富、易懂的具体例子来解释：

            内容：
            ```
            {content}
            ```

            要求：
            1. 首先识别最核心的概念、原理或题目
            2. 分析其关键要素和步骤
            3. 生成一个高质量、细节丰富的具体例子
            4. 确保例子量化、准确、逻辑正确
            5. 不丢失核心步骤和原理
            6. 揭露本质原理，清晰易懂，易于理解和记忆
            7. 如果涉及数学公式，请使用LaTeX格式（$公式$ 或 $$公式$$）
            8. 如果需要数据支撑，可以调用generate_simulation_data工具生成仿真数据

            请按照指定的markdown格式输出完整的例子解释。
            """

            # 获取回复
            response = example_agent.step(user_message)
            logger.info(f"获取到例子生成响应，长度: {len(response.msg.content)} 字符")

            # 提取Markdown内容
            example_content = self._extract_markdown(response.msg.content)

            if not example_content:
                logger.warning("无法提取有效的Markdown，使用完整响应")
                example_content = response.msg.content

            logger.info("高质量详细例子生成完成")
            return example_content

        except Exception as e:
            logger.error(f"生成高质量详细例子失败: {str(e)}")
            raise

    def refine_example_with_roleplay(self, example_content: str, original_content: str, max_rounds: int = None) -> str:
        """
        通过角色扮演迭代优化例子质量

        参数:
        - example_content: 初始例子内容
        - original_content: 原始内容
        - max_rounds: 最大迭代轮数

        返回:
        - str: 优化后的例子内容
        """
        if max_rounds is None:
            max_rounds = self.config.max_rounds

        logger.info(f"开始通过角色扮演优化例子质量，最大轮数: {max_rounds}")

        try:
            # 准备任务内容
            task_content = f"""
            请优化以下例子的质量，确保其准确、完整、易懂、高质量。

            当前例子内容：
            ```markdown
            {example_content}
            ```

            原始内容参考：
            ```
            {original_content[:2000]}...
            ```

            请通过例子生成专家和质量评审专家的对话，共同优化这个例子，确保它：
            1. 数据和计算准确无误
            2. 步骤完整，逻辑链条连贯
            3. 细节丰富，足够具体和量化
            4. 清晰易懂，逻辑正确
            5. 真正揭示核心概念的本质原理
            6. 整体质量达到高标准

            重要格式要求：
            当例子生成专家提出修改后的最终例子内容时，必须使用以下格式标记：

            ===开始：优化后的例子===
            [完整的Markdown内容]
            ===结束：优化后的例子===

            这将确保我们准确识别出最终例子内容。
            """

            # 将角色提示合并到任务中
            complete_task_content = f"""
            {task_content}

            ## 角色定位

            例子生成专家(Example Generator)：
            {EXAMPLE_GENERATOR_PROMPT}

            质量评审专家(Quality Reviewer)：
            {QUALITY_REVIEWER_PROMPT}

            ## 对话流程安排
            1. 质量评审专家首先评估当前例子的质量，指出需要改进的地方
            2. 例子生成专家根据反馈修改优化例子内容
            3. 质量评审专家再次评估修改后的内容
            4. 最后一轮对话时，例子生成专家必须提供最终版本的完整例子，使用格式标记：

            ===开始：优化后的例子===
            [完整的Markdown内容]
            ===结束：优化后的例子===

            请严格遵循这个格式规范，确保最终输出是完整的高质量例子。
            """

            # 设置角色扮演
            role_playing = RolePlaying(
                # 设置例子生成专家为助手角色
                assistant_role_name="Example Generator",
                assistant_agent_kwargs={"model": self.model, "tools": self._tools},
                # 设置质量评审专家为用户角色
                user_role_name="Quality Reviewer",
                user_agent_kwargs={"model": self.model},
                # 任务参数
                task_prompt=complete_task_content,
                task_type=TaskType.AI_SOCIETY,
                with_task_specify=False,  # 禁用task_specify避免报错
                # 附加配置
                output_language="chinese",
            )

            # 开始对话
            logger.info("开始角色对话")
            messages = []

            # 初始化对话
            chat_history = role_playing.init_chat()
            messages.append(chat_history)

            # 收集所有可能的例子版本，包括原始版本
            potential_examples = [example_content]

            # 最后一轮提前特别提醒
            is_final_reminder_sent = False

            # 进行多轮对话
            for round_num in range(max_rounds * 2):  # 每轮包含两步对话
                logger.info(f"对话步骤 {round_num + 1}")

                # 在最后一轮对话前，提醒生成专家使用正确的格式标记
                if round_num == max_rounds * 2 - 2 and not is_final_reminder_sent:
                    # 这是最后一轮对话的前一步
                    remind_message = """
                    这是最后一轮对话。请记住，您必须在回复中提供最终版本的完整例子，
                    并使用以下格式标记：

                    ===开始：优化后的例子===
                    [完整的Markdown内容]
                    ===结束：优化后的例子===

                    这样我才能准确识别最终例子内容。
                    """
                    chat_history.content += "\n\n" + remind_message
                    is_final_reminder_sent = True

                try:
                    # 进行对话步骤
                    assistant_response, user_response = role_playing.step(chat_history)

                    # 从响应中获取消息
                    assistant_message = assistant_response.msg
                    user_message = user_response.msg

                    # 添加到历史记录
                    chat_history = assistant_message
                    messages.append(assistant_message)
                    messages.append(user_message)

                    # 处理助手消息中的内容
                    if hasattr(assistant_message, "content") and assistant_message.role_name == "Example Generator":
                        content = assistant_message.content

                        # 首先检查是否有明确标记的例子段落
                        if "===开始：优化后的例子===" in content and "===结束：优化后的例子===" in content:
                            parts = content.split("===开始：优化后的例子===", 1)
                            if len(parts) > 1:
                                marked_content = parts[1].split("===结束：优化后的例子===", 1)[0].strip()
                                logger.info(f"步骤 {round_num + 1} 从明确标记中提取到例子内容")
                                potential_examples.append(marked_content)

                                # 如果是最后一轮并使用了正确的标记，可以直接结束对话
                                if round_num >= max_rounds * 2 - 2:
                                    logger.info("获取到最终标记的例子内容，结束对话")
                                    break

                        # 尝试提取Markdown文本（作为备选）
                        extracted_markdown = self._extract_markdown(content)
                        if extracted_markdown and len(extracted_markdown) > 100:
                            logger.info(f"步骤 {round_num + 1} 尝试提取Markdown例子")
                            # 检查提取的内容是否为有效Markdown（有标题结构）
                            if "#" in extracted_markdown[:50] or re.search(r"^#\s+", extracted_markdown, re.MULTILINE):
                                logger.info(f"步骤 {round_num + 1} 提取到可能的Markdown例子")
                                potential_examples.append(extracted_markdown)

                    # 如果已经完成了足够的轮次
                    if round_num >= max_rounds * 2 - 1:
                        logger.info("对话已完成足够的轮次")
                        break

                except Exception as e:
                    logger.error(f"对话步骤出错: {str(e)}")
                    break

            logger.info("角色对话完成，例子优化结束")

            # 筛选并选择最佳例子内容
            valid_examples = []

            # 按质量筛选例子（从最后一轮向前）
            for example in reversed(potential_examples):
                # 跳过太短的内容或明显非Markdown格式的内容
                if len(example) < 100 or (
                    not example.startswith("#") and not re.search(r"^#\s+", example, re.MULTILINE)
                ):
                    continue

                # 清理并格式化Markdown
                example = example.strip()
                # 确保以标题开始
                if not example.startswith("#"):
                    title_match = re.search(r"^#\s+", example, re.MULTILINE)
                    if title_match:
                        start_idx = title_match.start()
                        example = example[start_idx:]

                valid_examples.append(example)

            # 如果没有找到任何有效例子，回退到原始例子
            if not valid_examples:
                logger.warning("未找到任何有效的优化例子，回退到原始例子")
                return example_content

            # 选择最长的有效例子（通常是最完整的）
            final_example = max(valid_examples, key=len)
            logger.info(f"选择了长度为 {len(final_example)} 字符的最终例子")

            return final_example

        except Exception as e:
            import traceback

            logger.error(f"通过角色扮演优化例子失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 出错时返回原始例子
            return example_content

    def save_example(self, content: str, output_file: str = None) -> str:
        """
        保存例子内容到文件

        参数:
        - content: 例子内容
        - output_file: 输出文件路径

        返回:
        - str: 保存的文件路径
        """
        if output_file is None:
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"{self.config.output_dir}/example_explain_{timestamp}.md"

        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        os.makedirs(output_dir, exist_ok=True)

        # 写入文件
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(content)
            logger.info(f"例子内容已保存到 {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存例子内容失败: {str(e)}")
            # 尝试使用备用路径
            backup_file = f"output/example_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            try:
                with open(backup_file, "w", encoding="utf-8") as f:
                    f.write(content)
                logger.info(f"例子内容已保存到备用文件 {backup_file}")
                return backup_file
            except Exception as e2:
                logger.error(f"保存到备用文件也失败: {str(e2)}")
                return ""

    def run(self, material_file_path: str = None, content: str = None, output_file: str = None, max_rounds: int = None) -> dict[str, Any]:
        """
        运行例子解释的完整流程

        参数:
        - material_file_path: 原始素材文件路径（可选）
        - content: 直接输入的内容（可选）
        - output_file: 输出文件路径
        - max_rounds: 最大迭代轮数

        返回:
        - Dict: 包含处理结果的字典
        """
        result = {}

        try:
            # 1. 读取或获取内容
            if material_file_path:
                logger.info(f"开始处理素材文件: {material_file_path}")
                original_content = self.read_material(material_file_path)
            elif content:
                logger.info("开始处理直接输入的内容")
                original_content = content
            else:
                raise ValueError("必须提供material_file_path或content参数")

            result["original_length"] = len(original_content)

            # 2. 生成高质量详细例子
            logger.info("生成高质量详细例子")
            initial_example = self.generate_detailed_example(original_content)
            result["initial_example_length"] = len(initial_example)

            # 3. 通过角色对话优化例子质量
            logger.info("开始通过角色对话优化例子质量")
            final_example = self.refine_example_with_roleplay(initial_example, original_content, max_rounds)
            result["final_example_length"] = len(final_example)

            # 4. 保存结果
            logger.info("保存优化后的例子")
            saved_file = self.save_example(final_example, output_file)
            result["saved_file"] = saved_file
            result["success"] = True
            logger.info("例子解释处理完成")
            return result

        except Exception as e:
            logger.error(f"例子解释处理出错: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            result["error"] = str(e)
            result["success"] = False
            return result

    def _extract_markdown(self, text: str) -> str:
        """从文本中提取Markdown内容"""
        # 首先检查是否有明确标记的Markdown段落
        markdown_markers = [
            "===开始：优化后的例子===",
            "```markdown",
            "# 核心概念识别",
            "## 核心概念识别",
            "# 核心概念例子解释",
        ]

        # 尝试通过明确的标记寻找Markdown起始位置
        start_index = -1
        for marker in markdown_markers:
            if marker in text:
                possible_start = text.find(marker)
                if start_index == -1 or possible_start < start_index:
                    start_index = possible_start

        # 如果找到了标记，从标记处开始提取
        if start_index != -1:
            # 从标记处开始，但跳过标记本身（除非是Markdown标题）
            if text[start_index:start_index + 2] == "# ":
                extracted_text = text[start_index:]
            elif text[start_index:start_index + 3] == "## ":
                extracted_text = text[start_index:]
            else:
                # 找到标记后的第一个换行
                next_line = text.find("\n", start_index)
                if next_line != -1:
                    extracted_text = text[next_line + 1:]
                else:
                    extracted_text = text[start_index:]

            # 寻找终止标记
            end_markers = ["===结束：优化后的例子===", "```", "---", "以上是生成的例子"]
            for marker in end_markers:
                if marker in extracted_text:
                    extracted_text = extracted_text.split(marker)[0]

            return extracted_text.strip()

        # 如果没有明确的标记，尝试通过Markdown代码块提取
        markdown_pattern = r"```(?:markdown)?\s*([\s\S]*?)```"
        matches = re.findall(markdown_pattern, text, re.DOTALL)

        if matches:
            # 选择最长的匹配结果
            longest_match = max(matches, key=len).strip()
            # 确保这是真正的Markdown内容而不是代码
            if "#" in longest_match and len(longest_match) > 200:
                return longest_match

        # 如果没有明确的Markdown代码块，但文本看起来已经是Markdown格式
        if re.search(r"^#\s+", text, re.MULTILINE):
            # 尝试找到第一个标题
            match = re.search(r"^#\s+", text, re.MULTILINE)
            if match:
                start = match.start()
                extracted = text[start:].strip()
                
                # 检查是否包含完整的结构（不只是代码）
                if "##" in extracted and len(extracted) > 500:
                    return extracted

        # 如果以上都失败，检查是否整个文本就是Markdown格式
        if text.strip().startswith("#") and "##" in text:
            return text.strip()

        # 无法提取有效的Markdown，返回空字符串让调用者使用原始文本
        logger.warning("无法提取有效的Markdown内容")
        return ""


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="例子解释代理 - 生成高质量的概念解释例子")
    parser.add_argument("--config", type=str, default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--material", type=str, help="原始素材文件路径")
    parser.add_argument("--content", type=str, help="直接输入的内容")
    parser.add_argument("--output", type=str, help="输出文件路径")
    parser.add_argument("--max-rounds", type=int, default=1, help="最大迭代轮数")

    args = parser.parse_args()

    # 检查输入参数
    if not args.material and not args.content:
        print("错误：必须提供 --material 或 --content 参数")
        print("使用示例：")
        print("  python agents/example_explain_agent.py --material file.md")
        print("  python agents/example_explain_agent.py --content '你的内容'")
        return

    try:
        # 创建代理
        print("🚀 正在初始化例子解释代理...")
        agent = ExampleExplainAgent(config_path=args.config)

        # 运行处理
        print("⚡ 开始处理，请稍候...")
        result = agent.run(
            material_file_path=args.material,
            content=args.content,
            output_file=args.output,
            max_rounds=args.max_rounds
        )

        # 输出结果
        if result.get("success"):
            print(f"✅ 例子解释处理成功！")
            print(f"📄 原始内容长度: {result.get('original_length', 0)} 字符")
            print(f"📝 初始例子长度: {result.get('initial_example_length', 0)} 字符")
            print(f"🎯 最终例子长度: {result.get('final_example_length', 0)} 字符")
            print(f"💾 保存文件: {result.get('saved_file', '未保存')}")
        else:
            print(f"❌ 例子解释处理失败: {result.get('error', '未知错误')}")

    except Exception as e:
        print(f"❌ 程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 